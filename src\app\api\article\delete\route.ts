import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

// POST - Update article
export async function DELETE(req: NextRequest) {
    // const user = await verifyToken(req);
    // if (!user) {
    //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    try {
        const requestData = await req.json();
        const id = requestData.id?.trim();

        if (!id) {
            return NextResponse.json(
                { error: "Article ID is required" },
                { status: 400 }
            );
        }

        // Validate UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(id)) {
            return NextResponse.json(
                { error: "Invalid article ID format" },
                { status: 400 }
            );
        }

        // Check if article exists
        const existingArticle = await prisma.articleDetails.findUnique({
            where: { Id: id }
        });

        if (!existingArticle) {
            return NextResponse.json(
                { error: "Article not found" },
                { status: 404 }
            );
        }
        const updateData: any = {};
        updateData.IsDeleted = true;
        updateData.CustomChannal = null;
        updateData.StyleIdLm = null;
        updateData.StyleIdDm = null;
        
        const deleteArticle = await prisma.articleDetails.update({
            where: { Id: id },
            data: updateData
        });

        return NextResponse.json({
            success: true,
            message: "Article delete successfully",
        }, { status: 200 });

    } catch (error) {
        console.error("Error delete article:", error);
        return NextResponse.json(
            {
                error: "Failed to delete article",
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    } 
}