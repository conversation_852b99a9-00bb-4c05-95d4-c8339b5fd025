"use client";

import React, { useCallback } from 'react';
import DataTableWithActions from './DataTableWithActions';
import { usePaginatedData } from '@/hooks/usePaginatedData';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string;
}

interface ActionButton {
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'green' | 'danger' | 'dark';
  className?: string;
}

interface SmartDataTableProps {
  // API props
  endpoint: string;
  additionalFilters?: Record<string, any>;
  
  // Table props
  columns: Column[];
  title?: string;
  
  // Action props
  actionButtons?: ActionButton[];
  showActions?: boolean;
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  onView?: (row: any) => void;
  onClone?: (row: any) => void;
  onBlock?: (row: any) => void;
  onReset?: (row: any) => void;
  
  // Custom action handlers
  handleSubDomainClick?: (row: any) => void;
  handleStyleIDClick?: (row: any) => void;
  handleUserCountClick?: (row: any) => void;
  handleViewCampaignsClick?: (row: any) => void;
  handleAddCampaignClick?: (row: any) => void;
  onInPersonLogin?: (row: any) => void;
  
  // Search props
  searchPlaceholder?: string;
  showSearch?: boolean;
  
  // Pagination props
  initialRowsPerPage?: number;
  showPagination?: boolean;
  
  // Layout props
  className?: string;
  containerClassName?: string;
  
  // Other props
  totals?: any;
  notShowAction?: boolean;
  autoFetch?: boolean;
}

const SmartDataTable: React.FC<SmartDataTableProps> = ({
  // API props
  endpoint,
  additionalFilters = {},
  
  // Table props
  columns,
  title,
  
  // Action props
  actionButtons = [],
  showActions = true,
  onEdit,
  onDelete,
  onView,
  onClone,
  onBlock,
  onReset,
  
  // Custom action handlers
  handleSubDomainClick,
  handleStyleIDClick,
  handleUserCountClick,
  handleViewCampaignsClick,
  handleAddCampaignClick,
  onInPersonLogin,
  
  // Search props
  searchPlaceholder = 'Search...',
  showSearch = true,
  
  // Pagination props
  initialRowsPerPage = 10,
  showPagination = true,
  
  // Layout props
  className = '',
  containerClassName = '',
  
  // Other props
  totals,
  notShowAction = false,
  autoFetch = true,
}) => {
  // Use the paginated data hook
  const {
    data,
    loading,
    pagination,
    setPage,
    setRowsPerPage,
    setSearchTerm,
    handleSort,
    refetch,
  } = usePaginatedData(endpoint, {
    initialRowsPerPage,
    autoFetch,
    additionalFilters,
  });

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, [setPage]);

  // Handle rows per page change
  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
  }, [setRowsPerPage]);

  // Handle search change
  const handleSearchChange = useCallback((searchTerm: string) => {
    setSearchTerm(searchTerm);
  }, [setSearchTerm]);

  // Handle sort request
  const handleSortRequest = useCallback((property: string) => {
    handleSort(property);
  }, [handleSort]);

  // Refresh data function that can be used by parent components
  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // // Expose refresh function to parent via ref if needed
  // React.useImperativeHandle(React.forwardRef(() => null), () => ({
  //   refresh,
  // }));

  return (
    <DataTableWithActions
      // Data props
      data={data}
      columns={columns}
      loading={loading}
      totalCount={pagination.totalCount}
      
      // Pagination props
      page={pagination.page}
      rowsPerPage={pagination.rowsPerPage}
      onPageChange={handlePageChange}
      onRowsPerPageChange={handleRowsPerPageChange}
      showPagination={showPagination}
      
      // Sorting props
      order={pagination.order}
      orderBy={pagination.orderBy}
      onRequestSort={handleSortRequest}
      
      // Search props
      searchTerm={pagination.searchTerm}
      onSearchChange={handleSearchChange}
      searchPlaceholder={searchPlaceholder}
      showSearch={showSearch}
      
      // Action props
      actionButtons={actionButtons}
      showActions={showActions}
      onEdit={onEdit}
      onDelete={onDelete}
      onView={onView}
      onClone={onClone}
      onBlock={onBlock}
      onReset={onReset}
      
      // Custom action handlers
      handleSubDomainClick={handleSubDomainClick}
      handleStyleIDClick={handleStyleIDClick}
      handleUserCountClick={handleUserCountClick}
      handleViewCampaignsClick={handleViewCampaignsClick}
      handleAddCampaignClick={handleAddCampaignClick}
      onInPersonLogin={onInPersonLogin}
      
      // Layout props
      title={title}
      className={className}
      containerClassName={containerClassName}
      
      // Other props
      totals={totals}
      notShowAction={notShowAction}
    />
  );
};

export default SmartDataTable;
