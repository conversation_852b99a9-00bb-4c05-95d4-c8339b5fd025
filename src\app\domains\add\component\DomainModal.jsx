"use client";

import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { CloseIcon } from "@/assets/icons";
import apiClient from "@/utils/axiosInstance";

const DomainModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isEdit,
  channels,
}) => {
  const [formData, setFormData] = useState({
    Name: "",
    ShowUrlName: "",
    Prefix: "",
    ChannelId: "",
    CookieMinutes: "",
    StyleIdDm: "",
    StyleIdLm: "",
    HeadTagScript: "",
    HeadTagScriptLandingPage: "",
    HeadTagScriptSearchPage: "",
    GId: "",
    AWId: "", // Commented out
    SendTo: "", // Commented out
    AdsProviderId: "", // New field for Ads Provider
    TrackingKey: "", // New field for Tracking Key
    WideLogo: null,
    SquareLogo: null,
    ContactEmail: "",
    Address: "",
    AdsTxtContent: "",
  });

  const [adsProviders, setAdsProviders] = useState({
    providersList: [],
    isLoading: false,
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (isEdit && initialData) {
      setFormData({
        Name: initialData.Name || "",
        ShowUrlName: initialData.ShowUrlName || "",
        Prefix: initialData.Prefix || "",
        ChannelId: initialData.ChannelId || "",
        CookieMinutes: initialData.CookieMinutes?.toString() || "",
        StyleIdDm: initialData.StyleIdDm?.toString() || "",
        StyleIdLm: initialData.StyleIdLm?.toString() || "",
        HeadTagScript: initialData.HeadTagScript || "",
        HeadTagScriptLandingPage: initialData.HeadTagScriptLandingPage || "",
        HeadTagScriptSearchPage: initialData.HeadTagScriptSearchPage || "",
        GId: initialData.GId || "",
        AWId: initialData.AWId || "", // Commented out
        SendTo: initialData.SendTo || "", // Commented out
        AdsProviderId: initialData.AdsProviderId || "", // New field
        TrackingKey: initialData.TrackingKey || "", // New field
        WideLogo: null,
        SquareLogo: null,
        ContactEmail: initialData.ContactEmail || "",
        Address: initialData.Address || "",
        AdsTxtContent: initialData.AdsTxtContent || "",
      });
    } else {
      setFormData({
        Name: "",
        ShowUrlName: "",
        Prefix: "",
        ChannelId: "",
        CookieMinutes: "",
        StyleIdDm: "",
        StyleIdLm: "",
        HeadTagScript: "",
        HeadTagScriptLandingPage: "",
        HeadTagScriptSearchPage: "",
        GId: "",
        AWId: "", // Commented out
        SendTo: "", // Commented out
        AdsProviderId: "", // New field
        TrackingKey: "", // New field
        WideLogo: null,
        SquareLogo: null,
        ContactEmail: "",
        Address: "",
        AdsTxtContent: "",
      });
    }
  }, [initialData, isEdit]);

  // Fetch ads providers when modal opens
  useEffect(() => {
    const fetchAdsProviders = async () => {
      if (isOpen) {
        try {
          setAdsProviders((prev) => ({ ...prev, isLoading: true }));
          const response = await apiClient.get("/AdsProvider/GetDropDown");
          
          if (response.data.success) {
            setAdsProviders({
              providersList: response.data.data,
              isLoading: false,
            });
          } else {
            console.error("Failed to fetch ads providers:", response.data.error);
            setAdsProviders((prev) => ({ ...prev, isLoading: false }));
          }
        } catch (error) {
          console.error("Error fetching ads providers:", error);
          setAdsProviders((prev) => ({ ...prev, isLoading: false }));
        }
      }
    };

    fetchAdsProviders();
  }, [isOpen]);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (files) {
      setFormData((prev) => ({ ...prev, [name]: files[0] }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleChannelSelect = (item) => {
    setFormData((prev) => ({ ...prev, ChannelId: item?.Id ? item.Id : "" }));
  };

  const handleAdsProviderSelect = async (item) => {
    if (item?.Id) {
      setFormData((prev) => ({ 
        ...prev, 
        AdsProviderId: item.Id,
        TrackingKey: "" // Clear tracking key when provider changes
      }));

      // Fetch the ads provider details to get the description (tracking key)
      try {
        const response = await apiClient.get(`/AdsProvider/GetById?Id=${item.Id}`);
        if (response.data.success) {
          setFormData((prev) => ({ 
            ...prev, 
            TrackingKey: response.data.data?.TrackingKey || "" 
          }));
        }
      } catch (error) {
        console.error("Error fetching ads provider details:", error);
      }
    } else {
      setFormData((prev) => ({ 
        ...prev, 
        AdsProviderId: "",
        TrackingKey: "" 
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.Name.trim()) newErrors.Name = "Domain name is required";
    if (!formData.ShowUrlName.trim()) newErrors.ShowUrlName = "URL is required";
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const submitData = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        submitData.append(key, value);
      }
    });
    onSubmit(submitData);
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={(event, reason) => {
        if (reason === "backdropClick") return;
        setErrors({}); 
        setFormData({})
        onClose();
      }}
      fullWidth
      maxWidth="md"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {isEdit ? "Edit Domain" : "Add Domain"}
        </span>
        <IconButton
          aria-label="close"
          onClick={() => {
            setErrors({}); 
            setFormData({})
            onClose();
          }}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form style={{ display: "flex", flexDirection: "column", gap: 16 }} encType="multipart/form-data">
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: 20,
            }}
          >
            <InputGroup
              label="Domain Name"
              type="text"
              name="Name"
              value={formData.Name}
              handleChange={handleChange}
              placeholder="Enter Domain Name"
              required
              error={errors.Name}
            />

            <InputGroup
              label="URL"
              type="text"
              name="ShowUrlName"
              value={formData.ShowUrlName}
              handleChange={handleChange}
              placeholder="Enter URL-friendly name"
              required
              error={errors.ShowUrlName}
            />

            <InputGroup
              label="Prefix (max 5 characters)"
              type="text"
              name="Prefix"
              value={formData.Prefix}
              handleChange={handleChange}
              placeholder="Enter prefix (e.g., fb, tw)"
              maxLength={5}
            />

            <SearchableDropdown
              label="Channel"
              options={channels.channelsList}
              placeholder="Select Channel"
              value={formData.ChannelId}
              onChange={handleChannelSelect}
              displayKey="DisplayName"
              idKey="Id"
              isLoading={channels.isLoading}
            />

            <InputGroup
              label="Style ID DM (Numeric)"
              type="number"
              name="StyleIdDm"
              value={formData.StyleIdDm}
              handleChange={handleChange}
              placeholder="Enter Style ID DM"
            />

            <InputGroup
              label="Style ID LM (Numeric)"
              type="number"
              name="StyleIdLm"
              value={formData.StyleIdLm}
              handleChange={handleChange}
              placeholder="Enter Style ID LM"
            />

            <InputGroup
              label="Cookie Duration (minutes)"
              type="number"
              name="CookieMinutes"
              value={formData.CookieMinutes}
              handleChange={handleChange}
              placeholder="Enter cookie duration in minutes"
            />

            <InputGroup
              label="Send To"
              name="SendTo"
              type="text"
              placeholder="Enter Send To"
              value={formData.SendTo}
              handleChange={handleChange}
            />

            <InputGroup
              label="AwId"
              name="AWId"
              type="text"
              placeholder="Enter AwId"
              value={formData.AWId}
              handleChange={handleChange}
            />

            <InputGroup
              label="Gtag for Analytics"
              name="GId"
              type="text"
              placeholder="Enter Gtag for Analytics"
              value={formData.GId}
              handleChange={handleChange}
            />

            <div className="w-full z-99">
              <SearchableDropdown
                label="Ads Provider"
                options={adsProviders.providersList}
                placeholder="Select Ads Provider"
                value={formData.AdsProviderId}
                onChange={handleAdsProviderSelect}
                displayKey="Provider"
                idKey="Id"
                isLoading={adsProviders.isLoading}
              />
            </div>

            <InputGroup
              label="Tracking Key"
              name="TrackingKey"
              type="text"
              placeholder="Tracking key will be auto-filled when you select an Ads Provider"
              value={formData.TrackingKey}
              handleChange={handleChange}
              showInformationIcon
              informationText="hello"
            />

            <InputGroup
              label="Wide Logo"
              type="file"
              name="wideLogo"
              handleChange={handleChange}
              fileStyleVariant="style1"
              accept=".jpg,.jpeg,.png,.webp,.gif" 
            />

            <InputGroup
              label="Square Logo"
              type="file"
              name="squareLogo"
              fileStyleVariant="style1"
              handleChange={handleChange}
              accept=".jpg,.jpeg,.png,.webp,.gif"
            />

            <InputGroup
              label="Contact Email"
              type="email"
              name="ContactEmail"
              value={formData.ContactEmail}
              handleChange={handleChange}
              placeholder="Enter contact email"
            />

            <TextAreaGroup
              label="Address"
              name="Address"
              value={formData.Address}
              handleChange={handleChange}
              rows={1}
              placeholder="Enter address"
            />

            <TextAreaGroup
              label="Ads text Content"
              name="AdsTxtContent"
              value={formData.AdsTxtContent}
              handleChange={handleChange}
              rows={3}
              placeholder="Enter ads text content"
            />

            <TextAreaGroup
              label="Head Tag Script"
              name="HeadTagScript"
              value={formData.HeadTagScript}
              handleChange={handleChange}
              rows={3}
              showInformationIcon
              informationText="Used In All Pages"
            />

            <TextAreaGroup
              label="Head Tag Script Landing Page"
              name="HeadTagScriptLandingPage"
              value={formData.HeadTagScriptLandingPage}
              handleChange={handleChange}
              rows={3}
              showInformationIcon
              informationText="Used In Details Page"
            />

            <TextAreaGroup
              label="Head Tag Script Search & Search Result Page"
              name="HeadTagScriptSearchPage"
              value={formData.HeadTagScriptSearchPage}
              handleChange={handleChange}
              rows={3}
              showInformationIcon
              informationText="Used In Search Page"
            />
          </div>
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 2, py: 2 }}>
        <Button
          type="submit"
          label={isEdit ? "Update Domain" : "Add Domain"}
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
        />
      </DialogActions>
    </Dialog>
  );
};

export default DomainModal;