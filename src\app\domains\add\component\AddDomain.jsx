"use client";
import React, { useEffect, useState } from "react";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import { Button } from "@/components/ui-elements/button";
import Swal from "sweetalert2";
import InputGroup from "@/components/FormElements/InputGroup";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { FaPlus } from "react-icons/fa";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { CloseIcon } from "@/assets/icons";
import DomainModal from "./DomainModal";
import apiClient from "@/utils/axiosInstance";

const AddDomain = () => {
  const [domains, setDomains] = useState([]);
  const [channels, setChannels] = useState({
    channelsList: [],
    isLoading: false,
  });
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("Name");
  const [totalCount, setTotalCount] = useState(0);

  const columns = [
    { id: "Name", label: "Domain Name" },
    { id: "ShowUrlName", label: "URL" },
    { id: "Prefix", label: "Prefix" },
  ];

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get("/Domain/Get", {
        params: {
          page: page + 1,
          length: rowsPerPage,
          q: searchTerm,
          orderBy,
          orderDir: order,
        },
      });      

      if (response.data.success) {
        setDomains(response.data.data);
        setTotalCount(response.data.pagination.recordsFiltered);
      } else {
        throw new Error(response.data.error || "Failed to fetch domains");
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to fetch domains",
        timer: 3000,
        showConfirmButton: false,
      });
      setDomains([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, rowsPerPage, searchTerm, order, orderBy]);

  useEffect(() => {
    const fetchChannels = async () => {
      try {
        setChannels((prev) => ({ ...prev, isLoading: true }));

        const response = await apiClient.get("/Channals/GetDropdown");

        if (response.data.success) {
          setChannels({
            channelsList: response.data.data,
            isLoading: false,
          });
        } else {
          console.error("Failed to fetch channels:", response.data.error);
          setChannels((prev) => ({ ...prev, isLoading: false }));
          Swal.fire({
            icon: "error",
            title: "Error",
            text: response.data.error || "Failed to load channels",
            timer: 3000,
            showConfirmButton: false,
          });
        }
      } catch (error) {
        console.error("Error fetching channels:", error);
        setChannels((prev) => ({ ...prev, isLoading: false }));
        Swal.fire({
          icon: "error",
          title: "Error",
          text: error.message || "Failed to load channels",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    };
    if (
      isModalOpen &&
      channels.channelsList.length === 0 &&
      !channels.isLoading
    ) {
      fetchChannels();
    }
  }, [isModalOpen]);

  const handleDomainSubmit = async (formData) => {
    try {
      setLoading(true);
      let response;
      if (editData) {
        formData.append('Id', editData.Id);
        response = await apiClient.put("/Domain/Edit", formData, {
          headers: { "Content-Type": "multipart/form-data" },
        });
      } else {
        response = await apiClient.post("/Domain/Add", formData, {
          headers: { "Content-Type": "multipart/form-data" },
        });        
      }

      if (response.data.success) {
        Swal.fire(
          "Success",
          editData
            ? "Domain updated successfully"
            : "Domain added successfully",
          "success",
        );
        fetchData();
        setIsModalOpen(false);
        setEditData(null);
      } else {
        throw new Error(response.data.error || "Failed to save domain");
      }
    } catch (error) {
      Swal.fire(
        "Error",
        error?.response?.data?.error || "Failed to save domain",
        "error",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleEditCategory = async (rowData) => {
    try {
      Swal.fire({
        title: "Loading Domain Data",
        html: "Please wait while we fetch the domain details...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const response = await apiClient.get(`/Domain/GetById?id=${rowData.Id}`);
      const domainData = response.data.data[0];

      setEditData({
        Id: domainData.Id,
        Name: domainData.Name,
        ShowUrlName: domainData.ShowUrlName,
        Prefix: domainData.Prefix || "",
        ChannelId: domainData.ChannelId,
        CookieMinutes: domainData.CookieMinutes?.toString() || "",
        StyleIdDm: domainData.StyleIdDm?.toString() || "",
        StyleIdLm: domainData.StyleIdLm?.toString() || "",
        HeadTagScript: domainData.HeadTagScript,
        HeadTagScriptLandingPage: domainData.HeadTagScriptLandingPage,
        HeadTagScriptSearchPage: domainData.HeadTagScriptSearchPage,
        GId: domainData.GId,
        AWId: domainData.AWId, // Commented out
        SendTo: domainData.SendTo, // Commented out
        AdsProviderId: domainData.AdsProviderId || "", // New field
        TrackingKey: domainData.TrackingKey || "", // New field
        WideLogo: null,
        SquareLogo: null,
        ContactEmail: domainData.ContactEmail || "", // New field
        Address: domainData.Address || "", // New field
        AdsTxtContent : domainData.AdsTxtContent || ""
      });

      Swal.close();

      setIsModalOpen(true);
    } catch (error) {
      console.error("Error fetching domain data:", error);

      Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          "Failed to load domain data. Please try again.",
        icon: "error",
        confirmButtonText: "OK",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  const handleDeleteCategory = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        setLoading(true);
        const response = await apiClient.delete("/Domain/Delete", {
          data: { Id: rowData.Id },
        });        

        if (response.data.success) {
          Swal.fire("Success", "Domain deleted successfully", "success");
          fetchData();
        } else {
          throw new Error(response.data.error || "Failed to delete domain");
        }
      } catch (error) {
        Swal.fire("Error", error.message || "Failed to delete domain", "error");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleChangePage = (newPage) => {
    setPage(newPage);
  };

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">Add Domain</h1>
          </div>
        </div>
        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
          <div className="mb-5 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="w-full mt-4 sm:w-64">
              <Button
                type="button"
                label="Add Domain"
                variant="primary"
                shape="rounded"
                className="ml-auto flex w-full items-center justify-center gap-2 sm:w-64"
                icon={<FaPlus size={14} />}
                onClick={() => {
                  setEditData(null);
                  setIsModalOpen(true);
                }}
              />
              </div>
              <div className="w-full sm:w-64">
                <InputGroup
                  placeholder="Search..."
                  label="Search"
                  value={searchTerm}
                  handleChange={(e) => {
                    setSearchTerm(e.target.value);
                    setPage(0);
                  }}
                  type="text"
                  className="w-full"
                />
              </div>
              
            </div>
          </div>
          <CustomDataTable
            isLoading={loading}
            columns={columns}
            rows={domains}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handleChangePage}
            onRowsPerPageChange={setRowsPerPage}
            totalCount={totalCount}
            order={order}
            orderBy={orderBy}
            onRequestSort={handleRequestSort}
            onEdit={handleEditCategory}
            onDelete={handleDeleteCategory}
          />
          <DomainModal
            isOpen={isModalOpen}
            onClose={() => {
              setIsModalOpen(false);
              setEditData(null);
            }}
            onSubmit={handleDomainSubmit}
            initialData={editData}
            isEdit={!!editData}
            channels={channels}
          />
        </div>
      </div>
    </>
  );
};

export default AddDomain;