import { useState, useCallback } from 'react';
import apiClient from '@/utils/axiosInstance';
import Swal from 'sweetalert2';

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  showErrorAlert?: boolean;
  errorTitle?: string;
  successMessage?: string;
  showSuccessAlert?: boolean;
}

interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  execute: (url: string, options?: any) => Promise<T | null>;
  reset: () => void;
}

export function useApi<T = any>(options: UseApiOptions = {}): UseApiReturn<T> {
  const {
    showErrorAlert = true,
    errorTitle = 'Error',
    successMessage,
    showSuccessAlert = false,
  } = options;

  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async (url: string, requestOptions: any = {}): Promise<T | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const { method = 'GET', data: requestData, ...otherOptions } = requestOptions;
      
      let response;
      switch (method.toUpperCase()) {
        case 'POST':
          response = await apiClient.post(url, requestData, otherOptions);
          break;
        case 'PUT':
          response = await apiClient.put(url, requestData, otherOptions);
          break;
        case 'DELETE':
          response = await apiClient.delete(url, otherOptions);
          break;
        case 'PATCH':
          response = await apiClient.patch(url, requestData, otherOptions);
          break;
        default:
          response = await apiClient.get(url, otherOptions);
      }

      const responseData = response.data;
      setState(prev => ({ ...prev, data: responseData, loading: false }));

      if (showSuccessAlert && successMessage) {
        await Swal.fire({
          icon: 'success',
          title: 'Success',
          text: successMessage,
          confirmButtonColor: '#5750f1',
        });
      }

      return responseData;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));

      if (showErrorAlert) {
        await Swal.fire({
          icon: 'error',
          title: errorTitle,
          text: errorMessage,
          confirmButtonColor: '#5750f1',
        });
      }

      return null;
    }
  }, [showErrorAlert, errorTitle, successMessage, showSuccessAlert]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    data: state.data,
    loading: state.loading,
    error: state.error,
    execute,
    reset,
  };
}

// Specialized hook for GET requests
export function useApiGet<T = any>(options: UseApiOptions = {}) {
  const api = useApi<T>(options);
  
  const get = useCallback((url: string, config?: any) => {
    return api.execute(url, { method: 'GET', ...config });
  }, [api]);

  return {
    ...api,
    get,
  };
}

// Specialized hook for POST requests
export function useApiPost<T = any>(options: UseApiOptions = {}) {
  const api = useApi<T>(options);
  
  const post = useCallback((url: string, data?: any, config?: any) => {
    return api.execute(url, { method: 'POST', data, ...config });
  }, [api]);

  return {
    ...api,
    post,
  };
}

// Specialized hook for PUT requests
export function useApiPut<T = any>(options: UseApiOptions = {}) {
  const api = useApi<T>(options);
  
  const put = useCallback((url: string, data?: any, config?: any) => {
    return api.execute(url, { method: 'PUT', data, ...config });
  }, [api]);

  return {
    ...api,
    put,
  };
}

// Specialized hook for DELETE requests
export function useApiDelete<T = any>(options: UseApiOptions = {}) {
  const api = useApi<T>(options);
  
  const del = useCallback((url: string, config?: any) => {
    return api.execute(url, { method: 'DELETE', ...config });
  }, [api]);

  return {
    ...api,
    delete: del,
  };
}
