import { prisma } from "../../../../lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { verifyToken } from "../../../../lib/varifyToken";
import { google } from "googleapis";
import {
  generateReport,
  getAdClients,
  getAdSenseAccounts,
} from "@/utils/functions";

// Helper function to deduplicate report data
function deduplicateReportData(data: any[]): any[] {
  const seen = new Set();
  return data.filter((item) => {
    const key = [
      item.DATE,
      item.COUNTRY_CODE || "",
      item.PLATFORM_TYPE_NAME || "",
      item.CUSTOM_CHANNEL_ID || "",
      item.CUSTOM_SEARCH_STYLE_ID || "",
    ].join("|");

    if (!seen.has(key)) {
      seen.add(key);
      return true;
    }
    return false;
  });
}

// Helper function to validate and process article details
async function processArticleDetails(articleId: string) {
  try {
    const articleDetails = await prisma.articleDetails.findFirst({
      where: { Id: articleId },
      select: {
        CustomChannal: true,
        StyleIdLm: true,
        StyleIdDm: true,
      },
    });

    if (!articleDetails) {
      throw new Error(`Article details not found for ID: ${articleId}`);
    }

    const channels = articleDetails.CustomChannal
      ? [articleDetails.CustomChannal]
      : [];
    const styles = [articleDetails.StyleIdLm, articleDetails.StyleIdDm].filter(
      Boolean,
    );

    return { channels, styles };
  } catch (error) {
    console.error(
      `Error processing article details for ID ${articleId}:`,
      error,
    );
    return { channels: [], styles: [] };
  }
}

// Enhanced helper functions with better error handling
const fetchChannelsRecord = async (Id: string, userSettings: any) => {
  try {
    const record = await prisma.channals.findUnique({
      where: { Id: Id },
      select: { ReportingDimensionId: true },
    });

    if (!record?.ReportingDimensionId) {
      console.warn(
        `Channel not found or missing ReportingDimensionId for ID: ${Id}`,
      );
      return null;
    }

    return record.ReportingDimensionId;
  } catch (error) {
    console.error(`Error fetching channel record for ID ${Id}:`, error);
    return null;
  }
};

const fetchStyleDimensionId = async (Id: string) => {
  try {
    const record = await prisma.styleIds.findUnique({
      where: { Id: Id },
      select: { StyleId: true },
    });

    if (!record?.StyleId) {
      console.warn(`Style not found or missing StyleId for ID: ${Id}`);
      return null;
    }

    return record.StyleId;
  } catch (error) {
    console.error(`Error fetching style record for ID ${Id}:`, error);
    return null;
  }
};

export async function POST(req: NextRequest) {
  try {
    // const user = await verifyToken(req);
    // if (!user?.Id) {
    //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    // }
    const role = req.headers.get('x-user-role') as string;
    const userId = req.headers.get('x-user-id') as string;
    const email = req.headers.get('x-user-email');
    const name = req.headers.get('x-user-name');

    const today = new Date();
    const todayFormatted = today.toISOString().split("T")[0];
    const { searchParams } = new URL(req.url);
    const code = searchParams.get("code");
    const breakPoints = searchParams.get("breakPoints") || "{}";
    const InrtoUsd = searchParams.get('InrtoUsd') || 'false';
    const startDate = searchParams.get('startDate') || todayFormatted;
    const endDate = searchParams.get('endDate') || todayFormatted;

    // const today = new Date();
    // // const todayFormatted = today.toISOString().split("T")[0];
    // const yesterday = new Date(today);
    // yesterday.setDate(today.getDate() - 1);
    // const yesterdayFormatted = yesterday.toISOString().split("T")[0];

    const body = await req.json();
    let selectedChannels = body.selectedChannels || [];
    let selectedStyles = body.selectedStyles || [];
    const articleId = body.articleId;

    if (articleId) {
      const { channels, styles } = await processArticleDetails(articleId);

      if (!body.selectedChannels?.length && channels.length) {
        selectedChannels = channels;
      }

      if (!body.selectedStyles?.length && styles.length) {
        selectedStyles = styles;
      }
    }

    // Get OAuth credentials from database
    const userSettings = await prisma.adminUserSetting.findFirst();
    if (!userSettings?.RevenueClientId || !userSettings?.RevenueClientSecret) {
      return NextResponse.json(
        { message: "OAuth credentials not found in database" },
        { status: 404 },
      );
    }

    // Initialize OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      userSettings.RevenueClientId,
      userSettings.RevenueClientSecret,
      userSettings.RevenueRedirectUrl as string,
    );

    // Handle OAuth flow if needed
    if (code) {
      try {
        const { tokens } = await oauth2Client.getToken(code);
        oauth2Client.setCredentials(tokens);
        await prisma.adminUserSetting.updateMany({
          data: {
            RevenueAccessToken: tokens.access_token,
            RevenueRefreshToken: tokens.refresh_token,
          },
        });
        return NextResponse.json(
          { message: "Authorization successful" },
          { status: 200 },
        );
      } catch (error) {
        console.error("OAuth token exchange error:", error);
        return NextResponse.json(
          { error: "Failed to exchange authorization code" },
          { status: 500 },
        );
      }
    }

    // Set credentials from database
    if (userSettings.RevenueAccessToken && userSettings.RevenueRefreshToken) {
      oauth2Client.setCredentials({
        access_token: userSettings.RevenueAccessToken,
        refresh_token: userSettings.RevenueRefreshToken,
      });
    } else {
      return NextResponse.redirect(
        oauth2Client.generateAuthUrl({
          access_type: "offline",
          scope: [
            "https://www.googleapis.com/auth/adsense.readonly",
            "https://www.googleapis.com/auth/adsense",
          ],
          prompt: "consent",
        }),
      );
    }

    // Verify token validity with enhanced error handling
    try {
      const adsense = google.adsense({ version: "v2", auth: oauth2Client });
      await adsense.accounts.list({ pageSize: 1 });
    } catch (error) {
      if (error instanceof Error && error.message.includes("invalid_token")) {
        try {
          const { credentials } = await oauth2Client.refreshAccessToken();
          await prisma.adminUserSetting.updateMany({
            data: {
              RevenueAccessToken: credentials.access_token,
              RevenueRefreshToken:
                credentials.refresh_token || userSettings.RevenueRefreshToken,
            },
          });
          oauth2Client.setCredentials(credentials);
        } catch (refreshError) {
          console.error("Token refresh error:", refreshError);
          return NextResponse.redirect(
            oauth2Client.generateAuthUrl({
              access_type: "offline",
              scope: [
                "https://www.googleapis.com/auth/adsense.readonly",
                "https://www.googleapis.com/auth/adsense",
              ],
              prompt: "consent",
            }),
          );
        }
      } else {
        throw error;
      }
    }

    // Get AdSense accounts
    const accounts = await getAdSenseAccounts(oauth2Client, google);
    if (!accounts || accounts.length === 0) {
      return NextResponse.json(
        { message: "No AdSense accounts found" },
        { status: 404 },
      );
    }

    // Verify required account IDs
    if (!userSettings?.AdsAccountId || !userSettings?.AdsClientId) {
      return NextResponse.json(
        { message: "AdSense account or client ID not found" },
        { status: 404 },
      );
    }

    // Check user permissions
    const finduser = await prisma.adminUser.findUnique({
      where: { Id: userId.toString() },
      select: { User_Type: true },
    });

    if (!finduser) {
      return NextResponse.json({ error: "User not found." }, { status: 404 });
    }

    if (
      !["Admin", "Super Admin", "Account", "Partner"].includes(
        finduser.User_Type || "",
      )
    ) {
      return NextResponse.json(
        { message: "Unauthorized role" },
        { status: 403 },
      );
    }

    // Prepare date parameters
    const finalStartDate = {
      day: startDate.split("-")[2],
      month: startDate.split("-")[1],
      year: startDate.split("-")[0],
    };
    const finalEndDate = {
      day: endDate.split("-")[2],
      month: endDate.split("-")[1],
      year: endDate.split("-")[0],
    };

    const metrics = [
      "ESTIMATED_EARNINGS",
      "IMPRESSIONS",
      "IMPRESSIONS_RPM",
      "CLICKS",
      "IMPRESSIONS_CTR",
      "COST_PER_CLICK",
    ];

    let allReportsData: any[] = [];

    if (!articleId || selectedChannels.length === 0) {
      const allChannels = await prisma.channals.findMany({
        select: { Id: true },
      });
      selectedChannels = allChannels.map((channel) => channel.Id);
    }

    if (!articleId || selectedStyles.length === 0) {
      const allStyles = await prisma.styleIds.findMany({
        select: { Id: true },
      });
      selectedStyles = allStyles.map((style) => style.Id);
    }

    // Process both channels and styles together in a single request
    if (selectedChannels.length > 0 || selectedStyles.length > 0) {
      const channelDimensions = await Promise.all(
        selectedChannels.map((id: string) => fetchChannelsRecord(id, userSettings)),
      );
      const validChannels = channelDimensions.filter(Boolean);

      const styleDimensions = await Promise.all(
        selectedStyles.map(fetchStyleDimensionId),
      );
      const validStyles = styleDimensions.filter(Boolean);

      if (validChannels.length > 0 || validStyles.length > 0) {
        try {
          const combinedReport = await generateReport(
            oauth2Client,
            userSettings.AdsAccountId,
            google,
            finalStartDate,
            finalEndDate,
            metrics,
            JSON.parse(breakPoints),
            [], // selectedOptions not used
            InrtoUsd,
            validChannels,
            validStyles
          );

          // Process the report data
          if (typeof combinedReport === "string") {
            const [headerRow, ...rows] = combinedReport.split("\n");
            const headers = headerRow.split(",");

            for (const row of rows) {
              if (!row.trim()) continue;

              const values = row.split(",");
              const rowData: any = {};

              headers.forEach((header, index) => {
                rowData[header] = values[index]?.trim() || null;
              });

              allReportsData.push(rowData);
            }
          } else if (Array.isArray(combinedReport)) {
            allReportsData = allReportsData.concat(combinedReport);
          }
        } catch (error) {
          console.error("Error generating combined report:", error);
        }
      }
    }

    // Deduplicate and filter the data
    const processedData = deduplicateReportData(
      allReportsData.filter((row) => {
        return (
          row.DATE &&
          (row.ESTIMATED_EARNINGS ||
            row.IMPRESSIONS ||
            row.CLICKS ||
            row.IMPRESSIONS_RPM ||
            row.IMPRESSIONS_CTR ||
            row.COST_PER_CLICK)
        );
      }),
    );

    // Return JSON response with deduplicated data
    return NextResponse.json(
      {
        data: processedData,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("AdSense POST Error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}