import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alogTitle, DialogContent, DialogActions, IconButton, FormControl, InputLabel, Select, MenuItem } from "@mui/material";
import { CloseIcon } from "@/assets/icons";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import Checkbox from "@/components/FormElements/checkbox";
import Swal from "sweetalert2";

const AddEditUserModal = ({ isOpen, onClose, onSubmit, isLoading, initialData, mode }) => {
  const isEditMode = mode === "edit";
  const [formData, setFormData] = useState({
    Name: "",
    Email: "",
    Number: "",
    User_Type: "",
    Password: "",
    DisplayName: "",
    ProfilePic: null,
    AccessExpiration: "",
    CreationDate: new Date().toISOString().split("T")[0],
    Status: false,
    AboutMe: "",
  });
  const [errors, setErrors] = useState({});
  const [imagePreview, setImagePreview] = useState(null); // State for image preview

  useEffect(() => {
    if (isOpen) {
      if (isEditMode && initialData) {
        setFormData({
          ...initialData,
          Password: "", // Password is not pre-filled for security
        });
        if (initialData.ProfilePic) {
          setImagePreview(process.env.NEXT_PUBLIC_IMAGE_ACCESS_DOMAIN + initialData.ProfilePic);
        } else {
          setImagePreview(null);
        }

        setErrors({});
      } else {
        const today = new Date().toISOString().split("T")[0];
        const expiration = new Date();
        expiration.setFullYear(expiration.getFullYear() + 1);
        setFormData({
          Name: "",
          Email: "",
          Number: "",
          User_Type: "",
          Password: "",
          DisplayName: "",
          ProfilePic: null,
          AccessExpiration: expiration.toISOString().split("T")[0],
          CreationDate: today,
          Status: false,
          AboutMe: "",
        });
        setImagePreview(null);
        setErrors({});
      }
    }
  }, [isOpen, initialData, isEditMode]);

  const handleChange = (e) => {
    const { name, value, files, type, checked } = e.target;

    let newValue;
    if (type === "checkbox") {
      newValue = checked;
    } else if (type === "file") {
        newValue = files?.[0] || null;
        
        // Handle image preview for file input
        if (files && files[0]) {
          const reader = new FileReader();
          reader.onload = (e) => {
            setImagePreview(e.target.result);
          };
          reader.readAsDataURL(files[0]);
        } else {
          // If no file selected and we're in edit mode, keep the existing image
          if (isEditMode && initialData?.ProfilePic) {
            setImagePreview(process.env.NEXT_PUBLIC_IMAGE_ACCESS_DOMAIN + initialData.ProfilePic);
          } else {
            setImagePreview(null);
          }
        }
      } 
      else if (name === "Number") {
      if (value && !/^\d*$/.test(value)) {
        return;
      }
      newValue = value;
    } else {
      newValue = value;
    }

    setFormData((prevData) => ({
      ...prevData,
      [name]: newValue,
    }));

    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    const requiredFields = [
      { key: "Name", label: "User Name" },
      { key: "Email", label: "Email" },
      { key: "User_Type", label: "User Type" },
    ];

    if (!isEditMode) {
      requiredFields.push({ key: "Password", label: "Password" });
    }

    requiredFields.forEach(({ key, label }) => {
      if (!formData[key]?.trim()) {
        newErrors[key] = `${label} is required`;
      }
    });

    if (formData.Email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.Email)) {
      newErrors.Email = "Invalid email format";
    }

    if (formData.Number && !/^\d{10}$/.test(formData.Number)) {
      newErrors.Number = "Mobile number must be exactly 10 digits";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }
    try {
      await onSubmit(formData);
      setFormData({
        Name: "",
        Email: "",
        Number: "",
        User_Type: "",
        Password: "",
        DisplayName: "",
        ProfilePic: null,
        AccessExpiration: "",
        CreationDate: new Date().toISOString().split("T")[0],
        Status: false,
        AboutMe: "",
      });
      setImagePreview(null);
      setErrors({});
    } catch (error) {
      console.error("Error submitting form:", error);
      await Swal.fire({
        title: "Error",
        text: `Failed to ${isEditMode ? "update" : "add"} user: ${error.message}`,
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        onClose();
        setErrors({});
      }}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {isEditMode ? "Edit User Details" : "Add New User"}
        </span>
        <IconButton
          aria-label="close"
          onClick={() => {
            onClose();
            setErrors({});
          }}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form
          onSubmit={handleSubmit}
          style={{ display: "flex", flexDirection: "column", gap: 16 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: 20,
            }}
          >
            <FormControl fullWidth error={!!errors.User_Type}>
              <InputLabel>User Type</InputLabel>
              <Select
                name="User_Type"
                value={formData.User_Type || ""}
                onChange={handleChange}
                required
                label="User Type"
              >
                <MenuItem value="">-- Select User Type --</MenuItem>
                <MenuItem value="Partner">Partner</MenuItem>
                <MenuItem value="Admin">Admin</MenuItem>
                <MenuItem value="Super Admin">Super Admin</MenuItem>
                <MenuItem value="Account">Account</MenuItem>
              </Select>
              {errors.User_Type && (
                <span style={{ color: "red", fontSize: "0.75rem", marginTop: "4px" }}>
                  {errors.User_Type}
                </span>
              )}
            </FormControl>

            <InputGroup
              label="User Name"
              name="Name"
              type="text"
              value={formData.Name || ""}
              handleChange={handleChange}
              placeholder="Enter user name"
              disabled={isLoading}
              required
              error={errors.Name}
            />
            <InputGroup
              label="Email"
              name="Email"
              type="email"
              value={formData.Email || ""}
              handleChange={handleChange}
              placeholder="Enter email"
              disabled={isLoading || isEditMode} // Disable email in edit mode
              required
              error={errors.Email}
            />
            <InputGroup
              label="Number"
              name="Number"
              type="tel"
              value={formData.Number || ""}
              handleChange={handleChange}
              placeholder="Enter mobile number"
              disabled={isLoading}
              error={errors.Number}
            />
            {!isEditMode && (
              <InputGroup
                label="Password"
                name="Password"
                type="password"
                value={formData.Password || ""}
                handleChange={handleChange}
                placeholder="Enter password"
                disabled={isLoading}
                required
                error={errors.Password}
              />
            )}
            <InputGroup
              label="Display Name (Optional)"
              name="DisplayName"
              type="text"
              value={formData.DisplayName || ""}
              handleChange={handleChange}
              placeholder="Enter display name"
              disabled={isLoading}
            />
            <InputGroup
              label="Access Expiration"
              name="AccessExpiration"
              type="date"
              value={
                formData.AccessExpiration
                  ? new Date(formData.AccessExpiration).toISOString().slice(0, 10)
                  : ""
              }
              handleChange={handleChange}
              disabled={isLoading}
            />
            <InputGroup
              label="About Me"
              name="AboutMe"
              type="textarea"
              value={formData.AboutMe || ""}
              handleChange={handleChange}
              placeholder="Enter about me description"
              disabled={isLoading}
            />
            <InputGroup
              type="hidden"
              name="CreationDate"
              value={formData.CreationDate || ""}
            />
            <InputGroup
              type="file"
              name="ProfilePic"
              handleChange={handleChange}
              fileStyleVariant="style1"
              label="Profile Picture"
              placeholder="Profile Picture"
              accept=".jpg,.jpeg,.png,.webp,.gif" 
            />
            {/* {console.log(formData.ProfilePic)} */}
             {/* Image Preview */}
             {imagePreview && (
                <div style={{ marginTop: "16px", textAlign: "center" }}>
                  <img
                    src={imagePreview}
                    alt="Profile Preview"
                    style={{
                      height: "128px",
                      width: "128px",
                    }}
                  />
                </div>
              )}
            <div style={{ gridColumn: "1 / -1" }}>
              <Checkbox
                label="Active"
                name="Status"
                onChange={handleChange}
                withIcon="check"
                withBg
                radius="md"
                checked={formData.Status === true}
              />
            </div>
          </div>
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 2, py: 2 }}>
        <Button
          label="Cancel"
          variant="secondary"
          shape="rounded"
          onClick={() => {
            onClose();
            setErrors({});
          }}
          disabled={isLoading}
        />
        <Button
          label={isLoading ? (isEditMode ? "Updating..." : "Adding...") : (isEditMode ? "Save" : "Add User")}
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
          disabled={isLoading}
        />
      </DialogActions>
    </Dialog>
  );
};

export default AddEditUserModal;