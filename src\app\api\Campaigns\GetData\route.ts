import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // Authentication check
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        // Extract query parameters
        const { searchParams } = new URL(req.url);
        const articleId = searchParams.get("articleId");
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";
        const search = searchParams.get("q")?.toLowerCase();

        // Validate articleId (UUID)
        if (!articleId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(articleId)) {
            return NextResponse.json(
                { error: "Valid ArticleId (UUID) is required" },
                { status: 400 }
            );
        }

        // Build the where clause
        const where: any = {
            ArticleId: articleId,
            CampaignId: { not: null }
        };

        if (search) {
            where.OR = [
                {
                    Ads_Campaigns: {
                        Name: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    }
                },
                ...(isNaN(Number(search)) ? [] : [{
                    Ads_Campaigns: {
                        CampaignId: {
                            equals: BigInt(search)
                        }
                    }
                }]),
                {
                    CreatedAt: {
                        gte: isNaN(Date.parse(search)) ? undefined : new Date(search),
                        lte: isNaN(Date.parse(search)) ? undefined : new Date(search)
                    }
                }
            ].filter(Boolean);
        }

        // Step 1: Get campaign mappings with related data
        const results = await prisma.articleCampaignMappings.findMany({
            where,
            select: {
                Id: true,
                CreatedAt: true,
                CampaignId: true,
                ArticleId: true,
                Ads_Campaigns: {
                    select: {
                        CampaignId: true,
                        Name: true
                    }
                }
            }
        });

        // Transform results to match expected format
        let formattedResults = results.map(mapping => ({
            Id: mapping.Id,
            CampaignId: mapping.Ads_Campaigns?.CampaignId?.toString() || "",
            Name: mapping.Ads_Campaigns?.Name || "N/A",
            CreatedAt: mapping.CreatedAt || new Date(0) // Fallback to epoch if null
        }));

        // Apply sorting
        formattedResults.sort((a, b) => {
            let comparison = 0;

            switch (orderBy) {
                case "CampaignId":
                    comparison = BigInt(a.CampaignId || "0") > BigInt(b.CampaignId || "0") ? 1 : -1;
                    break;
                case "Name":
                    comparison = (a.Name || "").localeCompare(b.Name || "");
                    break;
                case "CreatedAt":
                    const dateA = a.CreatedAt ? new Date(a.CreatedAt) : new Date(0);
                    const dateB = b.CreatedAt ? new Date(b.CreatedAt) : new Date(0);
                    comparison = dateA > dateB ? 1 : -1;
                    break;
                default:
                    const defaultDateA = a.CreatedAt ? new Date(a.CreatedAt) : new Date(0);
                    const defaultDateB = b.CreatedAt ? new Date(b.CreatedAt) : new Date(0);
                    comparison = defaultDateA > defaultDateB ? 1 : -1;
            }

            return orderDir.toLowerCase() === "desc" ? comparison * -1 : comparison;
        });

        // Apply search filter if needed (additional client-side filtering)
        if (search) {
            formattedResults = formattedResults.filter(item =>
                item.CampaignId?.toLowerCase().includes(search) ||
                item.Name?.toLowerCase().includes(search) ||
                item.CreatedAt?.toString().toLowerCase().includes(search)
            );
        }

        // Pagination
        const recordsTotal = formattedResults.length;
        const paginatedData = length === -1
            ? formattedResults
            : formattedResults.slice((start - 1) * length, start * length);

        return NextResponse.json({
            success: true,
            data: paginatedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsTotal,
                recordsFiltered: recordsTotal,
                currentPageCount: paginatedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsTotal / length),
                hasNextPage: length === -1 ? false : start * length < recordsTotal,
                hasPreviousPage: start > 1,
            },
        });

    } catch (error) {
        console.error("Error fetching campaigns:", error);
        return NextResponse.json(
            { error: "Failed to fetch campaigns" },
            { status: 500 }
        );
    } 
}