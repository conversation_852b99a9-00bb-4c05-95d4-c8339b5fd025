-- View: public.vw_articlecampaingchannelstylemapping

-- DROP VIEW public.vw_articlecampaingchannelstylemapping;

CREATE OR REPLACE VIEW public.vw_articlecampaingchannelstylemapping
 AS
 SELECT ad."Id" AS articleid,
    acm."CampaignId" AS campaignsrno,
    camp."CampaignId" AS googlecampaignid,
    c."ReportingDimensionId" AS channelreportingdimensionid,
    c."Id" AS channelid,
    camp."Name" AS campaignname,
    ad."Title" AS articlename,
    ad."StyleIdLm" AS "StyleId",
    si."Name" AS stylename,
    acd."SegmentDate" AS segmentdate
   FROM "ArticleDetails" ad
     JOIN "ArticleCampaignMappings" acm ON ad."Id" = acm."ArticleId"
     JOIN "Ads_Campaigns" camp ON acm."CampaignId" = camp."SNo"
     JOIN "Ads_CampaignDetails" acd ON camp."CampaignId" = acd."CampaignId"
     JOIN "Channals" c ON c."Id" = ad."CustomChannal"
     LEFT JOIN "StyleIds" si ON ad."StyleIdLm" = si."Id"
  WHERE 1 = 1 AND ad."IsDeleted" = false;
