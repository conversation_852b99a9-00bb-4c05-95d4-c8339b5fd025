import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
import { prisma } from '../../../../lib/prisma';


export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        let where: any = {
        };

        const users = await prisma.adsProvider.findMany({
            where,
            select: {
               Id: true,
               Provider: true
            },
            orderBy: {
                Provider: 'desc',
            }
        });


        return NextResponse.json({
            success: true,
            data: users,
        });

    } catch (error) {
        console.error("Error fetching ads Providers:", error);
        return NextResponse.json(
            { error: "Failed to fetch ads Providers" },
            { status: 500 }
        );
    }
}
