export const trimObject = (obj) => {
  return Object.keys(obj).reduce((acc, key) => {
    const value = obj[key];
    acc[key] = typeof value === "string" ? value?.trim() : value;
    return acc;
  }, {});
};

export const themeChanger = (mode) => {
  return mode === "dark" ? "dark" : null;
};

export async function readTokensFromFile(TOKENS_PATH, fs) {
  try {
    const data = await fs.readFile(TOKENS_PATH, "utf8");
    return JSON.parse(data);
  } catch (error) {
    console.error("Error reading tokens file:", error);
    return null;
  }
}

export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.readAsDataURL(file);

    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};


export async function saveTokensToFile(tokens, TOKENS_PATH, fs) {
  try {
    await fs.writeFile(TOKENS_PATH, JSON.stringify(tokens), {
      encoding: "utf8",
    });
  } catch (error) {
    console.error("Error writing tokens to file:", error);
  }
}
export async function getAdSenseAccounts(oauth2Client, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.list({});
  return res?.data?.accounts;
}

export async function getAdClients(oauth2Client, accountId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.adclients?.list({
    parent: accountId,
  });
  return res?.data?.adClients;
}
export async function getCustomChannels(oauth2Client, adClientId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });

  const res = await adsense?.accounts?.adclients?.customchannels?.list({
    parent: adClientId,
  });
  
  return res?.data?.customChannels;
}
export async function createCustomChannelId(oauth2Client, accountId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.adclients?.customchannels?.create({
    parent: accountId,
    requestBody: {
      displayName: "Custom Channel-Goti",
      active: true,
    },
  });
  return res;
}

export async function getSavedReports(oauth2Client, accountId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.reports?.saved?.list({
    parent: accountId,
  });
  return res?.data?.savedReports;
}

export async function generateSavedReport(
  oauth2Client,
  savedReportId,
  startDate,
  endDate,
  google,
  toggle
) {
  const adsense = google?.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.reports?.saved?.generate({
    "startDate.year": startDate.year,
    "startDate.day": startDate.day,
    "startDate.month": startDate.month,
    "endDate.year": endDate.year,
    "endDate.day": endDate.day,
    "endDate.month": endDate.month,
    name: savedReportId,
    currencyCode: toggle === "false" ? "USD" : "INR",
  });

  return res?.data;
}

export async function generateReport(
  oauth2Client,
  accountId,
  google,
  startDate,
  endDate,
  metrics,
  breakPoints,
  selectedOptions,
  toggle,
  selectedChannels,
  selectedStyles
) {
  const breakPointsData = [
    "DATE",
    ...(breakPoints?.country ? ["COUNTRY_CODE"] : []),
    ...(breakPoints?.platform ? ["PLATFORM_TYPE_NAME"] : []),
    ...(selectedChannels?.length > 0 || breakPoints?.customChannel ? ["CUSTOM_CHANNEL_NAME", "CUSTOM_CHANNEL_ID"] : []),
    ...(selectedStyles?.length > 0 || breakPoints?.styleId ? ["CUSTOM_SEARCH_STYLE_NAME", "CUSTOM_SEARCH_STYLE_ID"] : [])
  ];

  const uniqueDimensions = [...new Set(breakPointsData)];
  const adsense = google?.adsense({ version: "v2", auth: oauth2Client });

  // Group filters by type
  const filterGroups = {};

  // Handle channels - combine multiple with commas
  if (selectedChannels?.length > 0) {
    filterGroups.CUSTOM_CHANNEL_ID = selectedChannels
      .filter(channelId => channelId?.trim())
      .map(channelId => `CUSTOM_CHANNEL_ID==${channelId.trim()}`)
      .join(',');
  }

  // Handle styles - combine multiple with commas
  if (selectedStyles?.length > 0) {
    filterGroups.CUSTOM_SEARCH_STYLE_ID = selectedStyles
      .filter(styleId => styleId?.trim())
      .map(styleId => `CUSTOM_SEARCH_STYLE_ID==${styleId.trim()}`)
      .join(',');
  }

  // Handle other filters
  if (breakPoints && typeof breakPoints === 'object') {
    if (breakPoints.countryCode) {
      filterGroups.COUNTRY_CODE = Array.isArray(breakPoints.countryCode)
        ? breakPoints.countryCode
          .filter(country => country)
          .map(country => `COUNTRY_CODE==${country}`)
          .join(',')
        : `COUNTRY_CODE==${breakPoints.countryCode}`;
    }

    if (breakPoints.platformType) {
      filterGroups.PLATFORM_TYPE_NAME = Array.isArray(breakPoints.platformType)
        ? breakPoints.platformType
          .filter(platform => platform)
          .map(platform => `PLATFORM_TYPE_NAME==${platform}`)
          .join(',')
        : `PLATFORM_TYPE_NAME==${breakPoints.platformType}`;
    }

    if (breakPoints.specificDate) {
      filterGroups.DATE = `DATE==${breakPoints.specificDate}`;
    }
  }

  const generatedObject = {
    account: accountId,
    "startDate.year": parseInt(startDate.year),
    "startDate.day": parseInt(startDate.day),
    "startDate.month": parseInt(startDate.month),
    "endDate.year": parseInt(endDate.year),
    "endDate.day": parseInt(endDate.day),
    "endDate.month": parseInt(endDate.month),
    dimensions: uniqueDimensions,
    metrics: metrics,
    currencyCode: toggle === "false" ? "USD" : "INR"
  };

  // Convert filter groups to API format
  if (Object.keys(filterGroups).length > 0) {
    generatedObject.filters = Object.values(filterGroups);
  }

  try {
    const res = await adsense?.accounts?.reports?.generateCsv(generatedObject);
    // console.log(res, "response API");
    
    return res?.data;
  } catch (error) {
    if (Object.keys(filterGroups).length > 0) {
      const objectWithoutFilters = { ...generatedObject };
      delete objectWithoutFilters.filters;
      try {
        const retryRes = await adsense?.accounts?.reports?.generateCsv(objectWithoutFilters);
        return retryRes?.data;
      } catch (retryError) {
        throw retryError;
      }
    }
    throw error;
  }
}
export function getLast7Days() {
  const days = [];
  const today = new Date();

  for (let i = 6; i >= 0; i--) {
    const day = new Date();
    day.setDate(today.getDate() - i);
    days.push(day.toISOString().split("T")[0]);
  }

  return days;
}
export function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

export const getCurrency = (curr) => {
  switch (curr) {
    case "USD":
      return "$";
      break;
    case "INR":
      return "₹";
      break;
    default:
      return "₹";
  }
};

// export const deleteImage = async (imageUrl, path, fs) => {
//   const imagePath = path.resolve(process.cwd(), 'public', 'uploads', imageUrl?.split('uploads')[1]);
//   const fileName = path.basename(imagePath, path.extname(imagePath));
//   const fileExtension = path.extname(imagePath);
//   const originalImagePath = path.resolve(process.cwd(), 'public', 'uploads', fileName + fileExtension);
//   const smallImagePath = path.resolve(process.cwd(), 'public', 'uploads', fileName + "_small" + fileExtension);
//   const mediumImagePath = path.resolve(process.cwd(), 'public', 'uploads', fileName + "_medium" + fileExtension);

//   try {
//     if(!imageUrl?.includes("cloudinary")){
//       if (fs.existsSync(originalImagePath)) {
//         await fs.promises.unlink(originalImagePath);
//         console.log('Original image deleted successfully!');
//       }
//       if (fs.existsSync(smallImagePath)) {
//         await fs.promises.unlink(smallImagePath);
//         console.log('Small image deleted successfully!');
//       }
//       if (fs.existsSync(mediumImagePath)) {
//         await fs.promises.unlink(mediumImagePath);
//         console.log('medium image deleted successfully!');
//       }
//       return 'Both images deleted successfully!';
//     }
//   } catch (error) {
//     throw new Error('Error deleting the images: ' + error.message);
//   }
// };

// export const fetchImage = (type, imageUrl) => {

//   const lastDotIndex = imageUrl.lastIndexOf(".");
//   switch (type) {
//     case "small":
//       if (lastDotIndex !== -1) {
//         imageUrl =
//           imageUrl.slice(0, lastDotIndex) +
//           "_small" +
//           imageUrl.slice(lastDotIndex);
//         return imageUrl;
//       }
//       break;
//     case "medium":
//       if (lastDotIndex !== -1) {
//         imageUrl =
//           imageUrl.slice(0, lastDotIndex) +
//           "_medium" +
//           imageUrl.slice(lastDotIndex);
//         return imageUrl;
//       }
//       break;

//     case "cloud":
//       return imageUrl;
//       break;
//     default:
//       return imageUrl;
//       break;
//   }
// };
export const deleteImage = async (imageUrl, path, fs) => {
  const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";
  const imagePath = path.resolve(UPLOAD_DIR, imageUrl.replace(/^\//, '')); // Remove leading slash
  const fileName = path.basename(imagePath, path.extname(imagePath));
  const fileExtension = path.extname(imagePath);
  const originalImagePath = path.resolve(UPLOAD_DIR, fileName + fileExtension);
  const smallImagePath = path.resolve(UPLOAD_DIR, fileName + "_small" + fileExtension);
  const mediumImagePath = path.resolve(UPLOAD_DIR, fileName + "_medium" + fileExtension);

  try {
    if (!imageUrl?.includes("cloudinary")) {
      if (fs.existsSync(originalImagePath)) {
        await fs.promises.unlink(originalImagePath);
        // console.log('Original image deleted successfully!');
      }
      if (fs.existsSync(smallImagePath)) {
        await fs.promises.unlink(smallImagePath);
        // console.log('Small image deleted successfully!');
      }
      if (fs.existsSync(mediumImagePath)) {
        await fs.promises.unlink(mediumImagePath);
        // console.log('Medium image deleted successfully!');
      }
      return 'All images deleted successfully!';
    }
  } catch (error) {
    throw new Error('Error deleting the images: ' + error.message);
  }
};

export const fetchImage = (type, imageUrl) => {
  const IMAGE_ACCESS_DOMAIN = process.env.NEXT_PUBLIC_IMAGE_ACCESS_DOMAIN || "/uploads/";
  if (imageUrl.includes("cloudinary")) {
    return imageUrl; // Return Cloudinary URL as is
  }

  const lastDotIndex = imageUrl.lastIndexOf(".");
  let modifiedUrl = imageUrl;

  switch (type) {
    case "small":
      if (lastDotIndex !== -1) {
        modifiedUrl =
          imageUrl.slice(0, lastDotIndex) +
          "_small" +
          imageUrl.slice(lastDotIndex);
      }
      break;
    case "medium":
      if (lastDotIndex !== -1) {
        modifiedUrl =
          imageUrl.slice(0, lastDotIndex) +
          "_medium" +
          imageUrl.slice(lastDotIndex);
      }
      break;
    default:
      modifiedUrl = imageUrl;
      break;
  }

  return `${IMAGE_ACCESS_DOMAIN}${modifiedUrl}`; // Prepend domain
};

export function decodeJWT(token) {
  try {
    const base64Url = token?.split('.')[1]; // Get payload part
    const base64 = base64Url?.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c =>
      '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    ).join(''));

    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error("Invalid JWT", e);
    return null;
  }
}