"use client";

import React from "react";
import BaseModal from "./BaseModal";
import { Button } from "@/components/ui-elements/button";

interface FormModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  onSubmit: (e: React.FormEvent) => void;
  isLoading?: boolean;
  submitLabel?: string;
  cancelLabel?: string;
  submitVariant?: "primary" | "green" | "danger" | "dark";
  maxWidth?: "xs" | "sm" | "md" | "lg" | "xl";
  fullWidth?: boolean;
  disableBackdropClick?: boolean;
  className?: string;
  showCancelButton?: boolean;
  customActions?: React.ReactNode;
  disabled?: boolean;
}

const FormModal: React.FC<FormModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  onSubmit,
  isLoading = false,
  submitLabel = "Save",
  cancelLabel = "Cancel",
  submitVariant = "primary",
  maxWidth = "md",
  fullWidth = true,
  disableBackdropClick = false,
  className = "",
  showCancelButton = true,
  customActions,
  disabled = false,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!disabled && !isLoading) {
      onSubmit(e);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const defaultActions = (
    <>
      {showCancelButton && (
        <Button
          label={cancelLabel}
          variant="outlineDark"
          shape="rounded"
          onClick={handleClose}
          disabled={isLoading}
        />
      )}
      <Button
        label={isLoading ? "Loading..." : submitLabel}
        variant={submitVariant}
        shape="rounded"
        onClick={handleSubmit}
        disabled={disabled || isLoading}
        type="submit"
      />
    </>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title={title}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      disableBackdropClick={disableBackdropClick || isLoading}
      className={className}
      actions={customActions || defaultActions}
    >
      <form onSubmit={handleSubmit} className="flex flex-1 flex-col">
        {children}
      </form>
    </BaseModal>
  );
};

export default FormModal;
