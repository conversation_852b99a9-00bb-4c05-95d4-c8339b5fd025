-- DROP FUNCTION public.fn_ads_revenue_data_test_styleid(text, text, date, date, text, bool, bool, text);

CREATE OR REPLACE FUNCTION public.fn_ads_revenue_data_test_styleid(p_article_id text, p_channel_id text, p_from_date date, p_to_date date, p_group_mode text, p_isnullable_data boolean, p_inr_to_usd boolean, p_style_id text)
 RETURNS void
 LANGUAGE plpgsql
AS $function$

DECLARE
    sql TEXT;
	selectefield TEXT := '';
    selectfiledforagg TEXT := '';
	selectfiledforgroupby TEXT := '';
    selectfieldforwherecls TEXT := '';
    selectfieldforjoin TEXT := '';
	nullable_where_clause  TEXT := '';
	today_rate numeric(10,2);
BEGIN

	nullable_where_clause  := 'WHERE
	COALESCE(rv."total_rv_revenue", 0) != 0 OR
	COALESCE(rv."total_rv_rpc", 0) != 0 OR
	COALESCE((rv."total_rv_revenue" - ads."total_ads_cost"), 0) != 0 OR
	COALESCE(ads."total_ads_conversions", 0) != 0 OR
	COALESCE(rv."total_rv_clicks", 0) != 0 OR
	COALESCE(ads."total_ads_cost", 0) != 0 OR
	COALESCE(ads."avg_ads_cpa", 0) != 0 OR
	COALESCE(ads."avg_ads_cpc", 0) != 0 OR
	COALESCE(ads."total_ads_clicks", 0) != 0';

	today_rate := 
		(SELECT ROUND("USDtoINR", 2)
		FROM "Ads_RateConversion" 
		WHERE "Date" = (SELECT MAX("Date") FROM "Ads_RateConversion"));

	-- Cleanup any existing temp table
    EXECUTE 'DROP TABLE IF EXISTS pg_temp.temp_ads_result';

	EXECUTE ' CREATE TEMP TABLE pg_temp.temp_ads_result (
			style_name text,
			styleid uuid,
            article_name text,
			articleid uuid,
            channel_name text,
			channelid uuid,
			country text,
            date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric,
			rv_impressions numeric,
			rv_rpm numeric
        )';

	

		IF p_group_mode IS NULL OR p_group_mode = '' THEN
        selectefield := ' ''''::text, NULL::uuid, ''''::text, NULL::uuid, ''''::text, NULL::uuid,''''::text, NULL::date, ';
		selectfiledforagg := '';
		selectfiledforgroupby := '';
        selectfieldforwherecls := ' TRUE ';
        selectfieldforjoin := '';

		ELSIF p_group_mode = 'date' THEN
        selectefield := ' ''''::text, NULL::uuid, ''''::text, NULL::uuid, ''''::text, NULL::uuid,''''::text, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := ' segmentdate, ';
		selectfiledforgroupby := ' GROUP BY segmentdate';
        selectfieldforwherecls := ' rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := '';

		ELSIF p_group_mode = 'country' THEN
        selectefield := ' ''''::text, NULL::uuid, ''''::text, NULL::uuid, ''''::text, NULL::uuid, COALESCE(rv.country, ads.country) AS country, NULL::date, ';
        selectfiledforagg := ' country, ' ;
		selectfiledforgroupby := ' GROUP BY country';
        selectfieldforwherecls := ' rv.country = ads.country';
        selectfieldforjoin := ' ';

		ELSIF p_group_mode = 'article' THEN
        selectefield := ' ''''::text, NULL::uuid, art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, ''''::text, NULL::uuid, ''''::text,NULL::date, ' ;
        selectfiledforagg := ' articleid, ';
		selectfiledforgroupby := ' GROUP BY articleid';
        selectfieldforwherecls := ' rv.articleid = ads.articleid';
        selectfieldforjoin := ' LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';

		ELSIF p_group_mode = 'channel' THEN
        selectefield := ' ''''::text, NULL::uuid,''''::text, NULL::uuid, ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,''''::text,NULL::date, ';
        selectfiledforagg := ' channelid,';
		selectfiledforgroupby := ' GROUP BY channelid';
        selectfieldforwherecls := ' rv.channelid = ads.channelid';
        selectfieldforjoin := ' LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'styleId' THEN
        selectefield := '  styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,  ''''::text, NULL::uuid,''''::text, NULL::uuid, ''''::text,NULL::date, ';
        selectfiledforagg := ' styleid,';
		selectfiledforgroupby := ' GROUP BY styleid';
        selectfieldforwherecls := ' rv.styleid = ads.styleid ';
        selectfieldforjoin := ' LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) ';

		ELSIF p_group_mode = 'date,country' THEN
        selectefield := ' ''''::text, NULL::uuid,''''::text, NULL::uuid,''''::text, NULL::uuid, COALESCE(rv.country, ads.country) AS country,COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' country,segmentdate, ' ;
		selectfiledforgroupby := ' GROUP BY country, segmentdate';
        selectfieldforwherecls := ' rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' ';

		ELSIF p_group_mode = 'date,article' THEN
        selectefield := ' ''''::text, NULL::uuid, art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, ''''::text, NULL::uuid,  ''''::text, COALESCE(rv.segmentdate, ads.segmentdate) AS date, ' ;
        selectfiledforagg := ' articleid,segmentdate, ';
		selectfiledforgroupby := ' GROUP BY articleid,segmentdate';
        selectfieldforwherecls := ' rv.articleid = ads.articleid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';

		ELSIF p_group_mode = 'date,channel' THEN
        selectefield := ' ''''::text, NULL::uuid,''''::text, NULL::uuid, ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,''''::text, COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' channelid,segmentdate,';
		selectfiledforgroupby := ' GROUP BY channelid, segmentdate';
        selectfieldforwherecls := ' rv.channelid = ads.channelid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'date,styleId' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid, ''''::text, NULL::uuid, ''''::text, NULL::uuid, ''''::text, COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' styleid,segmentdate,';
		selectfiledforgroupby := ' GROUP BY styleid, segmentdate';
        selectfieldforwherecls := ' rv.styleid = ads.styleid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid)';

		ELSIF p_group_mode = 'country,article' THEN
		selectefield := ' ''''::text, NULL::uuid, art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, ''''::text, NULL::uuid, COALESCE(rv.country, ads.country) AS country, NULL::date, ';
		selectfiledforagg := ' articleid, country, ';
		selectfiledforgroupby := ' GROUP BY articleid, country ';
		selectfieldforwherecls := ' rv.articleid = ads.articleid AND rv.country = ads.country';
		selectfieldforjoin :=  ' LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';

		ELSIF p_group_mode = 'country,channel' THEN
        selectefield := ' ''''::text, NULL::uuid,''''::text, NULL::uuid, ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, COALESCE(rv.country, ads.country) AS country,NULL::date,	 ';
        selectfiledforagg := ' channelid, country, ';
		selectfiledforgroupby := ' GROUP BY channelid, country';
        selectfieldforwherecls := 'rv.channelid = ads.channelid AND rv.country = ads.country';
        selectfieldforjoin := 'LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'country,styleId' THEN
		selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,  ''''::text, NULL::uuid, ''''::text, NULL::uuid, COALESCE(rv.country, ads.country) AS country, NULL::date, ';
		selectfiledforagg := ' styleid, country, ';
		selectfiledforgroupby := ' GROUP BY styleid, country ';
		selectfieldforwherecls := ' rv.styleid = ads.styleid AND rv.country = ads.country';
		selectfieldforjoin :=  ' LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid)';

		ELSIF p_group_mode = 'article,channel' THEN
        selectefield := ' ''''::text, NULL::uuid, art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
		ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,''''::text, NULL::date, ';
        selectfiledforagg := ' articleid, channelid, ';
		selectfiledforgroupby := ' GROUP BY articleid, channelid ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'article,styleId' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
						 art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
						 ''''::text, NULL::uuid,
						 ''''::text,  NULL::date, ';
        selectfiledforagg := ' articleid, styleid, ';
		selectfiledforgroupby := ' GROUP BY articleid, styleid ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.styleid = ads.styleid';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) 
								LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid)';

		ELSIF p_group_mode = 'channel,styleId' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
						  ''''::text, NULL::uuid, 
						  ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,
						  ''''::text, NULL::date, ';
        selectfiledforagg := ' styleid, channelid, ';
		selectfiledforgroupby := ' GROUP BY styleid, channelid ';
        selectfieldforwherecls := 'rv.styleid = ads.styleid AND rv.channelid = ads.channelid';
        selectfieldforjoin := 'LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'date,country,article' THEN
		selectefield := ' ''''::text, NULL::uuid,  
						art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid,
						''''::text, NULL::uuid, 
						COALESCE(rv.country, ads.country) AS country
						, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
		selectfiledforagg := 'articleid, country, segmentdate, ';
		selectfiledforgroupby := ' GROUP BY articleid, country, segmentdate';
		selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
		selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';

		ELSIF p_group_mode = 'date,country,channel' THEN
        selectefield := ' ''''::text, NULL::uuid, 
						''''::text, NULL::uuid,
						ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, 
						COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'channelid, country, segmentdate,';
		selectfiledforgroupby := ' GROUP BY channelid, country, segmentdate';
        selectfieldforwherecls := 'rv.channelid = ads.channelid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'date,country,styleId' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
						''''::text, NULL::uuid, 
						''''::text, NULL::uuid,
						COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'styleid, country, segmentdate,';
		selectfiledforgroupby := ' GROUP BY styleid, country, segmentdate';
        selectfieldforwherecls := 'rv.styleid = ads.styleid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) ';

		ELSIF p_group_mode = 'date,article,channel' THEN
		selectefield := ' ''''::text, NULL::uuid,
						  art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
						ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid ,
						''''::text,
						COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' articleid, channelid, segmentdate, ';
		selectfiledforgroupby := ' GROUP BY articleid, channelid, segmentdate ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'date,article,styleId' THEN
		selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid, 
							art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid,
							''''::text, NULL::uuid,	''''::text,
						COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' articleid, styleid, segmentdate, ';
		selectfiledforgroupby := ' GROUP BY articleid, styleid, segmentdate ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.styleid = ads.styleid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) 
							LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid)';

		ELSIF p_group_mode = 'date,channel,styleId' THEN
		selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
							''''::text, NULL::uuid,
						ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid ,
						''''::text,
						COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' styleid, channelid, segmentdate, ';
		selectfiledforgroupby := ' GROUP BY styleid, channelid, segmentdate ';
        selectfieldforwherecls := 'rv.styleid = ads.styleid AND rv.channelid = ads.channelid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'country,article,channel' THEN
        selectefield := ' ''''::text, NULL::uuid,
								art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
								ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,
								COALESCE(rv.country, ads.country) AS country, NULL::date,
								';
        selectfiledforagg := 'channelid, articleid, country,';
		selectfiledforgroupby := ' GROUP BY channelid, articleid, country';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.country = ads.country';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'country,article,styleId' THEN
        selectefield := ' 		styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
								art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
								''''::text, NULL::uuid,
								COALESCE(rv.country, ads.country) AS country, NULL::date,
								';
        selectfiledforagg := 'styleid, articleid, country,';
		selectfiledforgroupby := ' GROUP BY styleid, articleid, country';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.styleid = ads.styleid AND rv.country = ads.country';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid)';
		

		ELSIF p_group_mode = 'country,channel,styleId' THEN
        selectefield := '      styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
								''''::text, NULL::uuid,
								ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,
								COALESCE(rv.country, ads.country) AS country, NULL::date,
								';
        selectfiledforagg := 'channelid, styleid, country,';
		selectfiledforgroupby := ' GROUP BY channelid, styleid, country';
        selectfieldforwherecls := 'rv.styleid = ads.styleid AND rv.channelid = ads.channelid AND rv.country = ads.country';
        selectfieldforjoin := 'LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'article,channel,styleId' THEN
		selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
						  art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
						ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid ,
						''''::text, NULL::date, ';
        selectfiledforagg := ' articleid, channelid, styleId, ';
		selectfiledforgroupby := ' GROUP BY articleid, channelid, styleId ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.styleId = ads.styleId';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)
		LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)
		LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid)';

		ELSIF p_group_mode = 'date,country,article,channel' THEN
        selectefield := ' ''''::text, NULL::uuid,
						   art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid,
						   ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, 
						   COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'channelid, articleid, country, segmentdate,';
		selectfiledforgroupby := ' GROUP BY channelid, articleid, country, segmentdate';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'date,country,article,styleId' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid,
						   art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid,
						   ''''::text, NULL::uuid,
						   COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'styleid, articleid, country, segmentdate,';
		selectfiledforgroupby := ' GROUP BY styleid, articleid, country, segmentdate';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.styleid = ads.styleid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) 
								LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid)';

		ELSIF p_group_mode = 'date,country,channel,styleId' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid, 
						''''::text, NULL::uuid,
						ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, 
						COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'channelid, country, segmentdate,styleid,';
		selectfiledforgroupby := ' GROUP BY channelid, country, segmentdate, styleid';
        selectfieldforwherecls := 'rv.channelid = ads.channelid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate AND rv.styleid = ads.styleid ';
        selectfieldforjoin := 'LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid) LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) ';

		ELSIF p_group_mode = 'date,article,channel,styleId' THEN
		selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid, 
						  art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
						ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid ,
						''''::text,
						COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' articleid, channelid, segmentdate,styleid, ';
		selectfiledforgroupby := ' GROUP BY articleid, channelid, segmentdate, styleid ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.segmentdate = ads.segmentdate AND rv.styleid = ads.styleid ';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) 
		LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid) LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) ';

		ELSIF p_group_mode = 'country,article,channel,styleId' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid, 
								art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
								ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,
								COALESCE(rv.country, ads.country) AS country, NULL::date,
								';
        selectfiledforagg := 'channelid, articleid, country,styleid, ';
		selectfiledforgroupby := ' GROUP BY channelid, articleid, country, styleid ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.country = ads.country AND rv.styleid = ads.styleid ';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) 
		LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid) 
		LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) ';

		ELSIF p_group_mode = 'all' THEN
        selectefield := ' styl."Name" || ''['' || styl."StyleId"  || '']'' as style_name, COALESCE(rv.styleid, ads.styleid) as  styleid, 
						   art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid,
						   ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, 
						   COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'channelid, articleid, country, segmentdate, styleid, ';
		selectfiledforgroupby := ' GROUP BY channelid, articleid, country, segmentdate, styleid ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate AND rv.styleid = ads.styleid ';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) 
								LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)
								LEFT JOIN "StyleIds" styl ON styl."Id" = COALESCE(rv.styleid, ads.styleid) ';

		
		  ELSE
        RAISE EXCEPTION 'Unsupported group mode: %', p_group_mode;
    END IF;

	 -- Append nullable condition if needed
    IF p_isnullable_data THEN
        selectfieldforjoin := selectfieldforjoin || ' ' || nullable_where_clause;
    END IF;

    sql := format($fmt$
        WITH rv_agg AS (
            SELECT %s  --select field
               SUM(rv_revenue) AS total_rv_revenue,
				 case when SUM(rv_clicks) > 0 then SUM(rv_revenue) / SUM(rv_clicks) else 0 end AS total_rv_rpc,
                --SUM(rv_rpc) AS total_rv_rpc,
				SUM(rv_clicks) AS total_rv_clicks,
				SUM(rv_impressions) AS total_rv_impressions,
				case when SUM(rv_impressions) > 0 then (SUM(rv_revenue) / SUM(rv_impressions))*1000 else 0 end AS rv_rpm
            FROM fn_get_revenue_details_test_styleId($1,$2, $3, $4,$8)  --get styleId Name from here
            %s  --GROUP BY 
        ),
        ads_agg AS (
            SELECT %s  --select field
                SUM(ads_cost) AS total_ads_cost,
                SUM(ads_clicks) AS total_ads_clicks,
                SUM(ads_conversions) AS total_ads_conversions,
                case when SUM(ads_conversions) > 0 then SUM(ads_cost) / SUM(ads_conversions) else 0 end AS avg_ads_cpa,
				case when  SUM(ads_clicks) > 0 then SUM(ads_cost) / SUM(ads_clicks) else 0  end AS avg_ads_cpc
            FROM fn_get_campaign_details_test_styleId($1,$2, $3, $4,$8)
			%s 
        )
		INSERT INTO pg_temp.temp_ads_result
        SELECT
            %s  --select field
			 CASE WHEN $7 THEN ROUND(COALESCE(rv.total_rv_revenue / %s , 0),2) ELSE ROUND(COALESCE(rv.total_rv_revenue, 0),2) END AS rv_revenue,
			 CASE WHEN $7 THEN ROUND(COALESCE(rv.total_rv_rpc / %s , 0),2) ELSE ROUND(COALESCE(rv.total_rv_rpc, 0),2) END AS rv_rpc,
             CASE WHEN $7 THEN ROUND((COALESCE(rv.total_rv_revenue , 0) - COALESCE(ads.total_ads_cost, 0)) / %s , 2) ELSE ROUND((COALESCE(rv.total_rv_revenue , 0) - COALESCE(ads.total_ads_cost, 0)), 2) END AS profit,
			 
            CASE
                WHEN ads.total_ads_cost > 0 THEN
                    COALESCE(ROUND((rv.total_rv_revenue - ads.total_ads_cost) / ads.total_ads_cost * 100, 2),0)
                ELSE 0
            END AS roi,
			
			ROUND(COALESCE(ads.total_ads_conversions, 0),2) AS ads_conversions,
            COALESCE(rv.total_rv_clicks, 0) AS ads_click,
		 	CASE WHEN $7 THEN ROUND(COALESCE(ads.total_ads_cost / %s , 0), 2) ELSE  ROUND(COALESCE(ads.total_ads_cost, 0), 2) END AS ads_spend,
		 	CASE WHEN $7 THEN ROUND(COALESCE(ROUND(ads.avg_ads_cpa / %s, 2), 0), 2) ELSE ROUND(COALESCE(ROUND(ads.avg_ads_cpa, 2), 0), 2) END AS ads_cpa,
		 	CASE WHEN $7 THEN ROUND(COALESCE(ROUND(ads.avg_ads_cpc / %s, 2), 0), 2) ELSE ROUND(COALESCE(ROUND(ads.avg_ads_cpc, 2), 0), 2) END AS ads_cpc
			 ,COALESCE(total_ads_clicks,0) as spend_clicks
			 ,COALESCE(total_rv_impressions,0) as total_rv_impressions,
			 CASE WHEN $7 THEN ROUND(COALESCE(rv.rv_rpm / %s , 0),2) ELSE ROUND(COALESCE(rv.rv_rpm, 0),2) END AS rv_rpm
        FROM rv_agg rv
        FULL OUTER JOIN ads_agg ads
            ON %s -- selectfieldforwherecls
        %s  --selectfieldforjoin
    $fmt$,
        selectfiledforagg, selectfiledforgroupby, selectfiledforagg, selectfiledforgroupby,
        selectefield,
		today_rate,today_rate,today_rate,today_rate,today_rate,today_rate,today_rate,
        selectfieldforwherecls,
        selectfieldforjoin
    );

	--RETURN sql;
    -- Execute final SQL
    EXECUTE sql USING p_article_id, p_channel_id, p_from_date, p_to_date,p_group_mode,p_isnullable_data,p_inr_to_usd,p_style_id;
	END;
$function$
;
