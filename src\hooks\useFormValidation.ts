import { useCallback } from 'react';

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  url: /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  numeric: /^\d+$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
};

// Common validation messages
export const VALIDATION_MESSAGES = {
  required: (field: string) => `${field} is required`,
  email: 'Please enter a valid email address',
  phone: 'Please enter a valid phone number',
  url: 'Please enter a valid URL',
  minLength: (field: string, min: number) => `${field} must be at least ${min} characters`,
  maxLength: (field: string, max: number) => `${field} must be no more than ${max} characters`,
  numeric: 'Please enter a valid number',
  password: 'Password must contain at least 8 characters with uppercase, lowercase, and number',
  match: (field1: string, field2: string) => `${field1} and ${field2} must match`,
};

// Validation rule builders
export const validationRules = {
  required: (message?: string) => ({
    required: true,
    message: message || VALIDATION_MESSAGES.required('This field'),
  }),

  email: (message?: string) => ({
    pattern: VALIDATION_PATTERNS.email,
    message: message || VALIDATION_MESSAGES.email,
  }),

  phone: (message?: string) => ({
    pattern: VALIDATION_PATTERNS.phone,
    message: message || VALIDATION_MESSAGES.phone,
  }),

  url: (message?: string) => ({
    pattern: VALIDATION_PATTERNS.url,
    message: message || VALIDATION_MESSAGES.url,
  }),

  minLength: (min: number, message?: string) => ({
    minLength: min,
    message: message || VALIDATION_MESSAGES.minLength('This field', min),
  }),

  maxLength: (max: number, message?: string) => ({
    maxLength: max,
    message: message || VALIDATION_MESSAGES.maxLength('This field', max),
  }),

  numeric: (message?: string) => ({
    pattern: VALIDATION_PATTERNS.numeric,
    message: message || VALIDATION_MESSAGES.numeric,
  }),

  password: (message?: string) => ({
    pattern: VALIDATION_PATTERNS.password,
    message: message || VALIDATION_MESSAGES.password,
  }),

  custom: (validator: (value: any, formData?: any) => string | null) => ({
    custom: validator,
  }),

  match: (fieldToMatch: string, fieldName?: string, matchFieldName?: string) => ({
    custom: (value: any, formData: any) => {
      const matchValue = formData[fieldToMatch]?.value;
      if (value !== matchValue) {
        return VALIDATION_MESSAGES.match(
          fieldName || 'This field',
          matchFieldName || fieldToMatch
        );
      }
      return null;
    },
  }),
};

// Combine multiple validation rules
export function combineRules(...rules: any[]) {
  return rules.reduce((combined, rule) => ({ ...combined, ...rule }), {});
}

// Common form validation schemas
export const commonValidationSchemas = {
  user: {
    Name: combineRules(
      validationRules.required('Name is required'),
      validationRules.minLength(2, 'Name must be at least 2 characters')
    ),
    Email: combineRules(
      validationRules.required('Email is required'),
      validationRules.email()
    ),
    Number: combineRules(
      validationRules.required('Phone number is required'),
      validationRules.phone()
    ),
    Password: combineRules(
      validationRules.required('Password is required'),
      validationRules.minLength(6, 'Password must be at least 6 characters')
    ),
    User_Type: validationRules.required('User type is required'),
  },

  domain: {
    Name: combineRules(
      validationRules.required('Domain name is required'),
      validationRules.minLength(2, 'Domain name must be at least 2 characters')
    ),
    ShowUrlName: validationRules.required('Show URL name is required'),
    Prefix: validationRules.required('Prefix is required'),
    ChannelId: validationRules.required('Channel is required'),
  },

  subdomain: {
    Name: combineRules(
      validationRules.required('Subdomain name is required'),
      validationRules.minLength(2, 'Subdomain name must be at least 2 characters')
    ),
    Url: combineRules(
      validationRules.required('URL is required'),
      validationRules.url('Please enter a valid URL')
    ),
    Domain: validationRules.required('Domain is required'),
  },

  styleId: {
    StyleId: combineRules(
      validationRules.required('Style ID is required'),
      validationRules.numeric('Style ID must be numeric')
    ),
    Name: combineRules(
      validationRules.required('Name is required'),
      validationRules.minLength(2, 'Name must be at least 2 characters')
    ),
  },

  article: {
    title: combineRules(
      validationRules.required('Title is required'),
      validationRules.minLength(5, 'Title must be at least 5 characters'),
      validationRules.maxLength(200, 'Title must be no more than 200 characters')
    ),
    slug: combineRules(
      validationRules.required('Slug is required'),
      validationRules.minLength(3, 'Slug must be at least 3 characters')
    ),
    domain: validationRules.required('Domain is required'),
    subdomain: validationRules.required('Subdomain is required'),
    category: validationRules.required('Category is required'),
  },
};

// Hook for form validation utilities
export function useFormValidation() {
  const validateEmail = useCallback((email: string): boolean => {
    return VALIDATION_PATTERNS.email.test(email);
  }, []);

  const validatePhone = useCallback((phone: string): boolean => {
    return VALIDATION_PATTERNS.phone.test(phone);
  }, []);

  const validateUrl = useCallback((url: string): boolean => {
    return VALIDATION_PATTERNS.url.test(url);
  }, []);

  const validateRequired = useCallback((value: any): boolean => {
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }
    return value !== null && value !== undefined && value !== '';
  }, []);

  const validateMinLength = useCallback((value: string, minLength: number): boolean => {
    return value.length >= minLength;
  }, []);

  const validateMaxLength = useCallback((value: string, maxLength: number): boolean => {
    return value.length <= maxLength;
  }, []);

  const validateNumeric = useCallback((value: string): boolean => {
    return VALIDATION_PATTERNS.numeric.test(value);
  }, []);

  const validatePassword = useCallback((password: string): boolean => {
    return VALIDATION_PATTERNS.password.test(password);
  }, []);

  const validateMatch = useCallback((value1: any, value2: any): boolean => {
    return value1 === value2;
  }, []);

  return {
    validateEmail,
    validatePhone,
    validateUrl,
    validateRequired,
    validateMinLength,
    validateMaxLength,
    validateNumeric,
    validatePassword,
    validateMatch,
    patterns: VALIDATION_PATTERNS,
    messages: VALIDATION_MESSAGES,
    rules: validationRules,
    schemas: commonValidationSchemas,
    combineRules,
  };
}
