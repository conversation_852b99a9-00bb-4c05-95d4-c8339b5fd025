import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const styleId = searchParams.get('styleIdLm') || searchParams.get('styleIdDm');
        const articleId = searchParams.get('articleId')

        if (!styleId) {
            return NextResponse.json({ error: 'Missing StyleIdLm or StyleIdDm' }, { status: 400 });
        }

        // Get CustomChannal values from articleDetails
        const articles = await prisma.articleDetails.findMany({
            where: {
                OR: [
                    { StyleIdLm: styleId },
                    { StyleIdDm: styleId }
                ]
            },
            select: {
                Id: true,
                CustomChannal: true
            }
        });

        const customChannels = articles
            .filter(article => articleId ? article.Id !== articleId : true)
            .map(article => article.CustomChannal)
            .filter(channel => channel !== null && channel !== '');

        const allChannels = await prisma.channals.findMany();

        const remainingChannels = allChannels.filter(channel => {
            return !customChannels.includes(channel.Id.toString());
        });

        const dataWithCustomChannelId = remainingChannels.map(channel => {
            const parts = (channel.Name ?? "").split('/');
            const customChannelId = parts[parts.length - 1];
            return {
                Id: channel.Id,
                DisplayName: channel.DisplayName,
                CustomChannelId: customChannelId,
            };
        });

        return NextResponse.json({
            success: true,
            dataWithCustomChannelId
        });

    } catch (error) {
        console.error("Error fetching channels:", error);
        return NextResponse.json(
            { error: "Failed to fetch channels" },
            { status: 500 }
        );
    }
}
