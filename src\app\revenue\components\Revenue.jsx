"use client";
import React, { useCallback, useEffect, useState } from "react";
import {
  formatDate,
  getCurrency,
  getLast7Days,
  decodeJWT,
} from "@/utils/functions";
import { Button } from "@/components/ui-elements/button";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import DateRangePicker from "@/components/DateRangePicker";
import { CircularProgress } from "@mui/material";
import Swal from "sweetalert2";
import { FaFilter } from "react-icons/fa";
import apiClient from "@/utils/axiosInstance";

function Revenue() {
  const [Token, setToken] = useState(null);
  const [options, setOptions] = useState([]);
  const [customChannels, setCustomChannels] = useState([]);
  const [reportsTableData, setReportsTableData] = useState([]);
  const [toggle, setToggle] = useState(true);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [selectedPartner, setSelectedPartner] = useState("");
  const [showLoader, setShowLoader] = useState(false);
  const userData = Token ? decodeJWT(Token) : null;
  const [selectedBreakPoints, setSelectedBreakPoints] = useState(["channel"]);
  const [customChannelByUsers, setCustomChannelByUsers] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);
  const [tablePage, setTablePage] = useState(0);
  const [tableRowsPerPage, setTableRowsPerPage] = useState(25);
  const [tableOrder, setTableOrder] = useState("desc");
  const [tableOrderBy, setTableOrderBy] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [cronJobInfo, setCroneJobInfo] = useState({});
  const getLast7DaysUntilYesterday = () => {
    const end = new Date();
    end.setDate(end.getDate() - 1);
    const start = new Date(end);
    start.setDate(end.getDate() - 6);
    return [start, end];
  };
  const [dateRange, setDateRange] = useState(getLast7DaysUntilYesterday());
  const [partnerUsers, setPartnerUsers] = useState([]);
  const [startDate, endDate] = dateRange;
  const [styleIds, setStyleIds] = useState([]);
  const [selectedStyleIds, setSelectedStyleIds] = useState([]);
  const [totalRows, setTotalRows] = useState({ cells: [] });
  const [recordsTotal, setRecordsTotal] = useState(0);
  const [draw, setDraw] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const [showInUSD, setShowInUSD] = useState(false);
  const [domains, setDomains] = useState([]);
  const [subdomains, setSubdomains] = useState([]);
  const [selectedDomainId, setSelectedDomainId] = useState("");
  const [selectedSubdomainId, setSelectedSubdomainId] = useState("");
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [showPagination, setShowPagination] = useState(true); // New state to control pagination visibility
  const [articleOption, setArticleOptions] = useState([]);
  const [selectedArticleIds, setSelectedArticleIds] = useState([]);
  const [triggerSearch, setTriggerSearch] = useState(0);

  const breakPointOptions = [
    { value: "article", label: "Article" },
    { value: "channel", label: "Channel" },
    { value: "styleId", label: "Style IDs" },
    { value: "country", label: "Country" },
    { value: "date", label: "Date" },
  ];


  const fetchDomains = useCallback(async () => {
    try {
      const response = await apiClient.get("/Domain/GetDropDown");
      if (response?.status === 200) {
        setDomains([{ Id: "", Name: "All Domains" }, ...response?.data?.data]);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Error fetching domains");
      setDomains([]);
    }
  }, []);

  const fetchSubdomains = useCallback(async (domainId) => {
    try {
      const url = domainId
        ? `/SubDomain/GetDropDown?DomainId=${domainId}`
        : `/SubDomain/GetDropDown?`;
      const response = await apiClient.get(url);
      if (response?.status === 200) {
        setSubdomains(response?.data?.data);
      } else {
        setSubdomains([]);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Error fetching subdomains");
      setSubdomains([]);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const response = await apiClient.get("/Channals/GetDropdown");
      if (response?.status === 200) {
        setCustomChannels(response?.data?.data);
        setOptions(
          response?.data?.data?.map((channels) => ({
            label: channels?.DisplayName,
            value: channels?.Id,
          })),
        );
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);

  const fetchDataUsers = useCallback(async () => {
    try {
      const response = await apiClient.get("/adminuser/userDropdown");
      if (response?.status === 200) {
        setPartnerUsers(response?.data?.data);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
    }
  }, []);

  const fetchStyleIds = useCallback(async (userId = null) => {
    try {
      const url = userId
        ? `/StyleIds/getdropdown?userId=${userId}`
        : `/StyleIds/getdropdown`;
        const response = await apiClient.get(url);
      if (response?.status === 200) {
        setStyleIds(response?.data?.data || []);
        setSelectedStyleIds([]);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Internal server error");
      setStyleIds([]);
      setSelectedStyleIds([]);
    }
  }, []);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken");
    setToken(accessToken);
  }, []);

  useEffect(() => {
    fetchData();
    fetchDomains();
  }, [fetchData, fetchDomains]);

  useEffect(() => {
    if (["Admin", "Super Admin"]?.includes(userData?.User_Type)) {
      fetchDataUsers();
    }
  }, [fetchDataUsers, userData?.User_Type]);

  useEffect(() => {
    fetchStyleIds();
  }, [fetchStyleIds]);

  useEffect(() => {
    if (selectedDomainId) {
      fetchSubdomains(selectedDomainId);
    } else {
      setSubdomains([]);
      setSelectedSubdomainId("");
    }
  }, [selectedDomainId, fetchSubdomains]);

  const fetchChannelByUser = () => {
    if (["Super Admin", "Admin"].includes(userData?.User_Type)) {
      apiClient
        .get("/Channals/GetDropdown")
        .then((response) => {
          if (response?.status === 200 && response?.data?.data) {
            setCustomChannels(response.data.data || []);
          }
        })
        .catch((error) => {
          console.error(
            "Error fetching all channels:",
            error?.response?.data?.error || "Internal server error"
          );
        });
      return;
    }

    apiClient
      .get("/Channals/GetDropdown")
      .then((response) => {
        const channels = response?.data?.data || [];

        if (channels.length > 0) {
          setCustomChannelByUsers(channels);
          const channelOptions = channels.map((channel) => ({
            label: channel?.DisplayName,
            value: channel?.Id,
          }));
          setOptions(channelOptions);
        } else {
          setCustomChannelByUsers([]);
          setOptions([]);
        }
      })
      .catch((error) => {
        console.error(
          "Error fetching user channels:",
          error?.response?.data?.error || "Internal server error",
        );
      });
  };

  useEffect(() => {
    fetchChannelByUser();
  }, [fetchData, userData?.User_Type]);

  const fetchDataPartner = useCallback(async () => {
    try {
      const selectedPartnerData = selectedPartner
        ? partnerUsers?.find((user) => user?.Id === selectedPartner)
        : null;
      const effectiveUserType =
        selectedPartnerData?.User_Type || userData?.User_Type;

      if (["Super Admin", "Admin"].includes(userData?.User_Type)) {
        if (selectedPartner && selectedPartner !== "") {
          if (["Partner", "Account"].includes(effectiveUserType)) {
            const channelsResponse = await apiClient.get(
              `/Channals/GetDropdown`,
              {
                params: { userId: selectedPartner },
              }
            );            
            if (
              channelsResponse?.status === 200 &&
              channelsResponse?.data?.data &&
              channelsResponse?.data?.data.length > 0
            ) {
              const filteredChannels = channelsResponse?.data?.data;
              const channelOptions = filteredChannels.map((channel) => ({
                label: channel?.DisplayName,
                value: channel?.Id,
              }));
              setOptions(channelOptions);
            } else {
              setOptions([]);
            }
          } else {
            const allOptions = customChannels?.map((channels) => ({
              label: channels?.DisplayName,
              value: channels?.Id,
            }));
            setOptions(allOptions || []);
          }
        } else {
          const allOptions = customChannels?.map((channels) => ({
            label: channels?.DisplayName,
            value: channels?.Id,
          }));
          setOptions(allOptions);
        }
      } else if (["Partner", "Account"].includes(userData?.User_Type)) {
        const filteredOptions = customChannelByUsers?.map((channels) => ({
          label: channels?.DisplayName,
          value: channels?.Id,
        }));
        setOptions(filteredOptions);
      }
    } catch (error) {
      console.error(
        "Error in fetchDataPartner:",
        error?.response?.data?.error || "Internal server error",
      );
    }
  }, [
    customChannels,
    selectedPartner,
    customChannelByUsers,
    userData?.User_Type,
    partnerUsers,
    Token,
  ]);

  const fetchArticleOptions = useCallback(async (userId = null) => {
    try {
      const response = await apiClient.get("/article/GetDropdown", {
          params: userId ? { userId } : {}
        });
      if (response?.status === 200) {
        const options = response.data.data.map((article) => ({
          value: article.Id,
          label: article.Title,
        }));
        setArticleOptions(options);
      }
    } catch (error) {
      console.error(
        "Error fetching articles:",
        error?.response?.data?.error || "Internal server error"
      );
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load article options",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  useEffect(() => {
    fetchDataPartner();
  }, [fetchDataPartner]);
  useEffect(() => {
    fetchArticleOptions();
  }, [fetchArticleOptions]);
  const handleBreakPoints = (selectedValues) => {
    setSelectedBreakPoints(selectedValues);
    setTablePage(0);
    setDraw(1);
  };
  const addTimeOffset = (timeString, hours = 5, minutes = 30) => {
    // Convert string to Date
    const date = new Date(timeString);
  
    // Add offset
    date.setHours(date.getHours() + hours);
    date.setMinutes(date.getMinutes() + minutes);
  
    // Format back to a readable string
    return date.toLocaleString('en-GB', { 
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true 
    });
  };
 const searchApi = async (rowsPerPageOverride = null, newPage = null) => {
    if (isFetching) return;
    setIsFetching(true);
    setShowLoader(true);
    setReportsTableData([]);
    setTotalRows({ cells: [] });

    const selectedChannelOptions =
      selectedOptions["userIdCustomReport"]?.map((dataReport) => dataReport?.value) || [];
    let channelsToUse = selectedChannelOptions;
    // const backendOrderDir = tableOrder === "asc" ? "desc" : "asc";

    let breakPointsToSend;
    if (selectedBreakPoints.length === breakPointOptions.length) {
      breakPointsToSend = "all";
    } else if (selectedBreakPoints.length === 0) {
      breakPointsToSend = "";
    } else {
      // Define the strict backend order (same as RCAnalytics)
      const backendOrder = ['date', 'country', 'article', 'channel', 'styleId'];
      
      // Sort selected breakpoints according to backend order
      const sortedBreakPoints = selectedBreakPoints.sort((a, b) => {
        return backendOrder.indexOf(a) - backendOrder.indexOf(b);
      });
      
      // Map to backend field names
      const breakPointsMap = {
        date: "date",
        country: "country",
        article: "article",
        channel: "channel",
        styleId: "styleId",
      };
      breakPointsToSend = sortedBreakPoints
        .map((bp) => breakPointsMap[bp])
        .filter(Boolean)
        .join(",");
    }

    const column = tableColumns.find((col) => col.id === tableOrderBy);
    const backendOrderBy = column ? column.field : "estimated_earnings";

    let effectiveRowsPerPage = tableRowsPerPage;
    if (rowsPerPageOverride !== null) {
      effectiveRowsPerPage =
        typeof rowsPerPageOverride === "number"
          ? rowsPerPageOverride
          : typeof rowsPerPageOverride === "object" && rowsPerPageOverride.target?.value
          ? parseInt(rowsPerPageOverride.target.value, 10)
          : tableRowsPerPage;
    }

    const pageToSend = newPage !== null ? newPage + 1 : tablePage + 1;

    try {
      const response = await apiClient.post(
        "/Revenue/Get",
        {
          channelId: channelsToUse.join(","),
          styleId: selectedStyleIds.join(","),
          articleId: selectedArticleIds.join(","),
          domainId: selectedDomainId || "",
          subdomainId: selectedSubdomainId || "",
        },
        {
          params: {
            startDate: formatDate(startDate),
            endDate: formatDate(endDate),
            breakPoints: breakPointsToSend,
            orderBy: backendOrderBy,
            orderDir: tableOrder,
            page: pageToSend,
            length: effectiveRowsPerPage,
            search: searchTerm || "",
            InrtoUsd: showInUSD,
            draw,
            userId: selectedPartner || "",
            domainId: selectedDomainId || "",
            subdomainId: selectedSubdomainId || "",
          },
          paramsSerializer: {
            indexes: null, // optional: keeps arrays as comma-separated instead of indexed
          },
        }
      );

      if (response?.status === 200 && response.data.success) {
        const data = response.data.data || [];
        const summary = response.data.summary || {};
        const pagination = response.data.pagination || null;
        const cronJobInfo = response.data.cronJobInfo || {};

        const updatedCronJobInfo = {
          "Revenue Data": addTimeOffset(cronJobInfo["Revenue Data"])
        };

        setCroneJobInfo(updatedCronJobInfo);
        // Determine if pagination should be shown
        const today = new Date();
        const isTodayOrYesterday =
          formatDate(startDate) === formatDate(today) ||
          formatDate(startDate) === formatDate(new Date(today.setDate(today.getDate() - 1)));
        setShowPagination(!!pagination && !isTodayOrYesterday);

        if (data.length === 0) {
          Swal.fire({
            title: "No Data Found",
            icon: "info",
            confirmButtonColor: "#5750f1",
          });
          setReportsTableData([]);
          setTotalRows({ cells: [] });
          setRecordsTotal(0);
          setDraw(draw + 1);
          setIsFetching(false);
          setShowLoader(false);
          return;
        }

        setRecordsTotal(pagination?.recordsTotal || data.length);
        setDraw(draw + 1);

        const dimensionColumns = [];
        if (breakPointsToSend === "all" || selectedBreakPoints.includes("date")) {
          dimensionColumns.push({ name: "DATE", type: "DIMENSION" });
        }
        if (breakPointsToSend === "all" || selectedBreakPoints.includes("country")) {
          dimensionColumns.push({ name: "COUNTRY", type: "DIMENSION" });
        }
        if (breakPointsToSend === "all" || selectedBreakPoints.includes("article")) {
          dimensionColumns.push({ name: "ARTICLE", type: "DIMENSION" }); // Changed from PLATFORM to ARTICLE
        }
        if (breakPointsToSend === "all" || selectedBreakPoints.includes("channel")) {
          dimensionColumns.push({ name: "CHANNEL", type: "DIMENSION" });
        }
        if (breakPointsToSend === "all" || selectedBreakPoints.includes("styleId")) {
          dimensionColumns.push({ name: "STYLEID", type: "DIMENSION" });
        }

        const metricColumns = [
          { 
            name: "ESTIMATEDEARNINGS", 
            type: "METRIC", 
            currencyCode: showInUSD ? "USD" : "INR"
          },
          { name: "IMPRESSIONS", type: "METRIC" },
          { name: "IMPRESSIONSRPM", type: "METRIC" },
          { name: "CLICKS", type: "METRIC" },
          { name: "IMPRESSIONSCTR", type: "METRIC" },
          { name: "COSTPERCLICK", type: "METRIC" , currencyCode: showInUSD ? "USD" : "INR"},
        ];

        const headers = [...dimensionColumns, ...metricColumns];
        const fieldMap = {
          DATE: "date",
          COUNTRY: "country",
          ARTICLE: "article",
          CHANNEL: "channel",
          STYLEID: "styleid",
          ESTIMATEDEARNINGS: "estimated_earnings",
          IMPRESSIONS: "impressions",
          IMPRESSIONSRPM: "rpm",
          CLICKS: "clicks",
          IMPRESSIONSCTR: "impressionsctr",
          COSTPERCLICK: "cpc",
        };

        const rows = data.map((item, index) => {
          const rowCells = headers.map((header) => {
            let value;
            const fieldName = fieldMap[header.name];
            if (header.name === "CHANNEL") {
              value = item[fieldName] != null ? item[fieldName] : "N/A";
            } else if (header.type === "METRIC" && header.name === "ESTIMATEDEARNINGS") {
              const rawValue = item[fieldName];
              value = rawValue != null ? rawValue.toString() : "0";
            } else {
              value = item[fieldName] != null ? item[fieldName].toString() : "";
            }
            return { value };
          });
          return { id: index, cells: rowCells };
        });

        // Use summary object for totals
        const totals = {
          cells: headers.map((header) => {
            if (header.type === "METRIC") {
              switch (header.name) {
                case "ESTIMATEDEARNINGS":
                  return {
                    value: summary.estimated_earnings
                      ? `${getCurrency(header.currencyCode)}${parseFloat(summary.estimated_earnings).toFixed(2)}`
                      : `${getCurrency(header.currencyCode)}0.00`,
                  };
                case "IMPRESSIONS":
                  return {
                    value: summary.impressions
                      ? parseFloat(summary.impressions).toLocaleString()
                      : "0",
                  };
                case "IMPRESSIONSRPM":
                  return {
                    value: summary.rpm ? parseFloat(summary.rpm).toFixed(2) : "0.00",
                  };
                case "CLICKS":
                  return {
                    value: summary.clicks
                      ? parseFloat(summary.clicks).toLocaleString()
                      : "0",
                  };
                case "IMPRESSIONSCTR":
                  return {
                    value: summary.impressionsctr
                      ? `${parseFloat(summary.impressionsctr).toFixed(2)}%`
                      : "0%",
                  };
                case "COSTPERCLICK":
                  return {
                    value: summary.cpc
                      ? `${getCurrency(header.currencyCode)}${parseFloat(summary.cpc).toFixed(2)}`
                      : `${getCurrency(header.currencyCode)}0.00`,
                  };
                default:
                  return { value: "" };
              }
            }
            return { value: "" };
          }),
        };

        setReportsTableData([{ headers, rows, totals, originalData: data }]);
        setTotalRows(totals);
      } else {
        setReportsTableData([]);
        setTotalRows({ cells: [] });
        setRecordsTotal(0);
        Swal.fire({
          title: "No Data Found",
          icon: "info",
          confirmButtonColor: "#5750f1",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "No Data Found",
        text: error.message || error?.response?.data?.error,
        icon: "error",
        confirmButtonColor: "#5750f1",
      });
      setReportsTableData([]);
      setTotalRows({ cells: [] });
      setRecordsTotal(0);
    } finally {
      setIsFetching(false);
      setShowLoader(false);
    }
  };

  const handleTablePageChange = async (newPage) => {
    setTablePage(newPage);
    setReportsTableData([]);
    setTotalRows({ cells: [] });
    setTriggerSearch(prev => prev + 1);
    // await searchApi(null, newPage);
  };

  const handleTableRowsPerPageChange = async (newRowsPerPage) => {
    const rowsPerPageValue =
      typeof newRowsPerPage === "object" && newRowsPerPage.target?.value
        ? parseInt(newRowsPerPage.target.value, 10)
        : typeof newRowsPerPage === "number"
        ? newRowsPerPage
        : tableRowsPerPage;
    setTableRowsPerPage(rowsPerPageValue);
    setTablePage(0);
    setDraw(1);
    setReportsTableData([]);
    setTotalRows({ cells: [] });
    // await searchApi(rowsPerPageValue);
  };

  // const handleTableRequestSort = async (_, property) => {
  //   const isAsc = tableOrderBy === property && tableOrder === "asc";
  //   setTableOrder(isAsc ? "desc" : "asc");
  //   setTableOrderBy(property);
  //   setTablePage(0);
  //   setDraw(1);
  //   setReportsTableData([]);
  //   setTotalRows({ cells: [] });
  //   await searchApi();
  // };
  const handleTableRequestSort = async (_, property) => {
    // Use direct field names instead of col_0, col_1, etc.
    const isAsc = tableOrderBy === property && tableOrder === "asc";
    setTableOrder(isAsc ? "desc" : "asc");
    setTableOrderBy(property); // This will now be the actual field name like "date", "channel", etc.
    setTablePage(0);
    setDraw(1); 
    setReportsTableData([]);
    setTotalRows({ cells: [] });
    
    console.log("Sorting by:", property);
    console.log("Order:", isAsc ? "desc" : "asc");
    setTriggerSearch(prev => prev + 1);
    // await searchApi();
};
  const handleTableSearchChange = (term) => {
    setSearchTerm(term);
    setTablePage(0);
    setDraw(1);
    setReportsTableData([]);
    setTotalRows({ cells: [] });
    setTriggerSearch(prev => prev + 1);
    // searchApi();
  };

  const handleQuickDateSelect = (range) => {
    const today = new Date();
    let startDate, endDate;

    switch (range) {
      case "today":
        startDate = new Date(today);
        endDate = new Date(today);
        break;
      case "yesterday":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 1);
        endDate = new Date(startDate);
        break;
      case "thismonth":
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate =
          today?.getDate() >
          new Date(today.getFullYear(), today.getMonth() + 1, 0)?.getDate()
            ? new Date(today.getFullYear(), today.getMonth() + 1, 0)
            : today;
        break;
      case "lastmonth":
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case "last30days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29);
        endDate = new Date(today);
        break;
      case "last7days":
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6);
        endDate = new Date(today);
        break;
      default:
        startDate = null;
        endDate = null;
        break;
    }
    if (startDate && endDate) {
      setDateRange([startDate, endDate]);
      setTablePage(0);
      setDraw(1);
      setReportsTableData([]);
      setTotalRows({ cells: [] });
    }
  };

  // const transformDataForTable = useCallback(() => {
  //   if (!reportsTableData?.length) {
  //     setTableColumns([]);
  //     setTableData([]);
  //     setTotalRows({ cells: [] });
  //     return;
  //   }

  //   const headers = reportsTableData[0]?.headers || [];

  //   const columnNameMap = {
  //     DATE: "Date",
  //     ARTICLE: "Article",
  //     COUNTRY: "Country",
  //     CHANNEL: "Custom Channel",
  //     STYLEID: "Style ID",
  //     ESTIMATEDEARNINGS: "Estimated Earnings",
  //     IMPRESSIONS: "Impressions",
  //     IMPRESSIONSRPM: "Impressions RPM",
  //     CLICKS: "Clicks",
  //     IMPRESSIONSCTR: "Impressions CTR",
  //     COSTPERCLICK: "RPC",
  //   };
  //   const updatedHeaders = headers.map(header => {
  //     if (header.name === "ESTIMATEDEARNINGS" || header.name === "COSTPERCLICK") {
  //       return {
  //         ...header,
  //         currencyCode: showInUSD ? "USD" : "INR"
  //       };
  //     }
  //     return header;
  //   });
  //   const columns = updatedHeaders.map((header, index) => ({
  //     id: `col_${index}`,
  //     label: columnNameMap[header.name] || header.name.toLowerCase().replaceAll("_", " "),
  //     field: header.name.toLowerCase(),
  //   }));

  //   setTableColumns(columns);

  //   const combinedData = reportsTableData[0]?.rows || [];
  //   const transformedRows = combinedData.map((row, index) => {
  //     const rowData = { Id: row.id };
  //     row.cells?.forEach((cell, cellIndex) => {
  //       let value = cell?.value || "";
  //       const header = headers[cellIndex];
  //       const isCurrencyColumn = header?.currencyCode && header.name === "ESTIMATEDEARNINGS";
  //       const isMetricRatio = header?.name === "IMPRESSIONSCTR";

  //       if (header?.name === "DATE") {
  //         const originalData = reportsTableData[0]?.originalData[index];
  //         const dateString = originalData?.date || value;
  //         if (dateString) {
  //           value = dateString.split("T")[0];
  //         }
  //       } else if (header.name === "CHANNEL") {
  //         const originalData = reportsTableData[0]?.originalData[index];
  //         const channelName = originalData?.channel || value;
  //         value = channelName != null ? channelName : "N/A";
  //       } else if (header.name === "STYLEID") {
  //         const originalData = reportsTableData[0]?.originalData[index];
  //         const styleName = originalData?.styleid || value;
  //         value = styleName != null ? styleName : "N/A";
  //       } else if (header.name === "ARTICLE") {
  //         const originalData = reportsTableData[0]?.originalData[index];
  //         const articleName = originalData?.article || value;
  //         value = articleName != null ? articleName : "N/A";
  //       } else if (isCurrencyColumn) {
  //         const numericValue = parseFloat(value);
  //         value = isNaN(numericValue)
  //           ? `${getCurrency(header.currencyCode)}0.00`
  //           : `${getCurrency(header.currencyCode)}${numericValue.toFixed(2)}`;
  //       } else if (isMetricRatio) {
  //         const numericValue = parseFloat(value);
  //         value = isNaN(numericValue) || numericValue === 0 ? "0%" : `${numericValue.toFixed(2)}%`;
  //       } else if (
  //         header?.type === "METRIC" &&
  //         !isMetricRatio &&
  //         header?.name !== "ESTIMATEDEARNINGS"
  //       ) {
  //         const numericValue = parseFloat(value);
  //         value = isNaN(numericValue) ? "0" : numericValue.toLocaleString();
  //       }

  //       rowData[`col_${cellIndex}`] = value;
  //     });

  //     return rowData;
  //   });

  //   setTableData(transformedRows);
  //   setTotalRows(reportsTableData[0]?.totals || { cells: [] });
  // }, [reportsTableData]);
  const transformDataForTable = useCallback(() => {
    if (!reportsTableData?.length) {
        setTableColumns([]);
        setTableData([]);
        setTotalRows({ cells: [] });
        return;
    }

    const headers = reportsTableData[0]?.headers || [];

    const columnNameMap = {
        DATE: "Date",
        ARTICLE: "Article", 
        COUNTRY: "Country",
        CHANNEL: "Custom Channel",
        STYLEID: "Style ID",
        ESTIMATEDEARNINGS: "Estimated Earnings",
        IMPRESSIONS: "Impressions",
        IMPRESSIONSRPM: "Impressions RPM",
        CLICKS: "Clicks",
        IMPRESSIONSCTR: "Impressions CTR",
        COSTPERCLICK: "RPC",
    };

    const updatedHeaders = headers.map(header => {
        if (header.name === "ESTIMATEDEARNINGS" || header.name === "COSTPERCLICK") {
            return {
                ...header,
                currencyCode: showInUSD ? "USD" : "INR"
            };
        }
        return header;
    });

    // Use direct field names instead of col_0, col_1
    const columns = updatedHeaders.map((header, index) => ({
        id: header.name.toLowerCase(), // Use field name as ID: "date", "channel", etc.
        label: columnNameMap[header.name] || header.name.toLowerCase().replaceAll("_", " "),
        field: header.name.toLowerCase(), // Direct field name
    }));

    setTableColumns(columns);

    const combinedData = reportsTableData[0]?.rows || [];
    const transformedRows = combinedData.map((row, index) => {
        const rowData = { Id: row.id };
        row.cells?.forEach((cell, cellIndex) => {
            let value = cell?.value || "";
            const header = headers[cellIndex];
            const isCurrencyColumn = header?.currencyCode && header.name === "ESTIMATEDEARNINGS";
            const isMetricRatio = header?.name === "IMPRESSIONSCTR";

            if (header?.name === "DATE") {
                const originalData = reportsTableData[0]?.originalData[index];
                const dateString = originalData?.date || value;
                if (dateString) {
                    value = dateString.split("T")[0];
                }
            } else if (header.name === "CHANNEL") {
                const originalData = reportsTableData[0]?.originalData[index];
                const channelName = originalData?.channel || value;
                value = channelName != null ? channelName : "N/A";
            } else if (header.name === "STYLEID") {
                const originalData = reportsTableData[0]?.originalData[index];
                const styleName = originalData?.styleid || value;
                value = styleName != null ? styleName : "N/A";
            } else if (header.name === "ARTICLE") {
                const originalData = reportsTableData[0]?.originalData[index];
                const articleName = originalData?.article || value;
                value = articleName != null ? articleName : "N/A";
            } else if (isCurrencyColumn) {
                const numericValue = parseFloat(value);
                value = isNaN(numericValue)
                    ? `${getCurrency(header.currencyCode)}0.00`
                    : `${getCurrency(header.currencyCode)}${numericValue.toFixed(2)}`;
            } else if (isMetricRatio) {
                const numericValue = parseFloat(value);
                value = isNaN(numericValue) || numericValue === 0 ? "0%" : `${numericValue.toFixed(2)}%`;
            } else if (
                header?.type === "METRIC" &&
                !isMetricRatio &&
                header?.name !== "ESTIMATEDEARNINGS"
            ) {
                const numericValue = parseFloat(value);
                value = isNaN(numericValue) ? "0" : numericValue.toLocaleString();
            }

            // Use field name as key instead of col_0, col_1
            rowData[header.name.toLowerCase()] = value;
        });

        return rowData;
    });

    setTableData(transformedRows);
    setTotalRows(reportsTableData[0]?.totals || { cells: [] });
}, [reportsTableData, showInUSD]);
  useEffect(() => {
    transformDataForTable();
  }, [transformDataForTable]);

  // Use useEffect to trigger searchApi when relevant state changes
  useEffect(() => {
    // Don't search on initial render, only when triggered by user actions
    if (triggerSearch > 0) {
      searchApi();
    }
  }, [
    triggerSearch, 
    tableOrder, 
    tableOrderBy, 
    tablePage, 
    tableRowsPerPage, 
    searchTerm,
    // Add other dependencies that should trigger search
  ]);
  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">Live Revenue</h1>
          </div>
        </div>
        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
         <div className="mb-6">
          <div className="flex flex-col gap-4">
            {/* Main filters row */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 2xl:grid-cols-5  items-end">
              {/* Currency Toggle */}
              <div className="flex flex-col gap-1 space-y-2">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                  Currency
                </h3>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    INR
                  </span>
                  <label className="relative inline-flex cursor-pointer items-center">
                    <input
                      type="checkbox"
                      checked={showInUSD}
                      onChange={() => setShowInUSD(!showInUSD)}
                      className="peer sr-only"
                    />
                    <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-primary/25"></div>
                  </label>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    USD
                  </span>
                </div>
              </div>

              {/* Breakpoints */}
              <div className="min-w-0 z-999">
                <MultiSelectDropdown
                  label="Breakpoints"
                  placeholder="Select Breakpoints"
                  options={breakPointOptions}
                  value={selectedBreakPoints}
                  onChange={handleBreakPoints}
                  displayKey="label"
                  idKey="value"
                  className="w-full"
                />
              </div>

              {/* Users dropdown (for Admin/Super Admin) */}
              {["Admin", "Super Admin"]?.includes(userData?.User_Type) && (
                <div className="min-w-0 z-99">
                  <SearchableDropdown
                    label="All Users"
                    placeholder="Select Users..."
                    options={[{ Id: "", Name: "All Users" }, ...partnerUsers]}
                    value={selectedPartner}
                    onChange={(partner) => {
                      setSelectedPartner(partner?.Id || "");
                      fetchStyleIds(partner?.Id || null);
                      fetchArticleOptions(partner?.Id || "")
                      setTablePage(0);
                      setDraw(1);
                      setReportsTableData([]);
                      setTotalRows({ cells: [] });
                    }}
                    displayKey="Name"
                    idKey="Id"
                    className="w-full"
                  />
                </div>
              )}

              {/* Date Range Picker */}
              <div className="min-w-0 z-30">
                <DateRangePicker
                  startDate={startDate}
                  endDate={endDate}
                  onDateChange={(selectedDates) => {
                    setDateRange(selectedDates);
                    setTablePage(0);
                    setDraw(1);
                    setReportsTableData([]);
                    setTotalRows({ cells: [] });
                  }}
                  onQuickDateSelect={handleQuickDateSelect}
                  label="Date Range"
                  placeholder="Select date range"
                  showQuickSelectButtons={true}
                  className="w-full"
                />
              </div>

              {/* Empty cell for desktop layout */}
              <div className="hidden 2xl:flex items-center gap-2">
                <button
                  onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                  className="flex h-[40px] w-[40px] items-center justify-center rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                  title={isFilterExpanded ? "Hide Filters" : "More Filters"}
                >
                  <FaFilter size={16} />
                </button>
                <Button
                  label="Search"
                  variant="primary"
                  shape="rounded"
                  className="flex items-center w-[100%]  justify-center gap-2 rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 disabled:opacity-50 md:w-400px]"
                  onClick={searchApi}
                  disabled={showLoader || isFetching}
                />
              </div>
            </div>

            {/* Expanded Filters - appears before buttons on mobile */}
            {isFilterExpanded && (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 2xl:grid-cols-5 xl:grid-cols-4 items-end">
                {/* Domain */}
                <div className="min-w-0 z-50">
                  <SearchableDropdown
                    label="Domain"
                    placeholder="Select Domain"
                    options={domains}
                    value={selectedDomainId}
                    onChange={(item) => {
                      setSelectedDomainId(item?.Id || "");
                      setSelectedSubdomainId("");
                      setTablePage(0);
                      setDraw(1);
                      setReportsTableData([]);
                      setTotalRows({ cells: [] });
                    }}
                    className="w-full"
                    displayKey="Name"
                    displayKey2="ShowUrlName"
                    idKey="Id"
                  />
                </div>

                {/* SubDomain */}
                <div className="min-w-0 z-40">
                  <SearchableDropdown
                    label="Subdomain"
                    placeholder={selectedDomainId ? "Select Subdomain" : "Select a domain first"}
                    options={[{ Id: "", Name: "All Subdomains" }, ...subdomains]}
                    value={selectedSubdomainId}
                    onChange={(item) => {
                      setSelectedSubdomainId(item?.Id || "");
                      setTablePage(0);
                      setDraw(1);
                      setReportsTableData([]);
                      setTotalRows({ cells: [] });
                    }}
                    displayKey="Name"
                    idKey="Id"
                    disabled={!selectedDomainId}
                    className="w-full"
                  />
                </div>

                {/* Style IDs */}
                <div className="min-w-0 z-30">
                  <MultiSelectDropdown
                    label="Style IDs"
                    placeholder="Select Style IDs"
                    options={
                      styleIds?.map((style) => ({
                        value: style?.Id,
                        label: style?.Name,
                      })) || []
                    }
                    value={selectedStyleIds}
                    onChange={(selectedValues) => {
                      setSelectedStyleIds(selectedValues);
                      setTablePage(0);
                      setDraw(1);
                      setReportsTableData([]);
                      setTotalRows({ cells: [] });
                    }}
                    displayKey="label"
                    idKey="value"
                    className="w-full"
                  />
                </div>

                {/* Custom Channels */}
                <div className="min-w-0 z-20">
                  <MultiSelectDropdown
                    label="Custom Channels"
                    placeholder="Select Channels"
                    options={options}
                    value={
                      selectedOptions["userIdCustomReport"]?.map(
                        (item) => item.value,
                      ) || []
                    }
                    onChange={(selectedValues) => {
                      const selectedItems = options.filter((option) =>
                        selectedValues.includes(option.value),
                      );
                      setSelectedOptions((prev) => ({
                        ...prev,
                        userIdCustomReport: selectedItems,
                      }));
                      setTablePage(0);
                      setDraw(1);
                      setReportsTableData([]);
                      setTotalRows({ cells: [] });
                    }}
                    displayKey="label"
                    idKey="value"
                    className="w-full"
                  />
                </div>
                <div className="min-w-0 z-10">
                  <MultiSelectDropdown
                    label="Articles"
                    placeholder="Select Articles"
                    options={articleOption}
                    value={selectedArticleIds}
                    onChange={setSelectedArticleIds}
                    displayKey="label"
                    idKey="value"
                    className="w-full"
                  />
                </div>
              </div>
            )}

            {/* Filter Toggle and Apply Button - Mobile (appears last) */}
            <div className="flex justify-end items-center gap-2 2xl:hidden">
              <button
                onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                className="flex h-[40px] w-[40px] items-center justify-center rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                title={isFilterExpanded ? "Hide Filters" : "More Filters"}
              >
                <FaFilter size={16} />
              </button>
              <Button
                label="Search"
                variant="primary"
                shape="rounded"
                className="flex items-center md:w-[400px] justify-center gap-2 rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 disabled:opacity-50 w-full"
                onClick={searchApi}
                disabled={showLoader || isFetching}
              />
            </div>
          </div>
        </div>
        {cronJobInfo && Object.keys(cronJobInfo).length > 0 && (
          <div className="mt-2 px-4 mb-3 py-2 overflow-x-auto rounded-lg font-bold text-sm">
            <marquee behavior="scroll" direction="left">
              Last Updated : Revenue Data : {cronJobInfo["Revenue Data"]} 
            </marquee>
          </div>
        )}
          {showLoader && reportsTableData?.length === 0 && (
            <div className="mt-6 flex items-center justify-center">
              <CircularProgress size={40} />
            </div>
          )}

          {reportsTableData?.length > 0 && (
            <div className="mt-6">
              <CustomDataTable
                isLoading={showLoader}
                columns={tableColumns}
                rows={tableData}
                searchTerm={searchTerm}
                onSearchChange={handleTableSearchChange}
                page={tablePage}
                rowsPerPage={tableRowsPerPage}
                onPageChange={handleTablePageChange}
                onRowsPerPageChange={handleTableRowsPerPageChange}
                totalCount={recordsTotal}
                order={tableOrder}
                orderBy={tableOrderBy}
                onRequestSort={handleTableRequestSort}
                notShowAction={true}
                totals={totalRows}
                showPagination={showPagination} // Pass showPagination prop
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default Revenue;