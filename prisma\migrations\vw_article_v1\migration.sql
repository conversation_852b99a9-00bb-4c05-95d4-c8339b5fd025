-- public.vw_article_v1 source

CREATE OR REPLACE VIEW public.vw_article_v1
AS SELECT c."Name" AS "CategoryName",
    c."ShowUrlName" AS "CategoryUrl",
    d."Name" AS "DomainName",
    d."ShowUrlName" AS "DomainUrl",
    d."GId",
    d."AWId",
    d."SendTo",
    d."HeadTagScript",
    d."HeadTagScriptLandingPage",
    d."HeadTagScriptSearchPage",
    COALESCE(stylelm."StyleId"::bigint, d."StyleIdLm") AS styleidlm,
    COALESCE(styledm."StyleId"::bigint, d."StyleIdDm") AS styleiddm,
    sd."Name" AS "SubDomainName",
    (sd."Url" || '.'::text) || d."ShowUrlName" AS "SubDomainUrl",
    sd."GId" AS "SubGId",
    sd."AWId" AS "SubAWId",
    sd."SendTo" AS "SubSendTo",
    sd."HeadTagScript" AS "SubHeadTagScript",
    sd."HeadTagScriptLandingPage" AS "SubHeadTagScriptLandingPage",
    sd."HeadTagScriptSearchPage" AS "SubHeadTagScriptSearchPage",
    cc."ReportingDimensionId" AS "ChannalName",
    a."Id",
    a."Title",
    a."Url",
    a."MetaTitle",
    a."MetaDescription",
    a."MetaKeys",
    a."Image",
    a."ShowUrlName",
    a."ShowsAds",
    a."UpdatedAt",
    a."CreatedAt",
    a."ReadTime",
    COALESCE(NULLIF(TRIM(BOTH FROM au."DisplayName"), ''::text), au."Name") AS "CreatedBy",
    au."AboutMe",
    au."ProfilePic" AS "ProfileImage",
    a."Description",
    a."ShortDescription",
    d."WideLogo",
    d."SquareLogo",
    a."AdRelatedSearches",
    a."CheckRelatedSearches2",
    a."AdReletadSearches2",
    d."ContactEmail",
    d."Address",
    a."Published",
    a."ShowArticle",
    a."ShowInDomain"
   FROM "ArticleDetails" a
     LEFT JOIN "Domain" d ON a."Domain" = d."Id"
     LEFT JOIN "SubDomain" sd ON a."SubDomain" = sd."Id"
     LEFT JOIN "Category" c ON a."Category" = c."Id"
     LEFT JOIN "Channals" cc ON a."CustomChannal" = cc."Id"
     LEFT JOIN "StyleIds" stylelm ON a."StyleIdLm" = stylelm."Id"
     LEFT JOIN "StyleIds" styledm ON a."StyleIdDm" = styledm."Id"
     LEFT JOIN "AdminUser" au ON au."Id" = a."User_Id_Settings"
  WHERE COALESCE(a."IsDeleted", false) = false AND COALESCE(a."Published", false) = true;