// import { prisma } from '@/lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

// interface RevenueCampaignData {
//     articleid?: string;
//     articlename?: string;
//     date?: Date;
//     rv_revenue: number;
//     rv_rpc: number;
//     profit: number;
//     roi: number;
//     profit_gst: number;
//     roi_gst: number;
//     ads_conversions: number;
//     ads_click: number;
//     ads_spend: number;
//     ads_spend_with_gst: number;
//     ads_cpa: number;
//     ads_cpc: number;
//     channelid?: string;
//     channelname?: string;
//     country?: string;
// }

// interface GeographicMetrics {
//     conversions: number;
//     cost: number;
//     impressions: number;
//     averageCpc: number;
//     clicks: number;
// }

// interface GeographicData {
//     date: string;
//     countryCriterionId: string;
//     metrics: GeographicMetrics;
// }

// interface ApiResponseData {
//     [articleId: string]: {
//         geographic_data: GeographicData[];
//     };
// }

// interface RevenueApiResponse {
//     data: Array<{
//         DATE: string;
//         ESTIMATED_EARNINGS: string;
//         IMPRESSIONS: string;
//         IMPRESSIONS_RPM: string;
//         CLICKS: string;
//         IMPRESSIONS_CTR: string;
//         COST_PER_CLICK: string;
//         CUSTOM_CHANNEL_ID: string;
//         CUSTOM_SEARCH_STYLE_ID: string;
//         COUNTRY_CODE?: string;
//     }>;
// }

// interface AggregatedRevenueData {
//     [articleId: string]: {
//         estimatedEarnings: number;
//         revenueClicks: number;
//         countryData?: {
//             [countryCode: string]: {
//                 estimatedEarnings: number;
//                 revenueClicks: number;
//             };
//         };
//     };
// }

// interface UserInfo {
//     Id: string;
//     User_Type: string | null;
//     styleId?: string;
// }

// async function getUSDtoINRRate(): Promise<number> {
//     try {
//         const today = new Date();

//         const rateRecord = await prisma.ads_RateConversion.findFirst({
//             where: {
//                 Date: today
//             },
//             select: {
//                 USDtoINR: true
//             },
//         });

//         if (!rateRecord) {
//             const latestRateRecord = await prisma.ads_RateConversion.findFirst({
//                 select: {
//                     USDtoINR: true
//                 },
//                 orderBy: {
//                     Date: 'desc'
//                 }
//             });

//             return latestRateRecord?.USDtoINR ? Number(latestRateRecord.USDtoINR) : 83.0;
//         }

//         return rateRecord.USDtoINR ? Number(rateRecord.USDtoINR) : 83.0;
//     } catch (error) {
//         console.error('Error fetching USD to INR rate:', error);
//         return 83.0;
//     }
// }

// function convertRevenueToINR(data: RevenueCampaignData[], usdToInrRate: number): RevenueCampaignData[] {
//     return data.map(row => {
//         const convertedRow = { ...row };

//         if (convertedRow.rv_revenue !== undefined) {
//             convertedRow.rv_revenue = convertedRow.rv_revenue / usdToInrRate;
//         }

//         if (convertedRow.rv_rpc !== undefined) {
//             convertedRow.rv_rpc = convertedRow.rv_rpc / usdToInrRate;
//         }

//         if (convertedRow.profit !== undefined) {
//             convertedRow.profit = convertedRow.profit / usdToInrRate;
//         }

//         if (convertedRow.profit_gst !== undefined) {
//             convertedRow.profit_gst = convertedRow.profit_gst / usdToInrRate;
//         }

//         if (convertedRow.ads_spend !== undefined) {
//             convertedRow.ads_spend = convertedRow.ads_spend / usdToInrRate;
//         }

//         if (convertedRow.ads_spend_with_gst !== undefined) {
//             convertedRow.ads_spend_with_gst = convertedRow.ads_spend_with_gst / usdToInrRate;
//         }

//         if (convertedRow.ads_cpa !== undefined) {
//             convertedRow.ads_cpa = convertedRow.ads_cpa / usdToInrRate;
//         }

//         if (convertedRow.ads_cpc !== undefined) {
//             convertedRow.ads_cpc = convertedRow.ads_cpc / usdToInrRate;
//         }

//         return convertedRow;
//     });
// }

// async function getBaseUrlFromDatabase(): Promise<string> {
//     try {
//         const settings = await prisma.adminUserSetting.findFirst({
//             select: {
//                 PublicBaseUrl: true
//             }
//         });

//         if (!settings?.PublicBaseUrl) {
//             throw new Error('Base URL not found in admin user settings');
//         }

//         return settings.PublicBaseUrl;
//     } catch (error) {
//         console.error('Error fetching base URL from database:', error);
//         return process.env.NEXT_PUBLIC_BASE_URL || '';
//     }
// }

// async function fetchFromCampaignApi(articleIds: string[], channelIds: string[], startDate: string, endDate: string): Promise<ApiResponseData> {
//     try {
//         const baseUrl = await getBaseUrlFromDatabase();
//         const apiUrl = `http://localhost:3000/api/RCAnalytics/GetFromApi`;

//         const bodyPayload = {
//             articleIds: articleIds.join(','),
//             channelIds: channelIds.join(','),
//             startDate,
//             endDate
//         };

//         const response = await fetch(apiUrl, {
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json',
//                 'Authorization': `Bearer ${process.env.API_AUTH_TOKEN}`
//             },
//             body: JSON.stringify(bodyPayload),
//         });

//         if (!response.ok) {
//             const errorData = await response.json().catch(() => ({}));
//             throw new Error(`API request failed with status ${response.status}`);
//         }

//         return await response.json();
//     } catch (error) {
//         console.error('Failed to fetch data from external API:', error);
//         throw new Error('Failed to retrieve data from external API');
//     }
// }

// async function fetchFromRevenueApi(
//     req: NextRequest,
//     processedGroupBy: string[],
//     articleIds: string[],
//     channelIds: string[],
//     startDate?: string,
//     endDate?: string
// ): Promise<AggregatedRevenueData> {
//     try {
//         const baseUrl = await getBaseUrlFromDatabase();
//         const revenueApiUrl = `http://localhost:3000/api/RCAnalytics/RevenueFromAPi`;
//         const aggregatedRevenueData: AggregatedRevenueData = {};

//         let breakpoints = {
//             country: false,
//             platform: false,
//             date: true,
//             customChannel: true,
//             styleId: true
//         };

//         if (processedGroupBy.includes('article')) {
//             breakpoints.customChannel = true;
//             breakpoints.styleId = true;
//         }
//         if (processedGroupBy.includes('country')) {
//             breakpoints.customChannel = true;
//             breakpoints.styleId = true;
//             breakpoints.country = true;
//         }

//         const cookies = req.headers.get('cookie') || '';

//         for (const articleId of articleIds) {
//             try {
//                 const requestBody: any = {
//                     selectedChannels: [],
//                     selectedStyles: [],
//                     articleId: articleId,
//                     channelIds: channelIds
//                 };

//                 const response = await fetch(
//                     `${revenueApiUrl}?breakPoints=${encodeURIComponent(
//                         JSON.stringify(breakpoints)
//                     )}&InrtoUsd=true&startDate=${startDate}&endDate=${endDate}`,
//                     {
//                         method: 'POST',
//                         headers: {
//                             'Content-Type': 'application/json',
//                             'Cookie': cookies,
//                             'Authorization': req.headers.get('authorization') || ''
//                         },
//                         body: JSON.stringify(requestBody)
//                     }
//                 );

//                 if (!response.ok) {
//                     continue;
//                 }

//                 const revenueResponse: RevenueApiResponse = await response.json();

//                 let processedData = {};

//                 if (processedGroupBy.includes('country')) {
//                     // console.log(processedGroupBy.includes('country'), "country");

//                     processedData = await processRevenueDataForSingleArticle(
//                         revenueResponse,
//                         articleId
//                     );
//                 } else {
//                     let totalEarnings = 0;
//                     let totalClicks = 0;

//                     for (const item of revenueResponse.data) {
//                         totalEarnings += parseFloat(item.ESTIMATED_EARNINGS) || 0;
//                         totalClicks += parseInt(item.CLICKS) || 0;
//                     }

//                     processedData = {
//                         [articleId]: {
//                             estimatedEarnings: totalEarnings,
//                             revenueClicks: totalClicks
//                         }
//                     };
//                 }

//                 Object.assign(aggregatedRevenueData, processedData);
//                 // console.log(processedData, "processedData");

//                 await new Promise((resolve) => setTimeout(resolve, 100));

//             } catch (articleError) {
//                 console.error(`Failed to fetch revenue data for article ${articleId}:`, articleError);
//                 continue;
//             }
//         }

//         return aggregatedRevenueData;

//     } catch (error) {
//         console.error('Failed to fetch data from revenue API:', error);
//         return {};
//     }
// };

// async function processRevenueDataForSingleArticle(
//     revenueResponse: RevenueApiResponse,
//     articleId: string
// ): Promise<AggregatedRevenueData> {
//     let totalEarnings = 0;
//     let totalClicks = 0;
//     const countryData: { [countryCode: string]: { estimatedEarnings: number; revenueClicks: number } } = {};

//     for (const item of revenueResponse.data) {
//         const earnings = parseFloat(item.ESTIMATED_EARNINGS) || 0;
//         const clicks = parseInt(item.CLICKS) || 0;

//         totalEarnings += earnings;
//         totalClicks += clicks;

//         if (item.COUNTRY_CODE) {
//             if (!countryData[item.COUNTRY_CODE]) {
//                 countryData[item.COUNTRY_CODE] = {
//                     estimatedEarnings: 0,
//                     revenueClicks: 0
//                 };
//             }
//             countryData[item.COUNTRY_CODE].estimatedEarnings += earnings;
//             countryData[item.COUNTRY_CODE].revenueClicks += clicks;
//         }
//     }

//     const result: AggregatedRevenueData = {
//         [articleId]: {
//             estimatedEarnings: totalEarnings,
//             revenueClicks: totalClicks
//         }
//     };

//     if (Object.keys(countryData).length > 0) {
//         result[articleId]['countryData'] = countryData;
//     }

//     return result;
// }


// async function getArticleDetails(articleId: string): Promise<{ articlename: string, channelid: string }> {
//     const article = await prisma.articleDetails.findFirst({
//         where: { Id: articleId },
//         select: { Title: true, CustomChannal: true }
//     });

//     return {
//         articlename: article?.Title || '',
//         channelid: article?.CustomChannal?.toString() || ''
//     };
// }

// async function getChannelName(channelId: string): Promise<string> {
//     if (!channelId) return '';

//     const channel = await prisma.channals.findFirst({
//         where: { Id: channelId },
//         select: { DisplayName: true }
//     });

//     return channel?.DisplayName || '';
// }

// async function getCountryName(countryCode: string): Promise<string> {
//     if (!countryCode) return '';

//     const isoCountry = await prisma.ads_ISO_CountryMaster.findFirst({
//         where: { CountryCode: countryCode },
//         select: { Name: true }
//     });

//     if (isoCountry) return isoCountry.Name as string;

//     const countryByCode = await prisma.ads_CountryMaster.findFirst({
//         where: { CountryCode: countryCode },
//         select: { Name: true }
//     });

//     if (countryByCode) return countryByCode.Name as string;

//     const countryByIdForCode = await prisma.ads_CountryMaster.findFirst({
//         where: { CountryId: Number(countryCode) },
//         select: { CountryCode: true }
//     });

//     if (countryByIdForCode?.CountryCode) {
//         const isoCountryByDerivedCode = await prisma.ads_ISO_CountryMaster.findFirst({
//             where: { CountryCode: countryByIdForCode.CountryCode },
//             select: { Name: true }
//         });

//         if (isoCountryByDerivedCode) return isoCountryByDerivedCode.Name as string;
//     }

//     const countryById = await prisma.ads_CountryMaster.findFirst({
//         where: { CountryId: Number(countryCode) },
//         select: { Name: true }
//     });

//     return countryById?.Name || countryCode;
// }

// async function processGeographicData(
//     apiData: ApiResponseData,
//     groupByFields: string[],
//     originalRawData: RevenueCampaignData[],
//     today: string,
//     revenueApiData?: AggregatedRevenueData
// ): Promise<RevenueCampaignData[]> {
//     const processedData: RevenueCampaignData[] = [];
//     const includesCountry = groupByFields.includes('country');
//     const includesArticle = groupByFields.includes('article');
//     const includesChannel = groupByFields.includes('channel');
//     const includesDate = groupByFields.includes('date');

//     // Create caches for better performance
//     const channelCache = new Map<string, string>();
//     const articleCache = new Map<string, { articlename: string, channelid: string }>();
//     const countryCache = new Map<string, string>();

//     const getCachedArticleDetails = async (articleId: string): Promise<{ articlename: string, channelid: string }> => {
//         if (articleCache.has(articleId)) return articleCache.get(articleId)!;

//         const details = await getArticleDetails(articleId);
//         articleCache.set(articleId, details);
//         return details;
//     };

//     const getCachedChannelName = async (channelId: string): Promise<string> => {
//         if (!channelId) return '';
//         if (channelCache.has(channelId)) return channelCache.get(channelId)!;

//         const channelName = await getChannelName(channelId);
//         channelCache.set(channelId, channelName);
//         return channelName;
//     };

//     const getCachedCountryName = async (countryCode: string): Promise<string> => {
//         if (countryCache.has(countryCode)) return countryCache.get(countryCode)!;

//         const countryName = await getCountryName(countryCode);
//         countryCache.set(countryCode, countryName);
//         return countryName;
//     };

//     // 1. Channel-only grouping (without article or country)
//     if (includesChannel && !includesArticle && !includesCountry) {
//         const channelAggregates: { [channelId: string]: RevenueCampaignData & { totalClicks?: number } } = {};

//         for (const [articleId, articleData] of Object.entries(apiData)) {
//             const articleDetails = await getCachedArticleDetails(articleId);
//             const channelId = articleDetails.channelid;
//             const channelName = await getCachedChannelName(channelId);

//             if (!channelId) continue;

//             const revenueData = revenueApiData?.[articleId] || {
//                 estimatedEarnings: 0,
//                 revenueClicks: 0
//             };

//             let totalRevenue = revenueData.estimatedEarnings || 0;
//             let totalClicks = revenueData.revenueClicks || 0;
//             let totalConversions = 0;
//             let totalSpend = 0;

//             for (const geoData of articleData.geographic_data) {
//                 totalConversions += geoData.metrics.conversions;
//                 totalSpend += geoData.metrics.cost;
//             }

//             if (!channelAggregates[channelId]) {
//                 channelAggregates[channelId] = {
//                     channelid: channelId,
//                     channelname: channelName,
//                     date: includesDate ? new Date(today) : undefined,
//                     rv_revenue: 0,
//                     rv_rpc: 0,
//                     profit: 0,
//                     profit_gst: 0,
//                     ads_conversions: 0,
//                     ads_click: 0,
//                     ads_spend: 0,
//                     ads_spend_with_gst: 0,
//                     roi: 0,
//                     roi_gst: 0,
//                     ads_cpa: 0,
//                     ads_cpc: 0,
//                     totalClicks: 0
//                 };
//             }

//             channelAggregates[channelId].rv_revenue += totalRevenue;
//             channelAggregates[channelId].ads_conversions += totalConversions;
//             channelAggregates[channelId].ads_spend += totalSpend;
//             channelAggregates[channelId].ads_click += totalClicks;
//             channelAggregates[channelId].totalClicks! += totalClicks;
//         }

//         // Calculate derived metrics for each channel
//         for (const channelData of Object.values(channelAggregates)) {
//             const spendWithGst = channelData.ads_spend * 1.18;
//             const totalClicks = channelData.totalClicks || 0;

//             channelData.ads_spend_with_gst = spendWithGst;
//             channelData.profit = channelData.rv_revenue - channelData.ads_spend;
//             channelData.profit_gst = channelData.rv_revenue - spendWithGst;
//             channelData.roi = channelData.ads_spend === 0 ?
//                 (channelData.rv_revenue > 0 ? 10000 : 0) :
//                 (channelData.profit / channelData.ads_spend) * 100;
//             channelData.roi_gst = spendWithGst === 0 ?
//                 (channelData.rv_revenue > 0 ? 10000 : 0) :
//                 (channelData.profit_gst / spendWithGst) * 100;
//             channelData.ads_cpa = channelData.ads_conversions === 0 ? 0 : channelData.ads_spend / channelData.ads_conversions;
//             channelData.ads_cpc = totalClicks === 0 ? 0 : channelData.ads_spend / totalClicks;
//             channelData.rv_rpc = totalClicks === 0 ? 0 : channelData.rv_revenue / totalClicks;

//             const { totalClicks: _, ...responseData } = channelData;
//             processedData.push(responseData);
//         }

//         return processedData;
//     }

//     // 2. Article + Country grouping (with optional channel)
//     if (includesCountry && includesArticle) {
//         for (const [articleId, articleData] of Object.entries(apiData)) {
//             const articleDetails = await getCachedArticleDetails(articleId);
//             const channelName = includesChannel && articleDetails.channelid ?
//                 await getCachedChannelName(articleDetails.channelid) : '';

//             const revenueData = revenueApiData?.[articleId] || {
//                 estimatedEarnings: 0,
//                 revenueClicks: 0
//             };

//             for (const geoData of articleData.geographic_data) {
//                 const countryCode = geoData.countryCriterionId;
//                 const countryName = await getCachedCountryName(countryCode);

//                 let countryRevenue = 0;
//                 let countryClicks = 0;

//                 if (revenueApiData?.[articleId]?.countryData?.[countryCode]) {
//                     countryRevenue = revenueApiData[articleId].countryData![countryCode].estimatedEarnings;
//                     countryClicks = revenueApiData[articleId].countryData![countryCode].revenueClicks;
//                 } else {
//                     const mappedCountryCode = await getMappedCountryCode(countryCode);
//                     if (mappedCountryCode && revenueApiData?.[articleId]?.countryData?.[mappedCountryCode]) {
//                         countryRevenue = revenueApiData[articleId].countryData![mappedCountryCode].estimatedEarnings;
//                         countryClicks = revenueApiData[articleId].countryData![mappedCountryCode].revenueClicks;
//                     } else {
//                         countryRevenue = 0;
//                         countryClicks = 0;
//                     }
//                 }

//                 const spendWithGst = geoData.metrics.cost * 1.18;
//                 const profit = countryRevenue - geoData.metrics.cost;
//                 const profit_gst = countryRevenue - spendWithGst;

//                 const entry: RevenueCampaignData = {
//                     date: includesDate ? new Date(today) : undefined,
//                     articleid: articleId,
//                     articlename: articleDetails.articlename,
//                     country: countryName,
//                     rv_revenue: countryRevenue,
//                     rv_rpc: countryClicks ? countryRevenue / countryClicks : 0,
//                     profit: profit,
//                     profit_gst: profit_gst,
//                     ads_conversions: geoData.metrics.conversions,
//                     ads_click: countryClicks,
//                     ads_spend: geoData.metrics.cost,
//                     ads_spend_with_gst: spendWithGst,
//                     roi: geoData.metrics.cost === 0 ? (countryRevenue > 0 ? 10000 : 0) : (profit / geoData.metrics.cost) * 100,
//                     roi_gst: spendWithGst === 0 ? (countryRevenue > 0 ? 10000 : 0) : (profit_gst / spendWithGst) * 100,
//                     ads_cpa: geoData.metrics.conversions === 0 ? 0 : geoData.metrics.cost / geoData.metrics.conversions,
//                     ads_cpc: countryClicks === 0 ? 0 : geoData.metrics.cost / countryClicks,
//                     channelid: includesChannel ? articleDetails.channelid : undefined,
//                     channelname: includesChannel ? channelName : undefined
//                 };

//                 processedData.push(entry);
//             }
//         }
//     }
//     // 3. Country-only grouping (without article, with optional channel)
//     else if (includesCountry && !includesArticle) {
//         const countryAggregates: { [country: string]: RevenueCampaignData & { spend_clicks?: number } } = {};

//         for (const [articleId, articleData] of Object.entries(apiData)) {
//             const articleDetails = await getCachedArticleDetails(articleId);
//             const channelName = includesChannel && articleDetails.channelid ?
//                 await getCachedChannelName(articleDetails.channelid) : '';

//             const revenueData = revenueApiData?.[articleId] || {
//                 estimatedEarnings: 0,
//                 revenueClicks: 0
//             };

//             for (const geoData of articleData.geographic_data) {
//                 const countryCode = geoData.countryCriterionId;
//                 const countryName = await getCachedCountryName(countryCode);

//                 let countryRevenue = 0;
//                 let countryClicks = 0;

//                 if (revenueApiData?.[articleId]?.countryData?.[countryCode]) {
//                     countryRevenue = revenueApiData[articleId].countryData![countryCode].estimatedEarnings;
//                     countryClicks = revenueApiData[articleId].countryData![countryCode].revenueClicks;
//                 } else {
//                     const mappedCountryCode = await getMappedCountryCode(countryCode);
//                     if (mappedCountryCode && revenueApiData?.[articleId]?.countryData?.[mappedCountryCode]) {
//                         countryRevenue = revenueApiData[articleId].countryData![mappedCountryCode].estimatedEarnings;
//                         countryClicks = revenueApiData[articleId].countryData![mappedCountryCode].revenueClicks;
//                     } else {
//                         countryRevenue = 0;
//                         countryClicks = 0;
//                     }
//                 }

//                 if (!countryAggregates[countryName]) {
//                     countryAggregates[countryName] = {
//                         date: includesDate ? new Date(today) : undefined,
//                         country: countryName,
//                         rv_revenue: 0,
//                         rv_rpc: 0,
//                         profit: 0,
//                         profit_gst: 0,
//                         ads_conversions: 0,
//                         ads_click: 0,
//                         ads_spend: 0,
//                         ads_spend_with_gst: 0,
//                         roi: 0,
//                         roi_gst: 0,
//                         ads_cpa: 0,
//                         ads_cpc: 0,
//                         spend_clicks: 0,
//                         channelid: includesChannel ? articleDetails.channelid : undefined,
//                         channelname: includesChannel ? channelName : undefined
//                     };
//                 }

//                 countryAggregates[countryName].rv_revenue += countryRevenue;
//                 countryAggregates[countryName].ads_click += countryClicks;
//                 countryAggregates[countryName].ads_conversions += geoData.metrics.conversions;
//                 countryAggregates[countryName].ads_spend += geoData.metrics.cost;
//                 countryAggregates[countryName].spend_clicks! += geoData.metrics.clicks;
//             }
//         }

//         for (const countryData of Object.values(countryAggregates)) {
//             const spendWithGst = countryData.ads_spend * 1.18;
//             const totalClicks = countryData.ads_click || 0;

//             countryData.ads_spend_with_gst = spendWithGst;
//             countryData.profit = countryData.rv_revenue - countryData.ads_spend;
//             countryData.profit_gst = countryData.rv_revenue - spendWithGst;
//             countryData.roi = countryData.ads_spend === 0 ?
//                 (countryData.rv_revenue > 0 ? 10000 : 0) :
//                 (countryData.profit / countryData.ads_spend) * 100;
//             countryData.roi_gst = spendWithGst === 0 ?
//                 (countryData.rv_revenue > 0 ? 10000 : 0) :
//                 (countryData.profit_gst / spendWithGst) * 100;
//             countryData.ads_cpa = countryData.ads_conversions === 0 ? 0 : countryData.ads_spend / countryData.ads_conversions;
//             countryData.ads_cpc = totalClicks === 0 ? 0 : countryData.ads_spend / totalClicks;
//             countryData.rv_rpc = totalClicks === 0 ? 0 : countryData.rv_revenue / totalClicks;

//             const { spend_clicks, ...responseData } = countryData;
//             processedData.push(responseData);
//         }
//     }
//     // 4. Article-only grouping (without country, with optional channel)
//     else if (includesArticle && !includesCountry) {
//         for (const [articleId, articleData] of Object.entries(apiData)) {
//             let totalRevenue = 0;
//             let totalConversions = 0;
//             let totalSpend = 0;
//             let totalClicks = 0;

//             const articleDetails = await getCachedArticleDetails(articleId);
//             const channelName = includesChannel && articleDetails.channelid ?
//                 await getCachedChannelName(articleDetails.channelid) : '';

//             const revenueData = revenueApiData?.[articleId] || {
//                 estimatedEarnings: 0,
//                 revenueClicks: 0
//             };

//             totalRevenue = revenueData.estimatedEarnings || 0;
//             totalClicks = revenueData.revenueClicks || 0;

//             for (const geoData of articleData.geographic_data) {
//                 totalConversions += geoData.metrics.conversions;
//                 totalSpend += geoData.metrics.cost;
//             }

//             const spendWithGst = totalSpend * 1.18;
//             const profit = totalRevenue - totalSpend;
//             const profit_gst = totalRevenue - spendWithGst;

//             const entry: RevenueCampaignData = {
//                 date: includesDate ? new Date(today) : undefined,
//                 articleid: articleId,
//                 articlename: articleDetails.articlename,
//                 rv_revenue: totalRevenue,
//                 rv_rpc: totalClicks ? totalRevenue / totalClicks : 0,
//                 profit: profit,
//                 profit_gst: profit_gst,
//                 ads_conversions: totalConversions,
//                 ads_click: totalClicks,
//                 ads_spend: totalSpend,
//                 ads_spend_with_gst: spendWithGst,
//                 roi: totalSpend === 0 ? (totalRevenue > 0 ? 10000 : 0) : (profit / totalSpend) * 100,
//                 roi_gst: spendWithGst === 0 ? (totalRevenue > 0 ? 10000 : 0) : (profit_gst / spendWithGst) * 100,
//                 ads_cpa: totalConversions === 0 ? 0 : totalSpend / totalConversions,
//                 ads_cpc: totalClicks === 0 ? 0 : totalSpend / totalClicks,
//                 channelid: includesChannel ? articleDetails.channelid : undefined,
//                 channelname: includesChannel ? channelName : undefined
//             };

//             processedData.push(entry);
//         }
//     }
//     // 5. No grouping (aggregate all data)
//     else {
//         let totalRevenue = 0;
//         let totalConversions = 0;
//         let totalSpend = 0;
//         let totalClicks = 0;

//         for (const [articleId, articleData] of Object.entries(apiData)) {
//             const revenueData = revenueApiData?.[articleId] || {
//                 estimatedEarnings: 0,
//                 revenueClicks: 0
//             };

//             totalRevenue += revenueData.estimatedEarnings || 0;
//             totalClicks += revenueData.revenueClicks || 0;

//             for (const geoData of articleData.geographic_data) {
//                 totalConversions += geoData.metrics.conversions;
//                 totalSpend += geoData.metrics.cost;
//             }
//         }

//         const spendWithGst = totalSpend * 1.18;
//         const profit = totalRevenue - totalSpend;
//         const profit_gst = totalRevenue - spendWithGst;

//         const aggregatedEntry: RevenueCampaignData = {
//             date: includesDate ? new Date(today) : undefined,
//             rv_revenue: totalRevenue,
//             rv_rpc: totalClicks ? totalRevenue / totalClicks : 0,
//             profit: profit,
//             profit_gst: profit_gst,
//             ads_conversions: totalConversions,
//             ads_click: totalClicks,
//             ads_spend: totalSpend,
//             ads_spend_with_gst: spendWithGst,
//             roi: totalSpend === 0 ? (totalRevenue > 0 ? 10000 : 0) : (profit / totalSpend) * 100,
//             roi_gst: spendWithGst === 0 ? (totalRevenue > 0 ? 10000 : 0) : (profit_gst / spendWithGst) * 100,
//             ads_cpa: totalConversions === 0 ? 0 : totalSpend / totalConversions,
//             ads_cpc: totalClicks === 0 ? 0 : totalSpend / totalClicks
//         };

//         processedData.push(aggregatedEntry);
//     }

//     return processedData;
// }

// async function getMappedCountryCode(countryCode: string): Promise<string | null> {
//     const countryName = await getCountryName(countryCode);
//     if (countryName === countryCode) return null;

//     const standardCountry = await prisma.ads_ISO_CountryMaster.findFirst({
//         where: { Name: countryName },
//         select: { CountryCode: true }
//     });

//     return standardCountry?.CountryCode || null;
// }

// function isCurrentDate(dateString: string): boolean {
//     try {
//         if (!dateString) return false;

//         const today = new Date();
//         today.setHours(0, 0, 0, 0);

//         const yesterday = new Date(today);
//         yesterday.setDate(today.getDate() - 1);

//         const inputDate = new Date(dateString);
//         if (isNaN(inputDate.getTime())) return false;
//         inputDate.setHours(0, 0, 0, 0);

//         return inputDate.getTime() >= yesterday.getTime();
//     } catch (error) {
//         return false;
//     }
// }

// function isStartDateBeforeToday(dateString: string): boolean {
//     try {
//         if (!dateString) return false;

//         const today = new Date();
//         today.setHours(0, 0, 0, 0);

//         const yesterday = new Date(today);
//         yesterday.setDate(today.getDate() - 2);

//         const inputDate = new Date(dateString);
//         if (isNaN(inputDate.getTime())) return false;
//         inputDate.setHours(0, 0, 0, 0);

//         return inputDate.getTime() < yesterday.getTime();
//     } catch (error) {
//         return false;
//     }
// }

// function formatDateToYMD(date: Date): string {
//     return date.toISOString().split('T')[0];
// }

// function sortResponseData(data: RevenueCampaignData[], orderBy: string, orderDir: string): RevenueCampaignData[] {
//     return data.sort((a, b) => {
//         let aValue = (a as any)[orderBy];
//         let bValue = (b as any)[orderBy];

//         if (aValue === undefined || aValue === null) return 1;
//         if (bValue === undefined || bValue === null) return -1;

//         if (aValue instanceof Date && bValue instanceof Date) {
//             if (orderDir === 'asc') {
//                 return aValue.getTime() - bValue.getTime();
//             } else {
//                 return bValue.getTime() - aValue.getTime();
//             }
//         }

//         if (typeof aValue === 'string' && typeof bValue === 'string') {
//             if (orderDir === 'asc') {
//                 return aValue.localeCompare(bValue);
//             } else {
//                 return bValue.localeCompare(aValue);
//             }
//         }

//         const aNum = Number(aValue) || 0;
//         const bNum = Number(bValue) || 0;

//         if (orderDir === 'asc') {
//             return aNum - bNum;
//         } else {
//             return bNum - aNum;
//         }
//     });
// }

// async function getAllArticleIds(user: any, domainId?: string, subDomainId?: string): Promise<string[]> {
//     try {
//         let whereClause: any = {
//             IsDeleted: false
//         };
//         let processedStyleId: string | null = null;

//         if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
//             const styleMapping = await prisma.styleIdUserMappings.findFirst({
//                 where: { UserId: user.Id as string },
//                 select: { StyleId: true }
//             });

//             if (!styleMapping || !styleMapping.StyleId) {
//                 return [];
//             }
//             processedStyleId = styleMapping.StyleId;

//             whereClause.OR = [
//                 { StyleIdLm: styleMapping.StyleId },
//                 { StyleIdDm: styleMapping.StyleId }
//             ];
//             whereClause.User_Id_Settings = user.Id;
//         }

//         if (domainId) whereClause.Domain = domainId.toString();
//         if (subDomainId) whereClause.SubDomain = subDomainId.toString();

//         const articles = await prisma.articleDetails.findMany({
//             where: whereClause,
//             select: { Id: true }
//         });

//         return articles.map(article => article.Id.toString());
//     } catch (error) {
//         console.error('Error fetching all article IDs:', error);
//         return [];
//     }
// }

// async function getUserInfo(userId: string): Promise<UserInfo | null> {
//     try {
//         const user = await prisma.adminUser.findFirst({
//             where: { Id: userId },
//             select: {
//                 Id: true,
//                 User_Type: true
//             }
//         });

//         if (!user) {
//             return null;
//         }

//         let styleId: string | undefined;
//         if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
//             const styleMappings = await prisma.styleIdUserMappings.findMany({
//                 where: { UserId: userId },
//                 select: { StyleId: true }
//             });

//             const validMapping = styleMappings.find(mapping => mapping.StyleId !== null);
//             styleId = validMapping?.StyleId ?? undefined;
//         }

//         return {
//             Id: user.Id,
//             User_Type: user.User_Type,
//             styleId
//         };
//     } catch (error) {
//         return null;
//     }
// }
// function mergeArticleData(dbData: RevenueCampaignData[], apiData: RevenueCampaignData[]): RevenueCampaignData[] {
//     const mergedMap = new Map<string, RevenueCampaignData>();

//     // First, add all DB data to the map
//     for (const item of dbData) {
//         const key = generateMergeKey(item);
//         mergedMap.set(key, { ...item });
//     }

//     // Then, merge or add API data
//     for (const item of apiData) {
//         const key = generateMergeKey(item);
//         const existing = mergedMap.get(key);

//         if (existing) {
//             // Merge the data by adding revenue-related fields
//             existing.rv_revenue += item.rv_revenue || 0;
//             existing.ads_conversions += item.ads_conversions || 0;
//             existing.ads_click += item.ads_click || 0;
//             existing.ads_spend += item.ads_spend || 0;
//             existing.ads_spend_with_gst += item.ads_spend_with_gst || 0;

//             // Recalculate derived metrics
//             existing.profit = existing.rv_revenue - existing.ads_spend;
//             existing.profit_gst = existing.rv_revenue - existing.ads_spend_with_gst;
//             existing.rv_rpc = existing.ads_click > 0 ? existing.rv_revenue / existing.ads_click : 0;
//             existing.roi = existing.ads_spend === 0 ?
//                 (existing.rv_revenue > 0 ? 10000 : 0) :
//                 (existing.profit / existing.ads_spend) * 100;
//             existing.roi_gst = existing.ads_spend_with_gst === 0 ?
//                 (existing.rv_revenue > 0 ? 10000 : 0) :
//                 (existing.profit_gst / existing.ads_spend_with_gst) * 100;
//             existing.ads_cpa = existing.ads_conversions === 0 ? 0 : existing.ads_spend / existing.ads_conversions;
//             existing.ads_cpc = existing.ads_click === 0 ? 0 : existing.ads_spend / existing.ads_click;
//         } else {
//             // Add new API data
//             mergedMap.set(key, { ...item });
//         }
//     }

//     return Array.from(mergedMap.values());
// }

// function generateMergeKey(item: RevenueCampaignData): string {
//     const keyParts: string[] = [];

//     if (item.articleid) keyParts.push(`article:${item.articleid}`);
//     if (item.channelid) keyParts.push(`channel:${item.channelid}`);
//     if (item.country) keyParts.push(`country:${item.country}`);
//     if (item.date) keyParts.push(`date:${formatDateToYMD(item.date)}`);

//     return keyParts.join('|') || 'default';
// }

// export async function POST(req: NextRequest) {
//     try {
//         type AuthenticatedUser = {
//             Id: string;
//             User_Type: string;
//         };

//         const user = (await verifyToken(req)) as AuthenticatedUser;
//         if (!user) {
//             return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         }
//         const body = await req.json();
//         let { articleId, channelId, groupBy, domainId, subDomainId, orderBy, orderDir = 'asc' } = body;

//         const { searchParams } = new URL(req.url);
//         const startDate = searchParams.get('startDate');
//         const endDate = searchParams.get('endDate');
//         const showNoRevenue = searchParams.get('showNoRevenue') || 'true';
//         const InrtoUsd = searchParams.get('InrtoUsd') || 'false';
//         const userIdParam = searchParams.get('userId') || '';

//         if (!startDate || !endDate) {
//             return NextResponse.json({
//                 error: 'Both startDate and endDate are required'
//             }, { status: 400 });
//         }

//         const isEndDateToday = isCurrentDate(endDate);
//         const isStartBeforeToday = isStartDateBeforeToday(startDate);
//         const useHybridApproach = isStartBeforeToday && isEndDateToday;
//         const useOnlyExternalApi = !isStartBeforeToday && isEndDateToday;

//         if (!articleId) {
//             articleId = await getAllArticleIds(user, domainId, subDomainId);

//             if (articleId.length === 0) {
//                 return NextResponse.json({
//                     success: true,
//                     data: [],
//                     meta: { recordCount: 0, apiDataUsed: false, revenueDataUsed: false }
//                 });
//             }
//         }

//         const isAdmin = user.User_Type === 'Super Admin' || user.User_Type === 'Admin';

//         let effectiveUser: UserInfo;

//         if (isAdmin && userIdParam) {
//             const targetUser = await getUserInfo(userIdParam);
//             if (!targetUser) {
//                 return NextResponse.json({
//                     error: 'User not found'
//                 }, { status: 404 });
//             }
//             effectiveUser = targetUser;
//         } else {
//             let styleId: string | undefined;
//             if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
//                 const styleMapping = await prisma.styleIdUserMappings.findFirst({
//                     where: { UserId: user.Id as string },
//                     select: { StyleId: true }
//                 });
//                 styleId = styleMapping?.StyleId || undefined;
//             }

//             effectiveUser = {
//                 Id: user.Id as string,
//                 User_Type: user.User_Type,
//                 styleId
//             };
//         }
//         if (channelId) {
//             const channelArticles = await prisma.articleDetails.findMany({
//                 where: {
//                     CustomChannal: channelId.toString(),
//                     User_Id_Settings: effectiveUser.Id
//                 },
//                 select: { Id: true }
//             });
//             // console.log(channelArticles, "channelArticles");

//             const channelArticleIds = channelArticles.map(article => article.Id.toString());

//             if (articleId) {
//                 const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
//                 articleId = requestedIds.filter(id => channelArticleIds.includes(id));
//             } else {
//                 articleId = channelArticleIds;
//             }
//         }
//         const shouldCallExternalApi = useOnlyExternalApi || useHybridApproach;

//         let externalApiData: ApiResponseData | null = null;
//         let revenueApiData: AggregatedRevenueData | null = null;
//         let channelArticles: string[] = [];

//         let processedStyleId: string | null = null;

//         if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
//             try {
//                 const styleMapping = await prisma.styleIdUserMappings.findFirst({
//                     where: { UserId: user.Id as string },
//                     select: { StyleId: true }
//                 });

//                 if (!styleMapping?.StyleId) {
//                     return NextResponse.json({ error: 'No StyleId found for this user' }, { status: 403 });
//                 }
//                 processedStyleId = styleMapping.StyleId;

//                 const userStyleId = styleMapping.StyleId;

//                 const articleDetails = await prisma.articleDetails.findMany({
//                     where: {
//                         User_Id_Settings: user.Id as string,
//                         OR: [{ StyleIdLm: userStyleId }, { StyleIdDm: userStyleId }],
//                     },
//                     select: { Id: true }
//                 });

//                 const allowedArticleIds = articleDetails.map(a => a.Id.toString());

//                 if (allowedArticleIds.length === 0) {
//                     return NextResponse.json({ success: true, data: [] });
//                 }

//                 if (!articleId) {
//                     articleId = allowedArticleIds;
//                 } else {
//                     const requestedIds = Array.isArray(articleId) ?
//                         articleId.map(id => id.toString()) :
//                         [articleId.toString()];

//                     articleId = requestedIds.filter(id => allowedArticleIds.includes(id));

//                     if (articleId.length === 0) {
//                         return NextResponse.json({ success: true, data: [] });
//                     }
//                 }

//                 const articleChannels = await prisma.articleDetails.findMany({
//                     where: { OR: [{ StyleIdLm: userStyleId }, { StyleIdDm: userStyleId }] },
//                     select: { CustomChannal: true },
//                 });

//                 const allowedChannelIds = articleChannels
//                     .map(a => a.CustomChannal?.toString())
//                     .filter(id => id) as string[];

//                 if (!channelId && allowedChannelIds.length > 0) {
//                     channelId = allowedChannelIds;
//                 } else if (channelId && allowedChannelIds.length > 0) {
//                     const requestedIds = Array.isArray(channelId) ?
//                         channelId.map(id => id.toString()) :
//                         [channelId.toString()];

//                     channelId = requestedIds.filter(id => allowedChannelIds.includes(id));
//                 }

//             } catch (dbError) {
//                 console.error('Permission check failed:', dbError);
//                 return NextResponse.json({
//                     error: 'Failed to fetch user permissions'
//                 }, { status: 500 });
//             }
//         }

//         if (shouldCallExternalApi && (articleId || channelId)) {
//             try {
//                 let articleIds: string[] = [];
//                 let channelIds: string[] = [];

//                 if (articleId) {
//                     articleIds = Array.isArray(articleId) ? articleId : [articleId];
//                 }

//                 if (channelId) {
//                     channelIds = Array.isArray(channelId) ? channelId : [channelId];

//                     // Get articles for these channels
//                     const articles = await prisma.articleDetails.findMany({
//                         where: {
//                             CustomChannal: {
//                                 in: channelIds
//                             }
//                         },
//                         select: {
//                             Id: true
//                         }
//                     });

//                     const channelArticleIds = articles.map(a => a.Id.toString());

//                     // If we also have specific articleIds, filter them
//                     if (articleIds.length > 0) {
//                         articleIds = articleIds.filter(id => channelArticleIds.includes(id));
//                     } else {
//                         articleIds = channelArticleIds;
//                     }
//                 }

//                 if (articleIds.length > 0 || channelIds.length > 0) {
//                     const dateRange = useHybridApproach ?
//                         { start: formatDateToYMD(new Date()), end: formatDateToYMD(new Date()) } :
//                         { start: startDate, end: endDate };

//                     externalApiData = await fetchFromCampaignApi(articleIds, channelIds, dateRange.start, dateRange.end);
//                     const groupByFields = groupBy ?
//                         (Array.isArray(groupBy) ? groupBy : groupBy.split(',').map((f: string) => f.trim().toLowerCase())) :
//                         [];
//                     revenueApiData = await fetchFromRevenueApi(req, groupByFields, articleIds, channelIds, dateRange.start, dateRange.end);
//                 }
//             } catch (error) {
//                 console.error('Continuing without external data:', error);
//                 externalApiData = null;
//                 revenueApiData = null;
//             }
//         }

//         const validGroupByCombinations = [
//             [""], ["article"], ["channel"], ["country"], ["date"],
//             ["article", "channel"], ["article", "country"], ["article", "date"],
//             ["channel", "country"], ["channel", "date"], ["country", "date"],
//             ["article", "channel", "country"], ["article", "channel", "date"],
//             ["article", "country", "date"], ["channel", "country", "date"],
//             ["article", "channel", "country", "date"]
//         ];

//         let processedGroupBy = '';
//         let groupByFields: string[] = [];

//         if (groupBy) {
//             const groupByArray = Array.isArray(groupBy) ?
//                 groupBy :
//                 groupBy.split(',').map((f: string) => f.trim().toLowerCase());

//             const isValid = validGroupByCombinations.some(validCombo =>
//                 validCombo.length === groupByArray.length &&
//                 validCombo.every(field => groupByArray.includes(field))
//             );

//             if (!isValid) {
//                 return NextResponse.json({
//                     error: 'Invalid groupBy combination',
//                     validCombinations: validGroupByCombinations
//                 }, { status: 400 });
//             }

//             groupByFields = groupByArray;
//             processedGroupBy = groupByFields.join(',');
//         }

//         const includesDate = groupByFields.includes('date');
//         const includesChannel = groupByFields.includes('channel');
//         const finalOrderBy = orderBy || (includesDate ? 'date' : 'ads_spend');

//         const validSortFields = [
//             'ads_spend', 'rv_revenue', 'profit', 'roi', 'profit_gst', 'roi_gst',
//             'ads_conversions', 'ads_click', 'ads_cpa', 'ads_cpc',
//             'date', 'articlename', 'channelname', 'country', 'rv_rpc', 'ads_spend_with_gst'
//         ];

//         if (!validSortFields.includes(finalOrderBy.toLowerCase())) {
//             return NextResponse.json({
//                 error: `Invalid orderBy parameter. Valid options: ${validSortFields.join(', ')}`
//             }, { status: 400 });
//         }

//         if (!['asc', 'desc'].includes(orderDir.toLowerCase())) {
//             return NextResponse.json({
//                 error: 'Invalid orderDir parameter. Use asc or desc'
//             }, { status: 400 });
//         }

//         const processedArticleId = articleId ?
//             (Array.isArray(articleId) ? articleId.join(',') : articleId.toString()) :
//             null;

//         const processedChannelId = channelId ?
//             (Array.isArray(channelId) ? channelId.join(',') : channelId.toString()) :
//             null;

//         let finalData: RevenueCampaignData[] = [];

//         let dbEndDate = endDate;
//         if (useHybridApproach) {
//             const today = new Date();
//             const yesterday = new Date(today);
//             yesterday.setDate(yesterday.getDate() - 1);
//             dbEndDate = formatDateToYMD(yesterday);
//         }

//         if (!useOnlyExternalApi) {
//             await prisma.$executeRawUnsafe(
//                 `SELECT * FROM fn_ads_revenue_data_test(
//                     $1::text, $2::text, $3::date, $4::date, 
//                     $5::text, $6::boolean, $7::boolean, $8::text
//                 )`,
//                 processedArticleId,
//                 processedChannelId,
//                 new Date(startDate),
//                 new Date(dbEndDate),
//                 processedGroupBy,
//                 showNoRevenue === 'true',
//                 InrtoUsd === 'true',
//                 processedStyleId
//             );
//             // console.log(processedArticleId, "processedArticleId", processedChannelId, "processedChannelId", new Date(startDate), new Date(endDate), "Date",
//             //     processedGroupBy, "processedGroupBy", processedStyleId, "processedStyleId");

//             let selectFields = `
//                 "rv_revenue"::float AS "rv_revenue",
//                 "rv_rpc"::float AS "rv_rpc",
//                 ("rv_revenue"::float - "ads_spend"::float)::float AS "profit",
//                 ("rv_revenue"::float - ("ads_spend"::float * 1.18))::float AS "profit_gst",
//                 "ads_conversions"::integer AS "ads_conversions",
//                 "ads_click"::integer AS "ads_click",
//                 "ads_spend"::float AS "ads_spend",
//                 ("ads_spend"::float * 1.18)::float AS "ads_spend_with_gst",
//                 CASE 
//                     WHEN "ads_conversions"::integer = 0 THEN 0 
//                     ELSE ("ads_spend"::float / "ads_conversions"::integer)::float 
//                 END AS "ads_cpa",
//                 CASE 
//                     WHEN "ads_click"::integer = 0 THEN 0 
//                     ELSE ("ads_spend"::float / "ads_click"::integer)::float 
//                 END AS "ads_cpc",
//                 CASE
//                     WHEN "ads_spend"::float = 0 THEN 
//                         CASE WHEN "rv_revenue"::float > 0 THEN 0 ELSE 0 END
//                     ELSE ((("rv_revenue"::float - "ads_spend"::float) / 
//                         "ads_spend"::float)::float) * 100
//                 END AS "roi",
//                 CASE
//                     WHEN ("ads_spend"::float * 1.18) = 0 THEN 
//                         CASE WHEN "rv_revenue"::float > 0 THEN 0 ELSE 0 END
//                     ELSE ((("rv_revenue"::float - ("ads_spend"::float * 1.18)) / 
//                         ("ads_spend"::float * 1.18))::float) * 100
//                 END AS "roi_gst"
//             `;

//             if (includesDate) selectFields = `"date"::timestamp as "date", ` + selectFields;
//             if (groupByFields.includes('article')) selectFields = `"articleid"::text as "articleid", "article_name"::text as "articlename", ` + selectFields;
//             if (includesChannel) {
//                 selectFields = `"channelid"::text as "channelid", "channel_name"::text as "channelname", ` + selectFields;
//             }
//             if (groupByFields.includes('country')) selectFields += `, "country"::text as "country"`;

//             let orderByClause = `"${finalOrderBy}" ${orderDir}`;
//             if (includesDate && finalOrderBy !== 'date') orderByClause += ', "date" DESC';
//             if (!includesDate && finalOrderBy !== 'ads_spend') orderByClause += ', "ads_spend" DESC';
//             if (groupByFields.includes('article') && finalOrderBy !== 'articlename') orderByClause += ', "article_name"';
//             if (includesChannel && finalOrderBy !== 'channelname') orderByClause += ', "channel_name"';
//             if (groupByFields.includes('country') && finalOrderBy !== 'country') orderByClause += ', "country"';

//             const rawData = await prisma.$queryRawUnsafe<RevenueCampaignData[]>(`
//                 SELECT ${selectFields}
//                 FROM pg_temp.temp_ads_result
//                 ORDER BY ${orderByClause}
//             `);

//             finalData = [...rawData];
//         }

//         if (externalApiData && (processedArticleId || processedChannelId)) {
//             const dateToProcess = useHybridApproach ? formatDateToYMD(new Date()) : endDate;

//             if (Object.keys(externalApiData).length > 0) {
//                 const processedApiData = await processGeographicData(
//                     externalApiData,
//                     groupByFields,
//                     finalData,
//                     dateToProcess,
//                     revenueApiData || undefined
//                 );

//                 if (useHybridApproach) {
//                     finalData = mergeArticleData(finalData, processedApiData);
//                 } else {
//                     finalData = processedApiData;
//                 }

//                 finalData = sortResponseData(finalData, finalOrderBy, orderDir);
//             }
//         }
//         if (includesChannel) {
//             finalData = await Promise.all(finalData.map(async item => {
//                 if (!item.channelid && item.articleid) {
//                     const details = await getArticleDetails(item.articleid);
//                     return {
//                         ...item,
//                         channelid: details.channelid,
//                         channelname: await getChannelName(details.channelid)
//                     };
//                 }
//                 return item;
//             }));
//         }

//         if (includesChannel && !groupByFields.includes('article') && !groupByFields.includes('country')) {
//             const channelGroups = new Map<string, RevenueCampaignData & { channelname?: string }>();

//             for (const item of finalData) {
//                 const channelId = item.channelid || '';
//                 let channelGroup = channelGroups.get(channelId);

//                 if (!channelGroup) {
//                     channelGroup = {
//                         channelid: channelId,
//                         channelname: item.channelname || await getChannelName(channelId),
//                         rv_revenue: 0,
//                         rv_rpc: 0,
//                         profit: 0,
//                         profit_gst: 0,
//                         ads_conversions: 0,
//                         ads_click: 0,
//                         ads_spend: 0,
//                         ads_spend_with_gst: 0,
//                         roi: 0,
//                         roi_gst: 0,
//                         ads_cpa: 0,
//                         ads_cpc: 0,
//                         ...(includesDate && item.date ? { date: item.date } : {})
//                     };
//                     channelGroups.set(channelId, channelGroup);
//                 }

//                 channelGroup.rv_revenue += item.rv_revenue || 0;
//                 channelGroup.ads_conversions += item.ads_conversions || 0;
//                 channelGroup.ads_click += item.ads_click || 0;
//                 channelGroup.ads_spend += item.ads_spend || 0;
//                 channelGroup.ads_spend_with_gst += item.ads_spend_with_gst || 0;
//                 channelGroup.profit += item.profit || 0;
//                 channelGroup.profit_gst += item.profit_gst || 0;
//             }

//             finalData = Array.from(channelGroups.values()).map(channelGroup => {
//                 channelGroup.rv_rpc = channelGroup.ads_click ? channelGroup.rv_revenue / channelGroup.ads_click : 0;
//                 channelGroup.roi = channelGroup.ads_spend ?
//                     (channelGroup.profit / channelGroup.ads_spend) * 100 :
//                     (channelGroup.rv_revenue > 0 ? 10000 : 0);
//                 channelGroup.roi_gst = channelGroup.ads_spend_with_gst ?
//                     (channelGroup.profit_gst / channelGroup.ads_spend_with_gst) * 100 :
//                     (channelGroup.rv_revenue > 0 ? 10000 : 0);
//                 channelGroup.ads_cpa = channelGroup.ads_conversions ?
//                     channelGroup.ads_spend / channelGroup.ads_conversions : 0;
//                 channelGroup.ads_cpc = channelGroup.ads_click ?
//                     channelGroup.ads_spend / channelGroup.ads_click : 0;
//                 return channelGroup;
//             });
//         }

//         if (showNoRevenue === 'true') {
//             finalData = finalData.filter(row => {
//                 const rvRevenue = Number(row.rv_revenue) || 0;
//                 const adsSpend = Number(row.ads_spend) || 0;
//                 return !(rvRevenue === 0 && adsSpend === 0);
//             });
//         }

//         if (InrtoUsd === 'true' && shouldCallExternalApi && externalApiData) {
//             const usdToInrRate = await getUSDtoINRRate();
//             finalData = convertRevenueToINR(finalData, usdToInrRate);
//         }

//         return NextResponse.json({
//             success: true,
//             data: finalData
//         });

//     } catch (error) {
//         console.error('Request failed:', error);

//         if (error instanceof Error) {
//             if (error.message.includes('42703')) {
//                 return NextResponse.json(
//                     { error: 'Database column does not exist' },
//                     { status: 500 }
//                 );
//             }
//             if (error.message.includes('42P01')) {
//                 return NextResponse.json(
//                     { error: 'Database table does not exist' },
//                     { status: 500 }
//                 );
//             }
//         }

//         return NextResponse.json(
//             {
//                 error: 'Failed to process request',
//                 details: process.env.NODE_ENV === 'development' ? error : undefined,
//             },
//             { status: 500 }
//         );
//     }
// }






import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

interface RevenueCampaignData {
    articleid?: string;
    articlename?: string;
    date?: Date;
    rv_revenue: number;
    rv_rpc: number;
    profit: number;
    roi: number;
    profit_gst: number;
    roi_gst: number;
    ads_conversions: number;
    ads_click: number;
    ads_spend: number;
    ads_spend_with_gst: number;
    ads_cpa: number;
    ads_cpc: number;
    channelid?: string;
    channelname?: string;
    country?: string;
    rv_impressions: number;
    rv_rpm: number;
}

interface UserInfo {
    Id: string;
    User_Type: string | null;
    styleId?: string;
}

async function getUserInfo(userId: string): Promise<UserInfo | null> {
    try {
        const user = await prisma.adminUser.findFirst({
            where: { Id: userId },
            select: {
                Id: true,
                User_Type: true
            }
        });

        if (!user) {
            return null;
        }

        let styleId: string | undefined;
        if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
            const styleMappings = await prisma.styleIdUserMappings.findMany({
                where: { UserId: userId },
                select: { StyleId: true }
            });

            const validMapping = styleMappings.find(mapping => mapping.StyleId !== null);
            styleId = validMapping?.StyleId ?? undefined;
        }

        return {
            Id: user.Id,
            User_Type: user.User_Type,
            styleId
        };
    } catch (error) {
        return null;
    }
}

export async function POST(req: NextRequest) {
    let processedStyleId: string | null = null;

    try {
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const body = await req.json();
        let { articleId, channelId, styleId, groupBy, domainId, subDomainId, orderBy, orderDir = 'asc' } = body;

        const { searchParams } = new URL(req.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const showNoRevenue = searchParams.get('showNoRevenue') || 'true';
        const InrtoUsd = searchParams.get('InrtoUsd') || 'false';
        const userIdParam = searchParams.get('userId') || '';
        const showGSTValue = searchParams.get('showGSTValue') === 'true';

        if (!startDate || !endDate) {
            return NextResponse.json({
                error: 'Both startDate and endDate are required'
            }, { status: 400 });
        }

        let allowedChannelIds: string[] = [];
        let allowedArticleIds: string[] = [];

        if (role === 'Partner' || role === 'Account') {
            try {
                const styleMapping = await prisma.styleIdUserMappings.findFirst({
                    where: {
                        UserId: userId
                    },
                    select: {
                        StyleId: true
                    }
                });

                if (!styleMapping || !styleMapping.StyleId) {
                    return NextResponse.json({
                        error: 'No StyleId found for this user'
                    }, { status: 403 });
                }

                processedStyleId = styleMapping.StyleId;

                if (styleId) {
                    if (styleId !== processedStyleId) {
                        return NextResponse.json({
                            error: 'Access denied: You do not have permission to access this styleId'
                        }, { status: 403 });
                    }
                    processedStyleId = styleId;
                }

                const articleDetails = await prisma.articleDetails.findMany({
                    where: {
                        User_Id_Settings: userId,
                        OR: [
                            { StyleIdLm: processedStyleId },
                            { StyleIdDm: processedStyleId }
                        ],
                    },
                    select: {
                        Id: true,
                        CustomChannal: true
                    }
                });

                allowedArticleIds = articleDetails.map(article => article.Id.toString());

                if (allowedArticleIds.length === 0) {
                    return NextResponse.json({
                        success: true,
                        data: []
                    });
                }

                if (articleId) {
                    const requestedArticleIds = Array.isArray(articleId)
                        ? articleId.map(id => id.toString())
                        : [articleId.toString()];

                    const filteredArticleIds = requestedArticleIds.filter(id =>
                        allowedArticleIds.includes(id)
                    );

                    if (filteredArticleIds.length === 0) {
                        return NextResponse.json({
                            success: true,
                            data: []
                        });
                    }

                    articleId = filteredArticleIds;
                } else {
                    articleId = allowedArticleIds;
                }

                allowedChannelIds = articleDetails
                    .map(article => article.CustomChannal?.toString())
                    .filter(id => id !== undefined && id !== null && id !== '') as string[];

                if (channelId && allowedChannelIds.length > 0) {
                    const requestedChannelIds = Array.isArray(channelId)
                        ? channelId.map(id => id.toString())
                        : [channelId.toString()];

                    const filteredChannelIds = requestedChannelIds.filter(id =>
                        allowedChannelIds.includes(id)
                    );
                    channelId = filteredChannelIds.length > 0 ? filteredChannelIds : allowedChannelIds;
                } else if (!channelId && allowedChannelIds.length > 0) {
                    channelId = allowedChannelIds;
                }

            } catch (dbError) {
                console.error('Error fetching user permissions:', dbError);
                return NextResponse.json({
                    error: 'Failed to fetch user permissions'
                }, { status: 500 });
            }
        } else {
            if (styleId) {
                processedStyleId = styleId;
            }

            if (domainId || subDomainId || userIdParam) {
                try {
                    let styleMapping = null;

                    if (userIdParam) {
                        styleMapping = await prisma.styleIdUserMappings.findFirst({
                            where: {
                                UserId: userIdParam as string
                            },
                            select: {
                                StyleId: true
                            }
                        });

                        if (!styleMapping || !styleMapping.StyleId) {
                            return NextResponse.json({
                                error: 'Data not Found for this user'
                            }, { status: 403 });
                        }
                        processedStyleId = styleMapping.StyleId;
                    }

                    const articleWhereClause: any = {
                        IsDeleted: false,
                    };

                    if (processedStyleId) {
                        articleWhereClause.OR = [
                            { StyleIdLm: processedStyleId },
                            { StyleIdDm: processedStyleId }
                        ];
                    }

                    if (domainId) {
                        articleWhereClause.Domain = domainId.toString();
                    }

                    if (subDomainId) {
                        articleWhereClause.SubDomain = subDomainId.toString();
                    }
                    if (userIdParam) {
                        articleWhereClause.User_Id_Settings = userIdParam.toString();
                    }

                    const articleDetails = await prisma.articleDetails.findMany({
                        where: articleWhereClause,
                        select: {
                            Id: true
                        }
                    });

                    if (articleDetails.length === 0) {
                        return NextResponse.json({
                            success: true,
                            data: []
                        });
                    }

                    const domainFilteredArticleIds = articleDetails.map(article => article.Id.toString());

                    if (articleId) {
                        const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
                        const filteredIds = requestedIds.filter(id => domainFilteredArticleIds.includes(id.toString()));
                        articleId = filteredIds.length > 0 ? filteredIds : domainFilteredArticleIds;
                    } else {
                        articleId = domainFilteredArticleIds;
                    }

                } catch (dbError) {
                    console.error('Error applying domain filters:', dbError);
                    return NextResponse.json({
                        error: 'Failed to apply domain filters'
                    }, { status: 500 });
                }
            }
        }

        const processedArticleId = articleId
            ? (Array.isArray(articleId) ? articleId.join(',') : articleId.toString())
            : '';
        const processedChannelId = channelId
            ? (Array.isArray(channelId) ? channelId.join(',') : channelId.toString())
            : '';

        if (!processedArticleId && !processedChannelId && (role === 'Partner' || role === 'Account')) {
            return NextResponse.json({
                success: true,
                data: []
            });
        }

        const validGroupByCombinations = [
            '', 'all', 'date', 'country', 'article', 'channel', 'styleId',
            'date,country', 'date,article', 'date,channel', 'date,styleId',
            'country,article', 'country,channel', 'country,styleId',
            'article,channel', 'article,styleId', 'channel,styleId',
            'date,country,article', 'date,country,channel', 'date,country,styleId',
            'date,article,channel', 'date,article,styleId', 'date,channel,styleId',
            'country,article,channel', 'country,article,styleId', 'country,channel,styleId',
            'article,channel,styleId', 'date,country,article,channel',
            'date,country,article,styleId', 'date,country,channel,styleId',
            'date,article,channel,styleId', 'country,article,channel,styleId',
            'date,country,article,channel,styleId'
        ];

        let processedGroupBy = '';
        let groupByFields: string[] = [];

        if (groupBy) {
            const groupByArray = Array.isArray(groupBy) ? groupBy : groupBy.split(',').map((field: string) => field.trim());

            const normalizedGroupBy = groupByArray.map((field: string) => {
                if (field.toLowerCase() === 'styleid') return 'styleId';
                if (field.toLowerCase() === 'all') return 'all';
                return field;
            });

            if (normalizedGroupBy.includes('all')) {
                groupByFields = ['date', 'country', 'article', 'channel', 'styleId'];
                processedGroupBy = 'all';
            } else {
                const isValidCombination = validGroupByCombinations.some((validCombo: string) => {
                    const validComboArray = validCombo.split(',').filter(field => field !== '');
                    return validComboArray.length === normalizedGroupBy.length &&
                        validComboArray.every((field: string) => normalizedGroupBy.includes(field));
                });

                if (!isValidCombination) {
                    return NextResponse.json({
                        error: 'Invalid groupBy combination. Valid combinations: ' +
                            validGroupByCombinations.map(combo => combo ? `[${combo}]` : '[]').join(', ')
                    }, { status: 400 });
                }

                groupByFields = normalizedGroupBy;
                processedGroupBy = groupByFields.join(',');
            }
        } else {
            groupByFields = [];
            processedGroupBy = '';
        }

        const includesDate = groupByFields.includes('date') || groupByFields.includes('all');
        const includesStyleId = groupByFields.includes('styleId') || groupByFields.includes('all');
        const finalOrderBy = orderBy || (includesDate ? 'date' : 'ads_spend');

        const validSortFields = [
            'ads_spend', 'rv_revenue', 'profit', 'roi',
            'ads_conversions', 'ads_click', 'ads_cpa',
            'articlename', 'channelname', 'date', 'rv_rpc',
            'rv_impressions', 'rv_rpm', 'stylename', 'country',
            ...(showGSTValue ? ['profit_gst', 'roi_gst', 'ads_spend_with_gst'] : [])
        ];

        if (!validSortFields.includes(finalOrderBy.toLowerCase())) {
            return NextResponse.json({
                error: `Invalid orderBy parameter. Valid options: ${validSortFields.join(', ')}`
            }, { status: 400 });
        }

        if (!['asc', 'desc'].includes(orderDir.toLowerCase())) {
            return NextResponse.json({
                error: 'Invalid orderDir parameter. Use asc or desc'
            }, { status: 400 });
        }
        await prisma.$executeRawUnsafe(
            `SELECT fn_ads_revenue_data_test_styleid(
                    $1::text, $2::text, $3::date, $4::date, 
                    $5::text, $6::boolean, $7::boolean, $8::text
                )`,
            processedArticleId || '',
            processedChannelId || '',
            startDate,
            endDate,
            processedGroupBy || '',
            showNoRevenue === 'true',
            InrtoUsd === 'true',
            processedStyleId || ''
        );

        const buildSelectFields = (showGSTValue: boolean, includesDate: boolean, groupByFields: string[]): string => {
            let selectFields = '';

            const showAllFields = groupByFields.includes('all');

            if (includesDate || showAllFields) {
                selectFields += `"date"::timestamp as "date",`;
            }

            if (groupByFields.includes('article') || showAllFields) {
                selectFields += `
            "articleid"::text as "articleid",
            "article_name"::text as "articlename",`;
            }

            const baseFields = `
        "rv_revenue"::float AS "rv_revenue",
        "rv_rpc"::float AS "rv_rpc",
        ("rv_revenue"::float - "ads_spend"::float)::float AS "profit",
        "ads_conversions"::integer AS "ads_conversions",
        "ads_click"::integer AS "ads_click",
        "ads_spend"::float AS "ads_spend",
        CASE 
            WHEN "ads_conversions"::integer = 0 THEN 0 
            ELSE ("ads_spend"::float / "ads_conversions"::integer)::float 
        END AS "ads_cpa",
        CASE
            WHEN "ads_spend"::float = 0 THEN 
                CASE WHEN "rv_revenue"::float > 0 THEN 0 ELSE 0 END
            ELSE ((("rv_revenue"::float - "ads_spend"::float) / 
                "ads_spend"::float)::float) * 100
        END AS "roi",
        "rv_impressions"::float AS "rv_impressions",
        "rv_rpm"::float AS "rv_rpm"
    `;

            const gstFields = `
        ("rv_revenue"::float - ("ads_spend"::float * 1.18))::float AS "profit_gst",
        ("ads_spend"::float * 1.18)::float AS "ads_spend_with_gst",
        CASE
            WHEN ("ads_spend"::float * 1.18) = 0 THEN 
                CASE WHEN "rv_revenue"::float > 0 THEN 0 ELSE 0 END
            ELSE ((("rv_revenue"::float - ("ads_spend"::float * 1.18)) / 
                ("ads_spend"::float * 1.18))::float) * 100
        END AS "roi_gst"
    `;

            selectFields += showGSTValue ? `${baseFields}, ${gstFields}` : baseFields;

            if (groupByFields.includes('channel') || showAllFields) {
                selectFields += `,
            "channelid"::text as "channelid",
            "channel_name"::text as "channelname"`;
            }

            if (groupByFields.includes('country') || showAllFields) {
                selectFields += `,
            "country"::text as "country"`;
            }

            if (groupByFields.includes('styleId') || showAllFields) {
                selectFields += `,
            "styleid"::text as "styleid",
            "style_name"::text as "stylename"`;
            }

            return selectFields;
        };

        const selectFieldsString = buildSelectFields(showGSTValue, includesDate, groupByFields);

        let orderByClause = `"${finalOrderBy}" ${orderDir}`;

        if (includesDate && finalOrderBy !== 'date') {
            orderByClause += ', "date" DESC';
        } else if (!includesDate && finalOrderBy !== 'ads_spend') {
            orderByClause += ', "ads_spend" DESC';
        }

        if (groupByFields.includes('all')) {
            orderByClause += ', "date" DESC, "article_name", "channel_name", "country", "style_name"';
        } else {
            if (groupByFields.includes('article') && finalOrderBy !== 'articlename') {
                orderByClause += ', "article_name"';
            }
            if (groupByFields.includes('channel') && finalOrderBy !== 'channelname') {
                orderByClause += ', "channel_name"';
            }
            if (groupByFields.includes('country') && finalOrderBy !== 'country') {
                orderByClause += ', "country"';
            }
            if (includesStyleId && finalOrderBy !== 'stylename') {
                orderByClause += ', "style_name"';
            }
        }

        const rawData = await prisma.$queryRawUnsafe<RevenueCampaignData[]>(`
            SELECT ${selectFieldsString}
            FROM pg_temp.temp_ads_result
            ORDER BY ${orderByClause}
        `);

        return NextResponse.json({
            success: true,
            data: rawData
        });

    } catch (error) {
        console.error('Error fetching revenue campaign data:', error);

        if (error instanceof Error) {
            if (error.message.includes('42703')) {
                return NextResponse.json(
                    { error: 'Database column does not exist. Please check the database function or temporary table schema.' },
                    { status: 500 }
                );
            }
            if (error.message.includes('42P01')) {
                return NextResponse.json(
                    { error: 'Temporary table pg_temp.temp_ads_result does not exist. Please check the database function.' },
                    { status: 500 }
                );
            }
        }

        return NextResponse.json(
            {
                error: 'Failed to fetch revenue campaign data',
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    }
}