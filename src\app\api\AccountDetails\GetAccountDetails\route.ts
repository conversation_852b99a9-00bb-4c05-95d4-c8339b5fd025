import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
import { GoogleAdsService } from '@/services/googleCampaignService';
import { prisma } from '../../../../lib/prisma';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const googleAdsService = new GoogleAdsService();

        const customerClients = await googleAdsService.getCustomerClients();

        // 4. Process and update database
        const updatePromises = customerClients.map(async (client, index) => {
            const accountId = client.customerClient.id;
            const resourceName = client.customerClient.resourceName;
            const clientCustomer = client.customerClient.clientCustomer;
            const level = parseInt(client.customerClient.level);
            const timeZone = client.customerClient.timeZone;
            const isManager = Boolean(client.customerClient.manager);
            const descriptiveName = client.customerClient.descriptiveName;
            const currencyCode = client.customerClient.currencyCode;

            return prisma.ads_AccountDetails.upsert({
                where: { AccountId: BigInt(accountId) },
                update: {
                    ResourceName: resourceName,
                    ClientCustomer: clientCustomer,
                    Level: level,
                    TimeZone: timeZone,
                    Manager: isManager,
                    DescriptiveName: descriptiveName,
                    CurrencyCode: currencyCode
                },
                create: {
                    AccountId: BigInt(accountId),
                    ResourceName: resourceName,
                    ClientCustomer: clientCustomer,
                    Level: level,
                    TimeZone: timeZone,
                    Manager: isManager,
                    DescriptiveName: descriptiveName,
                    CurrencyCode: currencyCode,
                }
            });
        });

        // 5. Execute all updates
        const updateResults = await Promise.all(updatePromises);

        // 6. Return success response
        return NextResponse.json({
            success: true,
            message: 'Customer data synchronized successfully',
        });

    } catch (error) {
        console.error('Google Ads API error:', error);
        return NextResponse.json(
            {
                error: 'Failed to synchronize customer data',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}