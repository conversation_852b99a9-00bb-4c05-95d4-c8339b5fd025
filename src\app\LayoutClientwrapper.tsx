"use client";

import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Sidebar } from "@/components/Layouts/sidebar";
import { PropsWithChildren } from "react";
import { Header } from "@/components/Layouts/header";
import { useSidebarContext } from "@/components/Layouts/sidebar/sidebar-context";
import Footer from "@/components/Layouts/footer/Footer";

export function LayoutClientWrapper({ children }: PropsWithChildren) {
  const pathname = usePathname();
  const { isMobile, isCollapsed } = useSidebarContext();

  const hideLayout = pathname.startsWith("/auth");

  if (hideLayout) {
    return <main className="isolate mx-auto w-full max-w-screen-2xl overflow-hidden p-4 md:p-6 2xl:p-10">
      {children}
    </main>;
  }

  return (
    <div className="relative min-h-screen">
      <Sidebar />
      <div
        className={cn(
          "min-h-screen transition-all duration-300 bg-gray-2 dark:bg-[#020d1a]",
          isMobile
            ? "ml-0"
            : isCollapsed
              ? "ml-24" 
              : "ml-70" 
        )}
      >
        <Header />
        <main className="isolate mx-auto w-full overflow-hidden">
          {children}
        </main>
        <Footer />
      </div>
    </div>

  );
}
