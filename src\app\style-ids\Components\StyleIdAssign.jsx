"use client";
import React, { useCallback, useEffect, useState } from "react";
import CustomDataTable from "../../../components/DataTable/CustomDataTable";
import Swal from "sweetalert2";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import InputGroup from "@/components/FormElements/InputGroup";
import apiClient from "@/utils/axiosInstance";

const StyleIdAssign = () => {
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState("");
  const [tableData, setTableData] = useState({
    loading: false,
    rows: [],
    totalCount: 0,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("CreatedAt");

  const columns = [
    { id: "StyleId", label: "Style ID" },
    { id: "Name", label: "Name" },
    // { id: "Prefix", label: "Prefix" },
    {
      id: "CreatedAt",
      label: "Assigned On",
    },
  ];

  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get("/adminuser/GetDropdown");

      if (response?.status === 200) {
        setUsers(response.data.data || []);
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load users. Please try again.",
        timer: 2000,
        showConfirmButton: false,
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchAssignedStyleIds = useCallback(
    async ({ start, length, search }) => {
      if (!selectedUser) {
        return { data: [], total: 0 };
      }
      setTableData((prev) => ({ ...prev, loading: true }));

      try {
        const response = await apiClient.get("/AssignUserStyleId/Get", {
          params: {
            Id: selectedUser,
            q: search,
            page: Math.floor(start / length) + 1,
            length,
            orderBy,
            order,
            _: Date.now(), // cache-busting if needed
          },
        });        
        const formattedData = (response.data.data || []).map((item) => ({
          ...item,
          id: item.Id,
          CreatedAt: new Date(item.CreatedAt).toLocaleString(),
        }));

        return {
          data: formattedData,
          total: response.data.pagination?.recordsFiltered || 0,
        };
      } catch (error) {
        console.error("Error fetching data:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text:
            error?.response?.data?.error || "Failed to load assigned style IDs",
          timer: 2000,
          showConfirmButton: false,
        });
        return { data: [], total: 0 };
      } finally {
        setTableData((prev) => ({ ...prev, loading: false }));
      }
    },
    [selectedUser, orderBy, order],
  );

  const handleDelete = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: `You are about to delete ${rowData.Name}`,
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await apiClient.delete("/StyleIdUserMapping/delete", {
          data: { Id: rowData.Id },
        });        

        Swal.fire({
          icon: "success",
          title: "Deleted!",
          text: "Style ID has been deleted.",
          timer: 2000,
          showConfirmButton: false,
        });

        const result = await fetchAssignedStyleIds({
          start: page * rowsPerPage,
          length: rowsPerPage,
          search: searchTerm,
        });
        setTableData({
          rows: result.data,
          totalCount: result.total,
        });
      } catch (error) {
        Swal.fire({
          icon: "error",
          title: "Error!",
          text: error.response?.data?.error || "Failed to delete",
          timer: 2000,
          showConfirmButton: false,
        });
      }
    }
  };

  const handleUserChange = (item) => {
    setSelectedUser(item?.Id ? item?.Id : "");
    setPage(0);
    setSearchTerm("");
  };

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleChangePage = (newPage) => {
    setPage(newPage);
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedUser) {
        setTableData({ rows: [], totalCount: 0 });
        return;
      }

      try {
        const result = await fetchAssignedStyleIds({
          start: page * rowsPerPage,
          length: rowsPerPage,
          search: searchTerm,
        });
        setTableData({
          rows: result.data,
          totalCount: result.total,
        });
      } catch (error) {
        console.error("Error in fetchData:", error);
        setTableData({ rows: [], totalCount: 0 });
      }
    };

    fetchData();
  }, [selectedUser, searchTerm, page, rowsPerPage, order, orderBy, fetchAssignedStyleIds]);

  useEffect(() => {
    fetchUsers();
    setPage(0);
  }, [fetchUsers]);

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">Style ID Assignments</h1>
          </div>
        </div>
        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
  <div className="mb-5 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
      {selectedUser && (
        <>
          <div className="w-full sm:w-64">
            <InputGroup
              label="Search"
              placeholder="Search..."
              value={searchTerm}
              handleChange={(e) => {
                setSearchTerm(e.target.value);
                setPage(0);
              }}
              type="text"
              className="w-full"
            />
          </div>
        </>
      )}
    </div>

    {isLoading ? (
      <div className="flex justify-center py-8 sm:w-64">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    ) : (
      <div className="min-w-64 max-w-full inline-block">
        <SearchableDropdown
          label="Select User"
          options={users}
          placeholder="Select a User"
          value={selectedUser}
          onChange={handleUserChange}
          displayKey="Name"
          displayKey2="Email"
          idKey="Id"
        />
      </div>
    )}
  </div>

  {selectedUser && (
    <div className="mb-8">
      <CustomDataTable
        key={selectedUser}
        isLoading={tableData.loading}
        columns={columns.map((col) => ({
          id: col.id,
          label: col.label,
        }))}
        rows={tableData.rows}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handleChangePage}
        onRowsPerPageChange={setRowsPerPage}
        totalCount={tableData.totalCount}
        onDelete={handleDelete}
        order={order}
        orderBy={orderBy}
        onRequestSort={handleRequestSort}
      />
    </div>
  )}
</div>

      </div>
    </>
  );
};

export default StyleIdAssign;