"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface ContentCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  className?: string;
  headerActions?: React.ReactNode;
  noPadding?: boolean;
  variant?: 'default' | 'outlined' | 'elevated';
  size?: 'sm' | 'md' | 'lg';
}

const ContentCard: React.FC<ContentCardProps> = ({
  children,
  title,
  subtitle,
  className = '',
  headerActions,
  noPadding = false,
  variant = 'default',
  size = 'md',
}) => {
  const baseClasses = "bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 rounded-xl";
  
  const variantClasses = {
    default: "",
    outlined: "border-2",
    elevated: "shadow-lg",
  };

  const sizeClasses = {
    sm: noPadding ? "" : "p-3",
    md: noPadding ? "" : "p-4 sm:p-6",
    lg: noPadding ? "" : "p-6 sm:p-8",
  };

  return (
    <div className={cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      className
    )}>
      {(title || subtitle || headerActions) && (
        <div className={cn(
          "flex items-center justify-between mb-4",
          noPadding && "px-4 pt-4"
        )}>
          <div>
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {headerActions && (
            <div className="flex items-center gap-2">
              {headerActions}
            </div>
          )}
        </div>
      )}
      
      <div className={cn(noPadding && title && "px-4 pb-4")}>
        {children}
      </div>
    </div>
  );
};

export default ContentCard;
