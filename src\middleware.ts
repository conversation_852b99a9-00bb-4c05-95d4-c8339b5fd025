import { NextResponse, type NextRequest } from 'next/server';
import { jwtVerify, SignJWT } from 'jose';

const PUBLIC_PATHS = ['/auth/sign-in', '/api/auth/refresh-token','/api/auth/login'];

interface JWTPayload {
  Id: string;
  Email: string;
  User_Type: string;
  Name: string;
  ProfilePic: string;
  exp: number;
  iat: number;
}

// Helper to create a new access token using `jose`
async function createAccessToken(payload: JWTPayload): Promise<string> {
  const secret = new TextEncoder().encode(process.env.JWT_SECRET!);

  return await new SignJWT({
    Id: payload.Id,
    Email: payload.Email,
    User_Type: payload.User_Type,
    Name: payload.Name,
    ProfilePic: payload.ProfilePic,
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('15m') // Access token valid 15 minutes
    .setIssuedAt()
    .sign(secret);
}

// Helper to check if token will expire soon (within 5 minutes)
function shouldRefreshToken(payload: JWTPayload): boolean {
  const currentTime = Math.floor(Date.now() / 1000);
  const timeUntilExpiry = payload.exp - currentTime;
  return timeUntilExpiry < 300; // 5 minutes
}

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Skip auth for public paths
  if (PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }

  const accessToken = req.cookies.get('accessToken')?.value;
  const refreshToken = req.cookies.get('refreshToken')?.value;
  const secret = new TextEncoder().encode(process.env.JWT_SECRET!);

  const restrictedPaths = ['/domains', '/subdomains', '/style-ids', '/allUser'];

  let tokenRefreshed = false;
  let validPayload: JWTPayload | null = null;

  // Create request headers object
  const requestHeaders = new Headers(req.headers);

  // Step 1: Verify access token
  if (accessToken) {
    try {
      const { payload } = await jwtVerify(accessToken, secret) as { payload: JWTPayload };
      validPayload = payload;

      // Proactive refresh if token near expiry
      if (shouldRefreshToken(payload) && refreshToken) {
        try {
          await jwtVerify(refreshToken, secret);
          tokenRefreshed = true;
        } catch {
          console.warn('Refresh token invalid during proactive refresh');
        }
      }
    } catch (error: any) {
      if (error?.code === 'ERR_JWT_EXPIRED') {
        console.warn(`Access token expired for ${pathname}`);
        tokenRefreshed = true;
      } else {
        console.warn(`Access token invalid for ${pathname}:`, error);
      }
    }
  }

  // Step 2: If no valid access token or needs refresh, try refresh token
  if ((!validPayload || tokenRefreshed) && refreshToken) {
    try {
      const { payload } = await jwtVerify(refreshToken, secret) as { payload: JWTPayload };
      validPayload = payload;
      tokenRefreshed = true;
    } catch (error: any) {
      console.warn(`Refresh token invalid or expired for ${pathname}`, error);
      validPayload = null;
    }
  }
  // Step 3: If we have a valid payload
  if (validPayload) {
    // Restricted route check
    if (
      (validPayload.User_Type !== 'Super Admin' && validPayload.User_Type !== 'Admin') &&
      restrictedPaths.some(path => pathname.startsWith(path))
    ) {
      if (pathname.startsWith('/api/')) {
        return NextResponse.json(
          { error: 'Access denied' },
          { status: 403 }
        );
      }
      
      console.warn(`Access denied: ${validPayload.User_Type} tried to access ${pathname}`);
      return NextResponse.redirect(new URL('/', req.url));
    }

    // Attach user info to request headers for API routes
    requestHeaders.set('x-user-id', validPayload.Id);
    requestHeaders.set('x-user-email', validPayload.Email);
    requestHeaders.set('x-user-role', validPayload.User_Type);
    requestHeaders.set('x-user-name', validPayload.Name);
    // Prepare NextResponse
    const res = NextResponse.next({ request: { headers: requestHeaders } });
    // Refresh access token if needed
    if (tokenRefreshed) {
      try {
        const newAccessToken = await createAccessToken(validPayload);
        res.cookies.set('accessToken', newAccessToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          path: '/',
          maxAge: 15 * 60, // 15 minutes
        });
        res.headers.set('x-token-refreshed', 'true');
        console.log(`Access token refreshed for ${validPayload.Email}`);
       } catch (refreshError) {
        console.error('Failed to refresh access token:', refreshError);
        
        // For API routes, return error response instead of redirecting
        if (pathname.startsWith('/api/')) {
          return NextResponse.json(
            { error: 'Authentication failed' },
            { status: 401 }
          );
        }
        
        return NextResponse.redirect(new URL('/auth/sign-in', req.url));
      }
    }

    return res;
  }

  // Step 4: No valid tokens, redirect to login
  console.warn(`Unauthenticated access attempted to ${pathname}`);

   if (pathname.startsWith('/api/')) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }
  const res = NextResponse.redirect(new URL('/auth/sign-in', req.url));
  res.cookies.set('accessToken', '', { maxAge: 0, path: '/' });
  res.cookies.set('refreshToken', '', { maxAge: 0, path: '/' });
  return res;
}

// Protected routes
export const config = {
  matcher: [
    '/',
    '/(admin|category|articles|reports|revenue|users|settings|domains|subdomains|style-ids|forms|allUser|keywords-analytics)(/.*)?',
    '/api/:path*', // <-- Add this
  ],
};

