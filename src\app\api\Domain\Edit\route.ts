import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { v4 as uuidv4 } from 'uuid';
import { writeFile, access, constants, mkdir, unlink } from 'fs/promises';
import path from 'path';
import { existsSync } from 'fs';
import sharp from 'sharp';
// import { sendKafkaEvent } from '@/lib/kafka';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || '/var/www/images';
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MIN_FILE_SIZE = 1024;

type SharpImageFormat = keyof sharp.FormatEnum;

function isFile(value: FormDataEntryValue | null): value is File {
    return value !== null && typeof value === 'object' && 'size' in value && 'type' in value && 'name' in value;
}

export async function PUT(req: NextRequest) {
    try {
        // type AuthenticatedUser = {
        //     Id: string;
        // };
        // const user = await verifyToken(req) as AuthenticatedUser;
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const formData = await req.formData();
        const id = formData.get('Id')?.toString();

        if (!id) {
            return NextResponse.json(
                { error: 'Id is required for updating a Domain' },
                { status: 400 }
            );
        }

        const existingDomain = await prisma.domain.findFirst({
            where: { Id: id, IsDeleted: false }
        });

        if (!existingDomain) {
            return NextResponse.json(
                { error: "Domain not found or you don't have permission to update it" },
                { status: 404 }
            );
        }

        const newName = formData.get('Name')?.toString();
        if (newName && newName !== existingDomain.Name) {
            const nameConflict = await prisma.domain.findFirst({
                where: {
                    Name: newName,
                    IsDeleted: false,
                    NOT: { Id: id }
                }
            });
            if (nameConflict) {
                return NextResponse.json({ error: 'Name already exists' }, { status: 409 });
            }
        }

        const newShowUrlName = formData.get('ShowUrlName')?.toString();
        if (newShowUrlName && newShowUrlName !== existingDomain.ShowUrlName) {
            const showUrlConflict = await prisma.domain.findFirst({
                where: {
                    ShowUrlName: newShowUrlName,
                    IsDeleted: false,
                    NOT: { Id: id }
                }
            });
            if (showUrlConflict) {
                return NextResponse.json({ error: 'ShowUrlName already exists' }, { status: 409 });
            }
        }

        try {
            if (!existsSync(UPLOAD_DIR)) {
                await mkdir(UPLOAD_DIR, { recursive: true });
            }
            await access(UPLOAD_DIR, constants.W_OK);
        } catch (err) {
            console.error('Upload directory error:', err);
            return NextResponse.json(
                { error: `Server configuration error: ${err instanceof Error ? err.message : 'upload directory not accessible'}` },
                { status: 500 }
            );
        }

        const getString = (key: string) => {
            if (!formData.has(key)) {
                return undefined;
            }
            const value = formData.get(key);
            if (value === null || value === '' || value === 'null') {
                return null;
            }
            return value.toString();
        };

        const getNumber = (key: string) => {
            if (!formData.has(key)) {
                return undefined;
            }
            const value = formData.get(key);
            if (value === null || value === '' || value === 'null') {
                return null;
            }
            return Number(value);
        };

        const getBigInt = (key: string): bigint | null | undefined => {
            if (!formData.has(key)) {
                return undefined;
            }
            const value = formData.get(key);
            if (value === null || value === '' || value === 'null') {
                return null;
            }
            return BigInt(value.toString());
        };

        function isSharpImageFormat(format: string): format is SharpImageFormat {
            const sharpFormats: SharpImageFormat[] = ['jpeg', 'png', 'webp', 'gif', 'tiff', 'raw'];
            return sharpFormats.includes(format as SharpImageFormat);
        }

        const deleteOldImageFiles = async (oldFileName: string | null) => {
            if (!oldFileName) return;

            try {
                const baseName = oldFileName.replace(/\.[^/.]+$/, '');
                const extension = oldFileName.split('.').pop();

                const filesToDelete = [
                    oldFileName,
                    `${baseName}_small.${extension}`,
                    `${baseName}_medium.${extension}`
                ];

                await Promise.all(
                    filesToDelete.map(async (fileName) => {
                        const filePath = path.join(UPLOAD_DIR, fileName);
                        try {
                            if (existsSync(filePath)) {
                                await unlink(filePath);
                            }
                        } catch (deleteError) {
                            console.warn(`Could not delete old file ${fileName}:`, deleteError);
                        }
                    })
                );
            } catch (error) {
                console.warn('Error deleting old image files:', error);
            }
        };

        const processImage = async (file: File | null, existingPath: string | null, prefix: string): Promise<string | null | undefined> => {
            const fileField = `${prefix?.toLowerCase()}Logo`;

            if (!formData.has(fileField)) {
                return undefined;
            }

            if (!file || file.size === 0) {
                return null;
            }

            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                let mimeType = file.type;
                let originalExtension: SharpImageFormat = 'jpeg';

                if (!mimeType || mimeType === 'application/octet-stream' || mimeType === '') {
                    try {
                        const metadata = await sharp(buffer).metadata();
                        if (metadata.format && isSharpImageFormat(metadata.format)) {
                            mimeType = `image/${metadata.format}`;
                            originalExtension = metadata.format;
                        } else {
                            console.warn(`Invalid file format for ${prefix}.`);
                            return null;
                        }
                    } catch (sharpError) {
                        console.error('Sharp metadata error:', sharpError);
                        console.warn(`Could not process ${prefix} image.`);
                        return null;
                    }
                } else {
                    const ext = mimeType.split('/')[1];
                    if (ext && isSharpImageFormat(ext)) {
                        originalExtension = ext;
                    } else {
                        console.warn(`Invalid file type: ${mimeType}.`);
                        return null;
                    }
                }

                if (!ALLOWED_IMAGE_TYPES.includes(mimeType)) {
                    console.warn(`Invalid file type: ${mimeType}. Allowed types:`, ALLOWED_IMAGE_TYPES);
                    return null;
                }

                if (file.size < MIN_FILE_SIZE) {
                    console.warn(`File too small: ${file.size} bytes (minimum: ${MIN_FILE_SIZE}).`);
                    return null;
                }

                const uniqueId = uuidv4();
                const baseFileName = `${prefix?.toLowerCase()}_${uniqueId}.${originalExtension}`;
                const smallFileName = `${prefix?.toLowerCase()}_${uniqueId}_small.${originalExtension}`;
                const mediumFileName = `${prefix?.toLowerCase()}_${uniqueId}_medium.${originalExtension}`;

                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                const filePaths = [
                    { path: path.join(UPLOAD_DIR, baseFileName), buffer: originalBuffer },
                    { path: path.join(UPLOAD_DIR, smallFileName), buffer: smallBuffer },
                    { path: path.join(UPLOAD_DIR, mediumFileName), buffer: mediumBuffer }
                ];

                await Promise.all(
                    filePaths.map(async ({ path: filePath, buffer }) => {
                        try {
                            await writeFile(filePath, buffer);
                        } catch (writeError) {
                            console.error(`Failed to write ${filePath}:`, writeError);
                            throw writeError;
                        }
                    })
                );

                for (const { path: filePath } of filePaths) {
                    if (!existsSync(filePath)) {
                        console.error(`File verification failed: ${filePath} does not exist after write`);
                        throw new Error(`File write verification failed: ${filePath}`);
                    }
                }

                if (existingPath && existingPath !== baseFileName) {
                    await deleteOldImageFiles(existingPath);
                }

                return baseFileName;

            } catch (error) {
                console.error(`Error processing ${prefix} image:`, error);
                console.warn(`Could not process ${prefix} image due to error.`);
                return null;
            }
        };

        const wideLogoEntry = formData.get('wideLogo');
        const squareLogoEntry = formData.get('squareLogo');

        const wideLogo = isFile(wideLogoEntry) ? wideLogoEntry : null;
        const squareLogo = isFile(squareLogoEntry) ? squareLogoEntry : null;

        const [wideLogoPath, squareLogoPath] = await Promise.all([
            processImage(wideLogo, existingDomain.WideLogo, 'wide'),
            processImage(squareLogo, existingDomain.SquareLogo, 'square')
        ]);

        const updateData: any = {
            UpdatedAt: new Date()
        };

        const nameValue = getString('Name');
        if (nameValue !== undefined) updateData.Name = nameValue;

        const prefixValue = getString('Prefix');
        if (prefixValue !== undefined) {
            const currentPrefixIsEmpty = existingDomain.Prefix === null || existingDomain.Prefix === '';
            if (currentPrefixIsEmpty) {
                if (prefixValue === null || prefixValue === '') {
                    updateData.Prefix = null; 
                } else if (prefixValue.trim() !== '') {
                    updateData.Prefix = prefixValue; 
                }
            } else {
                if (prefixValue === null || prefixValue === '') {
                    updateData.Prefix = null; 
                }
            }
        }

        const showUrlNameValue = getString('ShowUrlName');
        if (showUrlNameValue !== undefined) updateData.ShowUrlName = showUrlNameValue;

        const channelIdValue = getString('ChannelId');
        if (channelIdValue !== undefined) updateData.ChannelId = channelIdValue;

        const cookieMinutesValue = getNumber('CookieMinutes');
        if (cookieMinutesValue !== undefined) updateData.CookieMinutes = cookieMinutesValue;

        const styleIdDmValue = getBigInt('StyleIdDm');
        if (styleIdDmValue !== undefined) updateData.StyleIdDm = styleIdDmValue;

        const styleIdLmValue = getBigInt('StyleIdLm');
        if (styleIdLmValue !== undefined) updateData.StyleIdLm = styleIdLmValue;

        const headTagScriptValue = getString('HeadTagScript');
        if (headTagScriptValue !== undefined) updateData.HeadTagScript = headTagScriptValue;

        const headTagScriptLandingPageValue = getString('HeadTagScriptLandingPage');
        if (headTagScriptLandingPageValue !== undefined) updateData.HeadTagScriptLandingPage = headTagScriptLandingPageValue;

        const headTagScriptSearchPageValue = getString('HeadTagScriptSearchPage');
        if (headTagScriptSearchPageValue !== undefined) updateData.HeadTagScriptSearchPage = headTagScriptSearchPageValue;

        const gIdValue = getString('GId');
        if (gIdValue !== undefined) updateData.GId = gIdValue;

        const awIdValue = getString('AWId');
        if (awIdValue !== undefined) updateData.AWId = awIdValue;

        const sendToValue = getString('SendTo');
        if (sendToValue !== undefined) updateData.SendTo = sendToValue;

        if (wideLogoPath !== undefined) updateData.WideLogo = wideLogoPath;
        if (squareLogoPath !== undefined) updateData.SquareLogo = squareLogoPath;

        const contactEmailValue = getString('ContactEmail');
        if (contactEmailValue !== undefined) updateData.ContactEmail = contactEmailValue;

        const addressValue = getString('Address');
        if (addressValue !== undefined) updateData.Address = addressValue;

        const adsTxtContentValue = getString('AdsTxtContent');
        if (adsTxtContentValue !== undefined) updateData.AdsTxtContent = adsTxtContentValue;

        const adsProviderIdValue = getString('AdsProviderId');
        if (adsProviderIdValue !== undefined) updateData.AdsProviderId = adsProviderIdValue;

        const TrackingKey = getString('TrackingKey');
        if (adsProviderIdValue !== undefined) updateData.TrackingKey = TrackingKey;

        const adsProviderDescriptionValue = getString('AdsProviderDescription');
        if (adsProviderIdValue) {
            try {
                await prisma.adsProvider.update({
                    where: { Id: adsProviderIdValue },
                    data: {
                        Description: adsProviderDescriptionValue,
                        TrackingKey: TrackingKey
                    }
                });
            } catch (error) {
                console.error('Error updating AdsProvider description:', error);
            }
        }

        const updatedDomain = await prisma.domain.update({
            where: { Id: id },
            data: updateData
        });
        // try {

        //     const payload = {
        //         'Type': "domain",
        //         'Id': id,
        //     };

        //     await sendKafkaEvent(payload);

        // } catch (kafkaError) {
        //     console.error("Kafka event failed:", kafkaError);
        // }
        return NextResponse.json({
            success: true,
            message: 'Domain updated successfully',
        });

    } catch (error) {
        console.error('Error processing request:', error);

        if (error instanceof Error) {
            if (error.message.includes('Unique constraint')) {
                return NextResponse.json(
                    { error: 'A domain with this name already exists' },
                    { status: 409 }
                );
            }

            if (error.message.includes('Foreign key constraint')) {
                return NextResponse.json(
                    { error: 'Invalid reference data provided' },
                    { status: 400 }
                );
            }
        }

        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}