import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

export async function PUT(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        // Parse form data
        const formData = await req.formData();

        // Extract text fields
        const Id = formData.get('Id') as string | null;
        const Name = formData.get('Name') as string | null;
        const Email = formData.get('Email') as string | null;
        const User_Type = formData.get('User_Type') as string | null;
        const Number = formData.get('Number') as string | null;
        const AccessExpiration = formData.get('AccessExpiration') as string | null;
        const Status = formData.get('Status') as string | null;
        const DisplayName = formData.get('DisplayName') as string | null;
        const file = formData.get('ProfilePic') as File | null;
        const AboutMe = formData.get('AboutMe') as string | null;

        if (!Id) {
            return NextResponse.json(
                { error: "User ID is required" },
                { status: 400 }
            );
        }

        // Check if the user exists
        const existingUser = await prisma.adminUser.findUnique({
            where: { Id }
        });

        if (!existingUser) {
            return NextResponse.json(
                { error: "User not found" },
                { status: 404 }
            );
        }

        // Email uniqueness check
        if (Email && Email !== existingUser.Email) {
            const emailExists = await prisma.adminUser.findFirst({
                where: {
                    Email,
                    IsDeleted: false,
                    NOT: { Id }
                }
            });
            if (emailExists) {
                return NextResponse.json(
                    { error: "Email already in use by another user" },
                    { status: 409 }
                );
            }
        }

        // Handle file upload if a new file is provided
        let imagePath = existingUser.ProfilePic; // default to existing image

        if (file && file.size > 0) {
            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                const mimeType = file.type;
                const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
                const uniqueId = uuidv4();

                if (!fs.existsSync(UPLOAD_DIR)) {
                    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
                }

                // Process image versions in parallel
                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                // Save all versions
                const baseFileName = `${uniqueId}.${originalExtension}`;
                const versions = [
                    { suffix: '', buffer: originalBuffer },
                    { suffix: '_small', buffer: smallBuffer },
                    { suffix: '_medium', buffer: mediumBuffer }
                ];

                await Promise.all(versions.map(({ suffix, buffer }) => {
                    const fileName = `${uniqueId}${suffix}.${originalExtension}`;
                    const filePath = path.resolve(UPLOAD_DIR, fileName);
                    return fs.promises.writeFile(filePath, buffer);
                }));

                imagePath = baseFileName;
            } catch (error: unknown) {
                console.error("Image processing error:", error);
                // Continue with existing image if processing fails
            }
        }

        // Prepare update data - start with existing values
        const updateData: any = {
            UpdatedAt: new Date(),
            // Initialize with existing values
            Name: existingUser.Name,
            Email: existingUser.Email,
            User_Type: existingUser.User_Type,
            Number: existingUser.Number,
            Status: existingUser.Status,
            DisplayName: existingUser.DisplayName,
            ProfilePic: imagePath, // Either existing or new image
            AccessExpiration: existingUser.AccessExpiration,
            AboutMe: existingUser.AboutMe
        };

        if (Name) updateData.Name = Name;
        if (Email) updateData.Email = Email;
        if (User_Type) updateData.User_Type = User_Type;
        if (Number) updateData.Number = Number;
        if (Status !== null && Status !== "") updateData.Status = Status === 'true';
        if (DisplayName) updateData.DisplayName = DisplayName;
        if (AboutMe) updateData.AboutMe = AboutMe;

        // Special handling for AccessExpiration
        if (AccessExpiration !== null) {
            if (AccessExpiration === "") {
                updateData.AccessExpiration = null;
            } else if (AccessExpiration) {
                const expirationDate = new Date(AccessExpiration);
                if (isNaN(expirationDate.getTime())) {
                    return NextResponse.json(
                        { error: "Invalid date format for AccessExpiration" },
                        { status: 400 }
                    );
                }
                updateData.AccessExpiration = expirationDate;
            }
        }

        // Update user
        await prisma.adminUser.update({
            where: { Id },
            data: updateData
        });

        return NextResponse.json({
            success: true,
            message: "User updated successfully",
        }, { status: 200 });

    } catch (error) {
        console.error("Error updating user:", error);
        return NextResponse.json(
            { error: "Internal Server Error", details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    } 
}