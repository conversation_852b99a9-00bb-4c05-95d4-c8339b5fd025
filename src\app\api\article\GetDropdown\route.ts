// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

// export async function GET(req: NextRequest) {
//     try {
//         type AuthenticatedUser = {
//             User_Type: string;
//             Id: string;
//         };

//         const user = await verifyToken(req) as AuthenticatedUser;
//         if (!user) {
//             return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         }

//         const { searchParams } = new URL(req.url);
//         const userIdFromParam = searchParams.get('userId');

//         let where: any = {
//             IsDeleted: false
//         };

//         const effectiveUserId =
//             user.User_Type === 'Super Admin' || user.User_Type === 'Admin'
//                 ? userIdFromParam
//                 : user.Id;

//         // if (user.User_Type !== 'Super Admin' && user.User_Type !== 'Admin') {
//             const userStyleMappings = await prisma.styleIdUserMappings.findMany({
//                 where: {
//                     UserId: effectiveUserId
//                 },
//                 select: {
//                     StyleId: true
//                 }
//             });

//             const userStyleIds = userStyleMappings.map(mapping => mapping.StyleId).filter(Boolean) as string[];

//             if (userStyleIds.length === 0) {
//                 return NextResponse.json({
//                     success: true,
//                     data: [],
//                 });
//             }

//             const styleIds = await prisma.styleIds.findMany({
//                 where: {
//                     Id: {
//                         in: userStyleIds
//                     }
//                 },
//                 select: {
//                     Id: true,
//                 }
//             });

//             const validStyleIds = [
//                 ...new Set(
//                     styleIds.flatMap(style => [
//                         style.Id
//                     ]).filter(Boolean)
//                 )
//             ] as string[];

//             if (validStyleIds.length === 0) {
//                 return NextResponse.json({
//                     success: true,
//                     data: [],
//                 });
//             }

//             where = {
//                 ...where,
//                 User_Id_Settings: effectiveUserId,
//                 OR: [
//                     {
//                         StyleIdLm: {
//                             in: validStyleIds
//                         },
//                     },
//                     {
//                         StyleIdDm: {
//                             in: validStyleIds
//                         }
//                     },
//                 ]
//             };


//         const articleWithUsers = await prisma.articleDetails.findMany({
//             where,
//             orderBy: {
//                 Title: 'asc'
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//             }
//         });

//         const transformedData = articleWithUsers.map(articleDetails => ({
//             ...articleDetails,
//         }));

//         return NextResponse.json({
//             success: true,
//             data: transformedData,
//         });

//     } catch (error) {
//         console.error("Error processing request:", error);
//         return NextResponse.json(
//             { error: "Internal Server Error" },
//             { status: 500 }
//         );
//     }
// };


import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // type AuthenticatedUser = {
        //     User_Type: string;
        //     Id: string;
        // };

        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        // const user = await verifyToken(req) as AuthenticatedUser;
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const userIdFromParam = searchParams.get('userId');
        let where: any = {
            IsDeleted: false
        };
        if (role === 'Super Admin' || role === 'Admin') {
            if (userIdFromParam) {
                where.User_Id_Settings = userIdFromParam;
            }
        } else {
            where.User_Id_Settings = userId;
        }


        const articleWithUsers = await prisma.articleDetails.findMany({
            where,
            orderBy: {
                Title: 'asc'
            },
            select: {
                Id: true,
                Title: true,
            }
        });



        return NextResponse.json({
            success: true,
            data: articleWithUsers,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
};
