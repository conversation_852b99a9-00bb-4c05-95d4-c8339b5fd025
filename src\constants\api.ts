// API endpoint constants

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    SIGN_IN: '/auth/sign-in',
    SIGN_OUT: '/auth/sign-out',
    REFRESH_TOKEN: '/auth/refresh-token',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
  },

  // Dashboard
  DASHBOARD: {
    GET_INFO: '/DashBoard/GetInfo',
  },

  // Users
  USERS: {
    GET_LIST: '/adminuser/GetList',
    GET_DROPDOWN: '/adminuser/GetDropDown',
    CREATE: '/adminuser/Create',
    UPDATE: '/adminuser/Update',
    DELETE: '/adminuser/Delete',
    BLOCK: '/adminuser/Block',
    RESET_PASSWORD: '/adminuser/ResetPassword',
    IN_PERSON_LOGIN: '/adminuser/InPersonLogin',
  },

  // Domains
  DOMAINS: {
    GET_LIST: '/Domain/GetList',
    GET_DROPDOWN: '/Domain/GetDropDown',
    CREATE: '/Domain/Create',
    UPDATE: '/Domain/Update',
    DELETE: '/Domain/Delete',
    ASSIGN_USERS: '/Domain/AssignUsers',
  },

  // Subdomains
  SUBDOMAINS: {
    GET_LIST: '/SubDomain/GetList',
    GET_DROPDOWN: '/SubDomain/GetDropDown',
    CREATE: '/SubDomain/Create',
    UPDATE: '/SubDomain/Update',
    DELETE: '/SubDomain/Delete',
    GET_USERS: '/SubDomainWiseUser/GetList',
  },

  // Style IDs
  STYLE_IDS: {
    GET_LIST: '/StyleIds/GetList',
    GET_DROPDOWN: '/StyleIds/GetDropDown',
    CREATE: '/StyleIds/Create',
    UPDATE: '/StyleIds/Update',
    DELETE: '/StyleIds/Delete',
    ASSIGN: '/StyleIds/Assign',
    GET_USERS: '/StyleIdUserMapping/GetList',
  },

  // Articles
  ARTICLES: {
    GET_LIST: '/article/GetList',
    CREATE: '/article/Create',
    UPDATE: '/article/Update',
    DELETE: '/article/Delete',
    GET_BY_ID: '/article/GetById',
  },

  // Categories
  CATEGORIES: {
    GET_LIST: '/category/GetList',
    GET_DROPDOWN: '/category/GetDropDown',
    CREATE: '/category/Create',
    UPDATE: '/category/Update',
    DELETE: '/category/Delete',
  },

  // Channels
  CHANNELS: {
    GET_LIST: '/Channals/GetList',
    GET_DROPDOWN: '/Channals/GetDropDown',
    CREATE: '/Channals/Create',
    UPDATE: '/Channals/Update',
    DELETE: '/Channals/Delete',
  },

  // Campaigns
  CAMPAIGNS: {
    GET_LIST: '/Campaigns/GetList',
    GET_DROPDOWN: '/Campaigns/GetDropDown',
    CREATE: '/Campaigns/Create',
    UPDATE: '/Campaigns/Update',
    DELETE: '/Campaigns/Delete',
  },

  // Ads Providers
  ADS_PROVIDERS: {
    GET_LIST: '/AdsProvider/GetList',
    GET_DROPDOWN: '/AdsProvider/GetDropDown',
    CREATE: '/AdsProvider/Create',
    UPDATE: '/AdsProvider/Update',
    DELETE: '/AdsProvider/Delete',
  },

  // Account Details
  ACCOUNT_DETAILS: {
    GET_LIST: '/AccountDetails/GetList',
    GET_DROPDOWN: '/AccountDetails/GetDropDown',
    CREATE: '/AccountDetails/Create',
    UPDATE: '/AccountDetails/Update',
    DELETE: '/AccountDetails/Delete',
  },

  // Reports
  REPORTS: {
    REVENUE: '/Revenue/GetList',
    KEYWORDS_ANALYTICS: '/KeywordAnalytics/GetList',
    TODAY_REFERRALS: '/TodayReferals/GetList',
    RC_ANALYTICS: '/RCAnalytics/GetList',
  },

  // Settings
  SETTINGS: {
    GET: '/AdminSetting/Get',
    UPDATE: '/AdminSetting/Update',
  },

  // Ads Accounts
  ADS_ACCOUNTS: {
    GET_LIST: '/ads-accounts/GetList',
  },
} as const;

// HTTP Methods
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
} as const;

// API Response Status
export const API_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  LOADING: 'loading',
} as const;
