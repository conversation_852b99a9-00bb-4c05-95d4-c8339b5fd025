import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const Id = searchParams.get("Id");

        if(!Id){
            return NextResponse.json(
                { error: "ads Providers Id is required" },
                { status: 400 }
            );
        }
        let where: any = {
            Id: Id
        };

        const users = await prisma.adsProvider.findFirst({
            where,
            select: {
                Id: true,
                Provider: true,
                Description: true,
                TrackingKey: true
            }
        });


        return NextResponse.json({
            success: true,
            data: users
        });

    } catch (error) {
        console.error("Error fetching ads Providers:", error);
        return NextResponse.json(
            { error: "Ads provider is not Found" },
            { status: 500 }
        );
    }
}