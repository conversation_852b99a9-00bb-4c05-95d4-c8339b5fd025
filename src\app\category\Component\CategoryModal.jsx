"use client";
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { IoCloseOutline } from "react-icons/io5";
import InputGroup from "@/components/FormElements/InputGroup";
import { Button } from "@/components/ui-elements/button";
import Checkbox from "@/components/FormElements/checkbox";

const CategoryModal = ({
  open,
  onClose,
  onSubmit,
  showLoader,
  selectedDomain,
  mode = "add", // 'add' or 'edit'
  initialData = null, // For edit mode
}) => {
  // Initialize form data
  const [formData, setFormData] = useState({
    name: "",
    title: "",
    shortDescription: "",
    showUrlName: "",
    image: null,
  });
  const [errors, setErrors] = useState({});
  const [editUrlMode, setEditUrlMode] = useState(false);
  const [isUrlManuallyModified, setIsUrlManuallyModified] = useState(false);

  // Generate slug function
  const generateSlug = (text) => {
    return text
      .replace(/[^\p{L}\p{N}\s-]/gu, "")
      .trim()
      .replace(/\s+/g, "-")
      .toLowerCase();
  };

  // Initialize form data when modal opens or initialData changes
  useEffect(() => {
    if (mode === "edit" && initialData) {
      setFormData({
        name: initialData.Name,
        title: initialData.Title || "",
        shortDescription: initialData.ShortDescription || "",
        showUrlName: initialData.ShowUrlName || generateSlug(initialData.Name),
        image: null,
      });
      setIsUrlManuallyModified(!!initialData.ShowUrlName);
      setEditUrlMode(false);
    } else {
      // Reset form for add mode
      setFormData({
        name: "",
        title: "",
        shortDescription: "",
        showUrlName: "",
        image: null,
      });
      setIsUrlManuallyModified(false);
      setEditUrlMode(true);
    }
  }, [mode, initialData]);

  // Handle field changes
  const handleChange = (e) => {
    const { name, value, files } = e.target;
    
    if (name === "image") {
      setFormData(prev => ({ ...prev, image: files?.[0] || null }));
      return;
    }
    
    const updates = { [name]: value };
    
    // Auto-generate URL when:
    // - Editing name field AND
    // - (In add mode OR URL not manually modified) AND
    // - Not in edit URL mode
    if (name === "name" && 
        (mode === "add" || !isUrlManuallyModified) &&
        !(mode === "edit" && !editUrlMode)) {
      updates.showUrlName = generateSlug(value);
    }
    
    // Track manual URL edits
    if (name === "showUrlName") {
      setIsUrlManuallyModified(true);
    }
    
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = "Category name is required";
    if (!formData.showUrlName.trim()) {
      newErrors.showUrlName = "URL name is required";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    const data = new FormData();
    data.append("name", formData.name.trim());
    data.append("title", formData.title.trim());
    data.append("shortDescription", formData.shortDescription.trim() || "");
    data.append("showUrlName", formData.showUrlName.trim() || "");
    data.append("domain", selectedDomain);
    if (formData.image) {
      data.append("image", formData.image);
    }

    if (mode === "edit") {
      data.append("id", initialData.Id);
    }

    onSubmit(data, mode);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {mode === "add" ? "Add New" : "Edit"} Category
        </span>
        <IconButton aria-label="close" onClick={onClose} sx={{ color: "white" }}>
          <IoCloseOutline size={24} />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form style={{ display: "flex", flexDirection: "column", gap: 16 }}>
          <InputGroup
            label="Category Name"
            type="text"
            name="name"
            value={formData.name}
            handleChange={handleChange}
            placeholder="Enter category name"
            required
            error={errors.name}
          />
          
          <div className="flex items-start gap-4">
            <div className="flex-1">
              <InputGroup
                label="URL Name"
                type="text"
                name="showUrlName"
                value={formData.showUrlName}
                handleChange={handleChange}
                placeholder="Enter URL name (e.g., category-name)"
                required
                error={errors.showUrlName}
                readOnly={mode === "edit" && !editUrlMode}
                disabled={mode === "edit" && !editUrlMode}
              />
            </div>

            {mode === "edit" && (
              <div className="mt-6">
                <Checkbox
                  label="Edit URL"
                  checked={editUrlMode}
                  withIcon="check"
                  withBg
                  radius="default"
                  onChange={(e) => {
                    const shouldEdit = e.target.checked;
                    setEditUrlMode(shouldEdit);

                    // When unchecking, regenerate from name
                    if (!shouldEdit) {
                      setFormData(prev => ({
                        ...prev,
                        showUrlName: generateSlug(prev.name)
                      }));
                      setIsUrlManuallyModified(false);
                    }
                  }}
                />
              </div>
            )}
          </div>

          <InputGroup
            label="Title"
            type="text"
            name="title"
            value={formData.title}
            handleChange={handleChange}
            placeholder="Enter category title"
          />
          <InputGroup
            label="Short Description"
            type="text"
            name="shortDescription"
            value={formData.shortDescription}
            handleChange={handleChange}
            placeholder="Enter short description"
          />
          <InputGroup
            label="Image"
            type="file"
            name="image"
            accept=".jpg,.jpeg,.png,.webp,.gif" 
            fileStyleVariant="style1"
            handleChange={handleChange}
            placeholder="Upload category image"
          />
        </form>
      </DialogContent>

      <DialogActions sx={{ py: 2, px: 3 }}>
       <Button
          type="submit"
          label={
            showLoader
              ? "Processing..."
              : mode === "add"
                ? "Add Category"
                : "Update Category"
          }
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
          disabled={showLoader}
        />
      </DialogActions>
    </Dialog>
  );
};

export default CategoryModal;