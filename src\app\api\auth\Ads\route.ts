import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const code = searchParams.get('code');

        if (!code) {
            return NextResponse.json({ error: 'Authorization code is required' }, { status: 400 });
        }

        const adminSettings = await prisma.adminUserSetting.findFirst({
            select: {
                CampaignClientId: true,
                CampaignClientSecret: true,
                CampaignRedirectUrl: true
            }
        });

        if (!adminSettings?.CampaignClientId || !adminSettings?.CampaignClientSecret) {
            return NextResponse.json({ error: 'OAuth credentials not configured' }, { status: 400 });
        }

        const tokenEndpoint = 'https://oauth2.googleapis.com/token';
        const requestBody = new URLSearchParams();
        requestBody.append('code', code);
        requestBody.append('client_id', adminSettings.CampaignClientId);
        requestBody.append('client_secret', adminSettings.CampaignClientSecret);
        requestBody.append('redirect_uri', adminSettings.CampaignRedirectUrl as string);
        requestBody.append('grant_type', 'authorization_code');

        const response = await fetch(tokenEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: requestBody.toString(),
        });

        const responseData = await response.json();

        if (!response.ok) {
            console.error('Google OAuth Error:', {
                status: response.status,
                error: responseData,
            });
            return NextResponse.json(
                { error: 'OAuth token exchange failed', details: responseData },
                { status: response.status }
            );
        }

        if (responseData.refresh_token) {
            const expiresInSeconds = responseData.refresh_token_expires_in;

            let expirationDate: Date | null = null;

            if (typeof expiresInSeconds === 'number' && !isNaN(expiresInSeconds)) {
                expirationDate = new Date(Date.now() + expiresInSeconds * 1000);
                if (isNaN(expirationDate.getTime())) {
                    console.error('Invalid expirationDate calculated:', expirationDate);
                    return NextResponse.json(
                        { error: 'Failed to calculate token expiration date' },
                        { status: 500 }
                    );
                }
            } else {
                console.warn('refresh_token_expires_in not found or invalid in response, setting CampaignRefreshTokenExpiresIn to null');
            }

            await prisma.adminUserSetting.updateMany({
                data: {
                    CampaignRefreshToken: responseData.refresh_token,
                    CampaignAccessToken: responseData.access_token,
                    CampaignRefreshTokenExpiresIn: expirationDate 
                }
            });
        } else {
            console.warn('No refresh token in response - using existing token');
        }

        return NextResponse.json(responseData);

    } catch (error) {
        console.error("OAuth Error:", error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return NextResponse.json(
            { error: "Internal Server Error", details: errorMessage },
            { status: 500 }
        );
    }
}