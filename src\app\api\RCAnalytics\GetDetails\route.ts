// import { prisma } from '@/lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

// interface RevenueCampaignData {
//     articleid?: string;
//     articlename?: string;
//     date?: string;
//     rv_revenue: number;
//     rv_rpc: number;
//     profit: number;
//     roi: number;
//     profit_gst: number;
//     roi_gst: number;
//     ads_conversions: number;
//     ads_click: number;
//     ads_spend: number;
//     ads_cpa: number;
//     ads_cpc: number;
//     channelid?: string;
//     channelname?: string;
//     ads_spend_with_gst: number;
//     spend_clicks?: number;
//     country?: string;
// }

// interface ApiResponseData {
//     [articleId: string]: {
//         geographic_data: Array<{
//             date: string;
//             countryCriterionId: string;
//             metrics: {
//                 clicks: number;
//                 conversions: number;
//                 cost: number;
//                 impressions: number;
//                 averageCpc: number;
//             };
//         }>;
//     };
// }

// interface RevenueApiResponse {
//     data: Array<{
//         DATE: string;
//         ESTIMATED_EARNINGS: string;
//         IMPRESSIONS: string;
//         IMPRESSIONS_RPM: string;
//         CLICKS: string;
//         IMPRESSIONS_CTR: string;
//         COST_PER_CLICK: string;
//         CUSTOM_CHANNEL_ID: string;
//         CUSTOM_SEARCH_STYLE_ID: string;
//     }>;
// }

// interface AggregatedApiData {
//     [articleId: string]: {
//         totalClicks: number;
//         totalConversions: number;
//         totalCost: number;
//         totalImpressions: number;
//         countryData?: { [countryId: string]: any };
//     };
// }

// interface AggregatedRevenueData {
//     [articleId: string]: {
//         estimatedEarnings: number;
//         revenueClicks: number;
//     };
// }

// interface MetaData {
//     recordCount: number;
//     apiDataUsed: boolean;
//     revenueDataUsed: boolean;
// }

// interface UserInfo {
//     Id: string;
//     User_Type: string | null;
//     styleId?: string;
// }

// const safeJson = (obj: any): any => {
//     if (Array.isArray(obj)) {
//         return obj.map(safeJson);
//     } else if (obj !== null && typeof obj === 'object') {
//         return Object.fromEntries(
//             Object.entries(obj).map(([key, value]) => [key, safeJson(value)])
//         );
//     } else if (typeof obj === 'bigint') {
//         return obj.toString();
//     } else {
//         return obj;
//     }
// };

// const VALID_GROUP_BY_COMBINATIONS = [
//     [],
//     ["article"],
//     ["channel"],
//     ["country"],
//     ["date"],
//     ["article", "channel"],
//     ["article", "country"],
//     ["article", "date"],
//     ["channel", "country"],
//     ["channel", "date"],
//     ["country", "date"],
//     ["article", "channel", "country"],
//     ["article", "channel", "date"],
//     ["article", "country", "date"],
//     ["channel", "country", "date"],
//     ["article", "channel", "country", "date"]
// ];

// async function getUSDtoINRRate(): Promise<number> {
//     try {
//         const today = new Date();

//         const rateRecord = await prisma.ads_RateConversion.findFirst({
//             where: {
//                 Date: today
//             },
//             select: {
//                 USDtoINR: true
//             },
//         });

//         if (!rateRecord) {
//             const latestRateRecord = await prisma.ads_RateConversion.findFirst({
//                 select: {
//                     USDtoINR: true
//                 },
//                 orderBy: {
//                     Date: 'desc'
//                 }
//             });

//             return latestRateRecord?.USDtoINR ? Number(latestRateRecord.USDtoINR) : 83.0;
//         }

//         return rateRecord.USDtoINR ? Number(rateRecord.USDtoINR) : 83.0;
//     } catch (error) {
//         return 83.0;
//     }
// }

// function convertRevenueToINR(data: RevenueCampaignData[], usdToInrRate: number): RevenueCampaignData[] {
//     return data.map(row => {
//         const convertedRow = { ...row };

//         if (convertedRow.rv_revenue !== undefined) {
//             convertedRow.rv_revenue = convertedRow.rv_revenue / usdToInrRate;
//         }

//         if (convertedRow.rv_rpc !== undefined) {
//             convertedRow.rv_rpc = convertedRow.rv_rpc / usdToInrRate;
//         }

//         if (convertedRow.profit !== undefined) {
//             convertedRow.profit = convertedRow.profit / usdToInrRate;
//         }

//         if (convertedRow.roi !== undefined) {
//             convertedRow.roi = convertedRow.roi;
//         }

//         if (convertedRow.profit_gst !== undefined) {
//             convertedRow.profit_gst = convertedRow.profit_gst / usdToInrRate;
//         }

//         if (convertedRow.roi_gst !== undefined) {
//             convertedRow.roi_gst = convertedRow.roi_gst;
//         }

//         if (convertedRow.ads_spend !== undefined) {
//             convertedRow.ads_spend = convertedRow.ads_spend / usdToInrRate;
//         }

//         if (convertedRow.ads_spend_with_gst !== undefined) {
//             convertedRow.ads_spend_with_gst = convertedRow.ads_spend_with_gst / usdToInrRate;
//         }

//         if (convertedRow.ads_cpa !== undefined) {
//             convertedRow.ads_cpa = convertedRow.ads_cpa / usdToInrRate;
//         }

//         if (convertedRow.ads_cpc !== undefined) {
//             convertedRow.ads_cpc = convertedRow.ads_cpc / usdToInrRate;
//         }

//         return convertedRow;
//     });
// }

// async function getUserInfo(userId: string): Promise<UserInfo | null> {
//     try {
//         const user = await prisma.adminUser.findFirst({
//             where: { Id: userId },
//             select: {
//                 Id: true,
//                 User_Type: true
//             }
//         });

//         if (!user) {
//             return null;
//         }

//         let styleId: string | undefined;
//         if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
//             const styleMappings = await prisma.styleIdUserMappings.findMany({
//                 where: { UserId: userId },
//                 select: { StyleId: true }
//             });

//             const validMapping = styleMappings.find(mapping => mapping.StyleId !== null);
//             styleId = validMapping?.StyleId ?? undefined;
//         }

//         return {
//             Id: user.Id,
//             User_Type: user.User_Type,
//             styleId
//         };
//     } catch (error) {
//         return null;
//     }
// }

// async function getArticlesWithCampaignMappings(articleIds?: string[]): Promise<string[]> {
//     try {
//         let whereClause: any = {};

//         if (articleIds && articleIds.length > 0) {
//             whereClause.ArticleId = {
//                 in: articleIds
//             };
//         }

//         const mappings = await prisma.articleCampaignMappings.findMany({
//             where: whereClause,
//             select: { ArticleId: true },
//             distinct: ['ArticleId']
//         });

//         return mappings
//             .map(mapping => mapping.ArticleId?.toString())
//             .filter((id): id is string => id !== undefined && id !== null);
//     } catch (error) {
//         return [];
//     }
// }

// async function getBaseUrlFromDatabase(): Promise<string> {
//     try {
//         const settings = await prisma.adminUserSetting.findFirst({
//             select: {
//                 PublicBaseUrl: true
//             }
//         });

//         if (!settings?.PublicBaseUrl) {
//             throw new Error('Base URL not found in admin user settings');
//         }

//         return settings.PublicBaseUrl;
//     } catch (error) {
//         return process.env.NEXT_PUBLIC_BASE_URL || '';
//     }
// }

// async function fetchFromCampaignApi(articleIds: string[], startDate: string, endDate: string): Promise<ApiResponseData> {
//     try {
//         const baseUrl = await getBaseUrlFromDatabase();
//         const apiUrl = `http://localhost:3000/api/RCAnalytics/GetFromApi`;
//         const bodyPayload = {
//             articleIds: articleIds.join(','),
//             startDate,
//             endDate
//         };

//         const response = await fetch(apiUrl, {
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json',
//                 'Authorization': `Bearer ${process.env.API_AUTH_TOKEN}`
//             },
//             body: JSON.stringify(bodyPayload),
//         });

//         if (!response.ok) {
//             throw new Error(`Campaign API request failed with status ${response.status}`);
//         }

//         return await response.json();
//     } catch (error) {
//         throw new Error('Failed to retrieve data from campaign API');
//     }
// }

// async function fetchFromRevenueApi(
//     req: NextRequest,
//     processedGroupBy: string[],
//     articleIds: string[],
//     startDate?: string,
//     endDate?: string
// ): Promise<AggregatedRevenueData> {
//     try {
//         const baseUrl = await getBaseUrlFromDatabase();
//         const revenueApiUrl = `http://localhost:3000/api/RCAnalytics/RevenueFromAPi`;
//         const aggregatedRevenueData: AggregatedRevenueData = {};

//         let breakpoints = {
//             "country": processedGroupBy.includes('country'),
//             "platform": false,
//             "date": true,
//             "customChannel": true,
//             "styleId": true
//         };

//         if (processedGroupBy.includes('article')) {
//             breakpoints.customChannel = true;
//             breakpoints.styleId = true;
//         }

//         const cookies = req.headers.get('cookie') || '';

//         try {
//             const requestBody: any = {
//                 selectedChannels: [],
//                 selectedStyles: [],
//                 articleIds: articleIds
//             };

//             const response = await fetch(`${revenueApiUrl}?breakPoints=${encodeURIComponent(JSON.stringify(breakpoints))}&InrtoUsd=true&startDate=${startDate}&endDate=${endDate}`, {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json',
//                     'Cookie': cookies,
//                     'Authorization': req.headers.get('authorization') || ''
//                 },
//                 body: JSON.stringify(requestBody)
//             });

//             if (!response.ok) {
//                 return aggregatedRevenueData;
//             }

//             const revenueResponse: RevenueApiResponse = await response.json();

//             const processedData = await processRevenueDataForMultipleArticles(revenueResponse, articleIds);

//             Object.assign(aggregatedRevenueData, processedData);

//         } catch (apiError) {
//             console.error('Revenue API error:', apiError);
//         }

//         return aggregatedRevenueData;
//     } catch (error) {
//         console.error('Error in fetchFromRevenueApi:', error);
//         return {};
//     }
// }

// async function processRevenueDataForMultipleArticles(revenueData: RevenueApiResponse, targetArticleIds: string[]): Promise<AggregatedRevenueData> {
//     const aggregated: AggregatedRevenueData = {};

//     for (const item of revenueData.data) {
//         try {
//             let channelId: string | null = null;
//             if (item.CUSTOM_CHANNEL_ID) {
//                 const channel = await prisma.channals.findFirst({
//                     where: { ReportingDimensionId: item.CUSTOM_CHANNEL_ID },
//                     select: { Id: true }
//                 });
//                 channelId = channel?.Id || null;
//             }

//             let styleId: string | null = null;
//             if (item.CUSTOM_SEARCH_STYLE_ID) {
//                 const style = await prisma.styleIds.findFirst({
//                     where: { StyleId: item.CUSTOM_SEARCH_STYLE_ID },
//                     select: { Id: true }
//                 });
//                 styleId = style?.Id || null;
//             }

//             if (channelId && styleId) {
//                 const articles = await prisma.articleDetails.findMany({
//                     where: {
//                         Id: { in: targetArticleIds },
//                         CustomChannal: channelId,
//                         OR: [
//                             { StyleIdLm: styleId },
//                             { StyleIdDm: styleId }
//                         ]
//                     },
//                     select: { Id: true, Title: true }
//                 });

//                 for (const article of articles) {
//                     const articleId = article.Id.toString();
//                     const estimatedEarnings = parseFloat(item.ESTIMATED_EARNINGS) || 0;
//                     const revenueClicks = parseInt(item.CLICKS) || 0;

//                     if (!aggregated[articleId]) {
//                         aggregated[articleId] = {
//                             estimatedEarnings: 0,
//                             revenueClicks: 0
//                         };
//                     }

//                     aggregated[articleId].estimatedEarnings += estimatedEarnings;
//                     aggregated[articleId].revenueClicks += revenueClicks;
//                 }
//             }
//         } catch (error) {
//         }
//     }

//     return aggregated;
// }

// function aggregateApiData(apiData: ApiResponseData, includeCountry: boolean = false): AggregatedApiData {
//     const aggregated: AggregatedApiData = {};

//     for (const [articleId, data] of Object.entries(apiData)) {
//         let totalClicks = 0;
//         let totalConversions = 0;
//         let totalCost = 0;
//         let totalImpressions = 0;
//         let countryData: { [countryId: string]: any } = {};

//         data.geographic_data.forEach(item => {
//             totalClicks += item.metrics.clicks || 0;
//             totalConversions += item.metrics.conversions || 0;
//             totalCost += item.metrics.cost || 0;
//             totalImpressions += item.metrics.impressions || 0;

//             if (includeCountry) {
//                 if (!countryData[item.countryCriterionId]) {
//                     countryData[item.countryCriterionId] = {
//                         clicks: 0,
//                         conversions: 0,
//                         cost: 0,
//                         impressions: 0
//                     };
//                 }
//                 countryData[item.countryCriterionId].clicks += item.metrics.clicks || 0;
//                 countryData[item.countryCriterionId].conversions += item.metrics.conversions || 0;
//                 countryData[item.countryCriterionId].cost += item.metrics.cost || 0;
//                 countryData[item.countryCriterionId].impressions += item.metrics.impressions || 0;
//             }
//         });

//         aggregated[articleId] = {
//             totalClicks,
//             totalConversions,
//             totalCost,
//             totalImpressions,
//             ...(includeCountry && { countryData })
//         };
//     }

//     return aggregated;
// }

// function formatDateToYMD(date: Date): string {
//     return date.toISOString().split('T')[0];
// }

// async function getArticleDetails(articleId: string): Promise<{ articlename: string, channelid: string }> {
//     const article = await prisma.articleDetails.findFirst({
//         where: { Id: articleId },
//         select: { Title: true, CustomChannal: true }
//     });

//     return {
//         articlename: article?.Title || '',
//         channelid: article?.CustomChannal?.toString() || ''
//     };
// }

// async function getChannelName(channelId: string): Promise<string> {
//     if (!channelId) return '';

//     const channel = await prisma.channals.findFirst({
//         where: { Id: channelId },
//         select: { DisplayName: true }
//     });

//     return channel?.DisplayName || '';
// }

// async function getAllArticleIds(effectiveUser: UserInfo, domainId?: string, subDomainId?: string): Promise<string[]> {
//     try {
//         let whereClause: any = {
//             IsDeleted: false
//         };

//         if (effectiveUser.User_Type === 'Partner' || effectiveUser.User_Type === 'Account') {
//             if (!effectiveUser.styleId) {
//                 return [];
//             }

//             whereClause.OR = [
//                 { StyleIdLm: effectiveUser.styleId },
//                 { StyleIdDm: effectiveUser.styleId }
//             ];
//             whereClause.User_Id_Settings = effectiveUser.Id;
//         }

//         if (domainId) whereClause.Domain = domainId.toString();
//         if (subDomainId) whereClause.SubDomain = subDomainId.toString();

//         const articles = await prisma.articleDetails.findMany({
//             where: whereClause,
//             select: { Id: true }
//         });

//         return articles.map(article => article.Id.toString());
//     } catch (error) {
//         return [];
//     }
// }

// function sortResponseData(data: RevenueCampaignData[], orderBy: string, orderDir: string): RevenueCampaignData[] {
//     return data.sort((a, b) => {
//         let aValue = (a as any)[orderBy];
//         let bValue = (b as any)[orderBy];

//         if (typeof aValue === 'string' && typeof bValue === 'string') {
//             const comparison = aValue.localeCompare(bValue);
//             return orderDir === 'desc' ? -comparison : comparison;
//         }

//         const aNum = Number(aValue) || 0;
//         const bNum = Number(bValue) || 0;

//         if (orderDir === 'desc') {
//             return bNum - aNum;
//         } else {
//             return aNum - bNum;
//         }
//     });
// }

// function aggregateCountryData(data: RevenueCampaignData[], processedGroupBy: string[]): RevenueCampaignData[] {
//     if (!processedGroupBy.includes('country')) {
//         return data;
//     }

//     const isCountryOnlyGroupBy = processedGroupBy.length === 1 && processedGroupBy[0] === 'country';

//     if (isCountryOnlyGroupBy) {
//         let totalRvRevenue = 0;
//         let totalAdsSpend = 0;
//         let totalAdsConversions = 0;
//         let totalAdsClick = 0;
//         let totalSpendClicks = 0;

//         data.forEach(row => {
//             totalRvRevenue += Number(row.rv_revenue) || 0;
//             totalAdsSpend += Number(row.ads_spend) || 0;
//             totalAdsConversions += Number(row.ads_conversions) || 0;
//             totalAdsClick += Number(row.ads_click) || 0;
//             totalSpendClicks += Number(row.spend_clicks) || 0;
//         });

//         const ads_spend_with_gst = totalAdsSpend * 1.18;
//         const profit = totalRvRevenue - totalAdsSpend;
//         const profit_gst = totalRvRevenue - ads_spend_with_gst;
//         const roi = totalAdsSpend === 0 ? 0 : (profit / totalAdsSpend) * 100;
//         const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;
//         const rv_rpc = totalAdsClick === 0 ? 0 : totalRvRevenue / totalAdsClick;
//         const ads_cpa = totalAdsConversions === 0 ? 0 : totalAdsSpend / totalAdsConversions;
//         const ads_cpc = totalSpendClicks === 0 ? 0 : totalAdsSpend / totalSpendClicks;

//         return [{
//             rv_revenue: totalRvRevenue,
//             rv_rpc: rv_rpc,
//             profit: profit,
//             roi: roi,
//             profit_gst: profit_gst,
//             roi_gst: roi_gst,
//             ads_conversions: totalAdsConversions,
//             ads_click: totalAdsClick,
//             ads_spend: totalAdsSpend,
//             ads_cpa: ads_cpa,
//             ads_cpc: ads_cpc,
//             ads_spend_with_gst: ads_spend_with_gst,
//             spend_clicks: totalSpendClicks
//         }];
//     }

//     const groupingFields = processedGroupBy.filter(field => field !== 'country');
//     const grouped = new Map<string, RevenueCampaignData[]>();

//     data.forEach(row => {
//         let key = '';
//         if (groupingFields.includes('article')) {
//             key += `article:${row.articleid || 'unknown'}|`;
//         }
//         if (groupingFields.includes('channel')) {
//             key += `channel:${row.channelid || 'unknown'}|`;
//         }
//         if (groupingFields.includes('date')) {
//             key += `date:${row.date || 'unknown'}|`;
//         }

//         if (key === '') {
//             key = 'all';
//         }

//         if (!grouped.has(key)) {
//             grouped.set(key, []);
//         }
//         grouped.get(key)!.push(row);
//     });

//     const aggregatedData: RevenueCampaignData[] = [];

//     grouped.forEach((rows, key) => {
//         let totalRvRevenue = 0;
//         let totalAdsSpend = 0;
//         let totalAdsConversions = 0;
//         let totalAdsClick = 0;
//         let totalSpendClicks = 0;

//         rows.forEach(row => {
//             totalRvRevenue += Number(row.rv_revenue) || 0;
//             totalAdsSpend += Number(row.ads_spend) || 0;
//             totalAdsConversions += Number(row.ads_conversions) || 0;
//             totalAdsClick += Number(row.ads_click) || 0;
//             totalSpendClicks += Number(row.spend_clicks) || 0;
//         });

//         const ads_spend_with_gst = totalAdsSpend * 1.18;
//         const profit = totalRvRevenue - totalAdsSpend;
//         const profit_gst = totalRvRevenue - ads_spend_with_gst;
//         const roi = totalAdsSpend === 0 ? 0 : (profit / totalAdsSpend) * 100;
//         const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;
//         const rv_rpc = totalAdsClick === 0 ? 0 : totalRvRevenue / totalAdsClick;
//         const ads_cpa = totalAdsConversions === 0 ? 0 : totalAdsSpend / totalAdsConversions;
//         const ads_cpc = totalSpendClicks === 0 ? 0 : totalAdsSpend / totalSpendClicks;

//         const aggregatedRow: RevenueCampaignData = {
//             rv_revenue: totalRvRevenue,
//             rv_rpc: rv_rpc,
//             profit: profit,
//             roi: roi,
//             profit_gst: profit_gst,
//             roi_gst: roi_gst,
//             ads_conversions: totalAdsConversions,
//             ads_click: totalAdsClick,
//             ads_spend: totalAdsSpend,
//             ads_cpa: ads_cpa,
//             ads_cpc: ads_cpc,
//             ads_spend_with_gst: ads_spend_with_gst,
//             spend_clicks: totalSpendClicks
//         };

//         const firstRow = rows[0];
//         if (groupingFields.includes('article')) {
//             aggregatedRow.articleid = firstRow.articleid;
//             aggregatedRow.articlename = firstRow.articlename;
//         }
//         if (groupingFields.includes('channel')) {
//             aggregatedRow.channelid = firstRow.channelid;
//             aggregatedRow.channelname = firstRow.channelname;
//         }
//         if (groupingFields.includes('date')) {
//             aggregatedRow.date = firstRow.date;
//         }

//         aggregatedData.push(aggregatedRow);
//     });

//     return aggregatedData;
// }

// function isCurrentDate(dateString: string): boolean {
//     try {
//         if (!dateString) return false;

//         const today = new Date();
//         today.setHours(0, 0, 0, 0);

//         const yesterday = new Date(today);
//         yesterday.setDate(today.getDate() - 1);

//         const inputDate = new Date(dateString);
//         if (isNaN(inputDate.getTime())) return false;
//         inputDate.setHours(0, 0, 0, 0);

//         return inputDate.getTime() >= yesterday.getTime();
//     } catch (error) {
//         return false;
//     }
// }

// function isStartDateBeforeToday(dateString: string): boolean {
//     try {
//         if (!dateString) return false;

//         const today = new Date();
//         today.setHours(0, 0, 0, 0);

//         const yesterday = new Date(today);
//         yesterday.setDate(today.getDate() - 2);

//         const inputDate = new Date(dateString);
//         if (isNaN(inputDate.getTime())) return false;
//         inputDate.setHours(0, 0, 0, 0);

//         return inputDate.getTime() < yesterday.getTime();
//     } catch (error) {
//         return false;
//     }
// }

// export async function POST(req: NextRequest) {
//     try {
//         type AuthenticatedUser = {
//             Id: string;
//             User_Type: string;
//         };

//         const user = (await verifyToken(req)) as AuthenticatedUser;
//         if (!user) {
//             return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         }

//         const url = new URL(req.url);
//         const searchParams = url.searchParams;

//         const startDate = searchParams.get('startDate');
//         const endDate = searchParams.get('endDate');
//         const showNoRevenue = searchParams.get('showNoRevenue') || 'true';
//         const showDateRange = searchParams.get('showDateRange') || 'true';
//         const InrtoUsd = searchParams.get('InrtoUsd') === 'true';
//         const userIdParam = searchParams.get('userId') || '';

//         if (!startDate || !endDate) {
//             return NextResponse.json({
//                 error: 'Both startDate and endDate are required'
//             }, { status: 400 });
//         }

//         const isAdmin = user.User_Type === 'Super Admin' || user.User_Type === 'Admin';

//         let effectiveUser: UserInfo;

//         if (isAdmin && userIdParam) {
//             const targetUser = await getUserInfo(userIdParam);
//             if (!targetUser) {
//                 return NextResponse.json({
//                     error: 'User not found'
//                 }, { status: 404 });
//             }
//             effectiveUser = targetUser;
//         } else {
//             let styleId: string | undefined;
//             if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
//                 const styleMapping = await prisma.styleIdUserMappings.findFirst({
//                     where: { UserId: user.Id as string },
//                     select: { StyleId: true }
//                 });
//                 styleId = styleMapping?.StyleId || undefined;
//             }

//             effectiveUser = {
//                 Id: user.Id as string,
//                 User_Type: user.User_Type,
//                 styleId
//             };
//             // console.log(effectiveUser, "effectiveUser");

//         }

//         const body = await req.json();
//         let {
//             articleId,
//             channelId,
//             domainId,
//             subDomainId,
//             groupBy,
//             orderBy = 'ads_spend',
//             orderDir = 'desc'
//         } = body;

//         if (!articleId) {
//             articleId = await getAllArticleIds(effectiveUser, domainId, subDomainId);

//             if (articleId.length === 0) {
//                 return NextResponse.json({
//                     success: true,
//                     data: [],
//                     meta: { recordCount: 0, apiDataUsed: false, revenueDataUsed: false }
//                 });
//             }
//         }

//         // User permission filtering for Partner/Account users
//         if (effectiveUser.User_Type === 'Partner' || effectiveUser.User_Type === 'Account') {
//             if (!effectiveUser.styleId) {
//                 return NextResponse.json({
//                     error: 'Data not found for this user'
//                 }, { status: 403 });
//             }

//             const articleWhereClause: any = {
//                 OR: [
//                     { StyleIdLm: effectiveUser.styleId },
//                     { StyleIdDm: effectiveUser.styleId }
//                 ],
//                 IsDeleted: false,
//                 User_Id_Settings: effectiveUser.Id
//             };

//             if (domainId) articleWhereClause.Domain = domainId.toString();
//             if (subDomainId) articleWhereClause.SubDomain = subDomainId.toString();

//             const articleDetails = await prisma.articleDetails.findMany({
//                 where: articleWhereClause,
//                 select: { Id: true }
//             });

//             if (articleDetails.length === 0) {
//                 return NextResponse.json({
//                     success: true,
//                     data: [],
//                     meta: { recordCount: 0, apiDataUsed: false, revenueDataUsed: false }
//                 });
//             }

//             const allowedArticleIds = articleDetails.map(article => article.Id.toString());

//             if (articleId) {
//                 const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
//                 const filteredIds = requestedIds.filter(id => allowedArticleIds.includes(id.toString()));
//                 articleId = filteredIds.length > 0 ? filteredIds : allowedArticleIds;
//             } else {
//                 articleId = allowedArticleIds;
//             }
//         } else if (domainId || subDomainId) {
//             // Domain/subdomain filtering for admin users
//             const articleWhereClause: any = {};
//             if (domainId) articleWhereClause.Domain = domainId.toString();
//             if (subDomainId) articleWhereClause.SubDomain = subDomainId.toString();

//             const articleDetails = await prisma.articleDetails.findMany({
//                 where: articleWhereClause,
//                 select: { Id: true }
//             });

//             if (articleDetails.length === 0) {
//                 return NextResponse.json({
//                     success: true,
//                     data: [],
//                     meta: { recordCount: 0, apiDataUsed: false, revenueDataUsed: false }
//                 });
//             }

//             const domainFilteredArticleIds = articleDetails.map(article => article.Id.toString());

//             if (articleId) {
//                 const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
//                 const filteredIds = requestedIds.filter(id => domainFilteredArticleIds.includes(id.toString()));
//                 articleId = filteredIds.length > 0 ? filteredIds : domainFilteredArticleIds;
//             } else {
//                 articleId = domainFilteredArticleIds;
//             }
//         }

//         // Channel filtering
//         if (channelId) {
//             const channelArticles = await prisma.articleDetails.findMany({
//                 where: { CustomChannal: channelId.toString() },
//                 select: { Id: true }
//             });
//             // console.log(channelArticles, "channelArticles");

//             const channelArticleIds = channelArticles.map(article => article.Id.toString());

//             if (articleId) {
//                 const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
//                 articleId = requestedIds.filter(id => channelArticleIds.includes(id));
//             } else {
//                 articleId = channelArticleIds;
//             }
//         }

//         const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
//         const articlesWithCampaignMappings = await getArticlesWithCampaignMappings(requestedIds);

//         if (requestedIds.length === 1 && articlesWithCampaignMappings.length === 0) {
//             return NextResponse.json({
//                 error: 'Data not found - No campaign mapping exists for this article'
//             }, { status: 404 });
//         }

//         // Process groupBy parameter
//         let processedGroupBy: string[] = [];
//         if (groupBy) {
//             processedGroupBy = Array.isArray(groupBy)
//                 ? groupBy.map(field => field.toString().toLowerCase().trim())
//                 : groupBy.toLowerCase().split(',').map((field: string) => field.trim());
//         }

//         const isValidCombination = VALID_GROUP_BY_COMBINATIONS.some(validCombo =>
//             validCombo.length === processedGroupBy.length &&
//             validCombo.every(field => processedGroupBy.includes(field))
//         );

//         if (!isValidCombination) {
//             return NextResponse.json({
//                 error: 'Invalid groupBy combination. Please use one of the valid combinations.',
//                 validCombinations: VALID_GROUP_BY_COMBINATIONS
//             }, { status: 400 });
//         }

//         const isOnlyCountryGroupBy = processedGroupBy.length === 1 && processedGroupBy.includes('country');
//         const includesCountry = processedGroupBy.includes('country');

//         // Determine data source strategy
//         const isEndDateToday = isCurrentDate(endDate);
//         const isStartDateToday = isStartDateBeforeToday(startDate);
//         const useHybridApproach = isStartDateToday && isEndDateToday;
//         const useOnlyExternalApi = !isStartDateToday && isEndDateToday;

//         let externalApiData: AggregatedApiData = {};
//         let revenueApiData: AggregatedRevenueData = {};
//         let databaseData: RevenueCampaignData[] = [];
//         let apiDataUsed = false;
//         let revenueDataUsed = false;
//         let dbDataUsed = false;

//         // Fetch API data for current date if needed
//         if ((useHybridApproach || useOnlyExternalApi) && (articleId || channelId)) {
//             try {
//                 let articleIdsToFetch: string[] = [];

//                 if (channelId) {
//                     const channelArticles = await prisma.articleDetails.findMany({
//                         where: { CustomChannal: channelId.toString() },
//                         select: { Id: true }
//                     });
//                     articleIdsToFetch = channelArticles.map(a => a.Id.toString());

//                     if (articleId) {
//                         const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
//                         articleIdsToFetch = articleIdsToFetch.filter(id => requestedIds.includes(id));
//                     }
//                 } else if (articleId) {
//                     articleIdsToFetch = Array.isArray(articleId) ? articleId : [articleId];
//                 }

//                 if (articleIdsToFetch.length > 0) {
//                     const today = formatDateToYMD(new Date());

//                     const campaignArticles = articlesWithCampaignMappings.filter(id =>
//                         articleIdsToFetch.includes(id)
//                     );

//                     if (campaignArticles.length > 0) {
//                         try {
//                             const rawApiData = await fetchFromCampaignApi(campaignArticles, startDate, endDate);
//                             externalApiData = aggregateApiData(rawApiData, includesCountry);
//                             apiDataUsed = Object.keys(externalApiData).length > 0;
//                         } catch (campaignError) {
//                             console.error('Campaign API error:', campaignError);
//                         }

//                         try {
//                             revenueApiData = await fetchFromRevenueApi(req, processedGroupBy, campaignArticles, startDate, endDate);
//                             revenueDataUsed = Object.keys(revenueApiData).length > 0;
//                         } catch (revenueError) {
//                             console.error('Revenue API error:', revenueError);
//                         }
//                     }
//                 }
//             } catch (apiError) {
//                 console.error('API fetch error:', apiError);
//                 externalApiData = {};
//                 revenueApiData = {};
//             }
//         }

//         if (useHybridApproach) {
//             const yesterday = new Date();
//             yesterday.setDate(yesterday.getDate() - 1);
//             const dbEndDate = formatDateToYMD(yesterday);

//             try {
//                 const processedArticleId = articleId ? (Array.isArray(articleId) ? articleId.join(',') : articleId.toString()) : null;
//                 const processedChannelId = channelId ? (Array.isArray(channelId) ? channelId.join(',') : channelId.toString()) : null;
//                 const groupByString = processedGroupBy.join(',');

//                 await prisma.$executeRawUnsafe(
//                     `SELECT * FROM fn_ads_revenue_data_test(
//                         $1::text, $2::text, $3::date, $4::date, $5::text, $6::boolean, $7::boolean, $8::text
//                     )`,
//                     processedArticleId,
//                     processedChannelId,
//                     new Date(startDate),
//                     new Date(dbEndDate),
//                     groupByString,
//                     showNoRevenue === 'true',
//                     InrtoUsd,
//                     effectiveUser.styleId || null
//                 );


//                 const selectFieldsArray: string[] = [];
//                 const groupByFieldsArray: string[] = [];

//                 if (processedGroupBy.includes('date')) {
//                     if (showDateRange === 'true') {
//                         const dateRangeLabel = `${startDate} to ${dbEndDate}`;
//                         selectFieldsArray.push(`'${dateRangeLabel}' as "date"`);
//                     } else {
//                         selectFieldsArray.push(`"date"::text as "date"`);
//                         groupByFieldsArray.push('"date"');
//                     }
//                 }

//                 if (processedGroupBy.includes('article')) {
//                     selectFieldsArray.push(`"articleid"::text as "articleid"`);
//                     selectFieldsArray.push(`"article_name"::text as "articlename"`);
//                     groupByFieldsArray.push('"articleid"', '"article_name"');
//                 }

//                 if (processedGroupBy.includes('channel')) {
//                     selectFieldsArray.push(`"channelid"::text as "channelid"`);
//                     selectFieldsArray.push(`"channel_name"::text as "channelname"`);
//                     groupByFieldsArray.push('"channelid"', '"channel_name"');
//                 }

//                 if (processedGroupBy.includes('country')) {
//                     selectFieldsArray.push(`"country"::text as "country"`);
//                     groupByFieldsArray.push('"country"');
//                 }

//                 selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) as "rv_revenue"`);
//                 selectFieldsArray.push(`CASE
//                     WHEN COALESCE(SUM("ads_click"::integer), 0) = 0 THEN 0
//                     ELSE COALESCE(SUM("rv_revenue"::float), 0) / COALESCE(SUM("ads_click"::integer), 1)
//                 END as "rv_rpc"`);
//                 selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) - COALESCE(SUM("ads_spend"::float), 0) as "profit"`);
//                 selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) - (COALESCE(SUM("ads_spend"::float), 0) * 1.18) as "profit_gst"`);
//                 selectFieldsArray.push(`COALESCE(SUM("ads_spend"::float), 0) as "ads_spend"`);
//                 selectFieldsArray.push(`(COALESCE(SUM("ads_spend"::float), 0) * 1.18) as "ads_spend_with_gst"`);
//                 selectFieldsArray.push(`CASE 
//                     WHEN COALESCE(SUM("ads_spend"::float), 0) = 0 THEN 0 
//                     ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - COALESCE(SUM("ads_spend"::float), 0)) 
//                         / COALESCE(SUM("ads_spend"::float), 0)) * 100 
//                 END as "roi"`);
//                 selectFieldsArray.push(`CASE 
//                     WHEN (COALESCE(SUM("ads_spend"::float), 0) * 1.18) = 0 THEN 0 
//                     ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) 
//                         / (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) * 100 
//                 END as "roi_gst"`);
//                 selectFieldsArray.push(`COALESCE(SUM("ads_conversions"::integer), 0) as "ads_conversions"`);
//                 selectFieldsArray.push(`COALESCE(SUM("ads_click"::integer), 0) as "ads_click"`);
//                 selectFieldsArray.push(`COALESCE(SUM("spend_clicks"::integer), 0) as "spend_clicks"`);
//                 selectFieldsArray.push(`CASE
//                     WHEN COALESCE(SUM("ads_conversions"::integer), 0) = 0 THEN 0
//                     ELSE COALESCE(SUM("ads_spend"::float), 0) / COALESCE(SUM("ads_conversions"::integer), 1)
//                 END as "ads_cpa"`);
//                 selectFieldsArray.push(`CASE
//                     WHEN COALESCE(SUM("spend_clicks"::integer), 0) = 0 THEN 0
//                     ELSE COALESCE(SUM("ads_spend"::float), 0) / COALESCE(SUM("spend_clicks"::integer), 1)
//                 END as "ads_cpc"`);

//                 const selectFields = selectFieldsArray.join(',\n            ');
//                 const groupByClause = groupByFieldsArray.length > 0 ? `GROUP BY ${groupByFieldsArray.join(', ')}` : '';

//                 const dbQuery = `
//                     SELECT ${selectFields}
//                     FROM pg_temp.temp_ads_result
//                     ${groupByClause}
//                 `;

//                 databaseData = await prisma.$queryRawUnsafe<RevenueCampaignData[]>(dbQuery);
//                 dbDataUsed = databaseData.length > 0;

//             } catch (dbError) {
//                 console.error('Database query error:', dbError);
//                 databaseData = [];
//             }
//         }

//         if (useHybridApproach) {
//             const mergedDataMap = new Map<string, RevenueCampaignData>();

//             databaseData.forEach(dbRow => {
//                 const key = getGroupingKey(dbRow, processedGroupBy);
//                 mergedDataMap.set(key, { ...dbRow });
//             });

//             if (Object.keys(externalApiData).length > 0 || Object.keys(revenueApiData).length > 0) {
//                 const allArticleIds = new Set([
//                     ...Object.keys(externalApiData),
//                     ...Object.keys(revenueApiData)
//                 ]);

//                 for (const articleId of allArticleIds) {
//                     const apiData = externalApiData[articleId];
//                     const revenueData = revenueApiData[articleId];

//                     // Skip if article doesn't match channel filter
//                     if (channelId) {
//                         const articleDetails = await getArticleDetails(articleId);
//                         if (articleDetails.channelid !== channelId) {
//                             continue;
//                         }
//                     }

//                     const articleDetails = await getArticleDetails(articleId);
//                     const channelName = await getChannelName(articleDetails.channelid);

//                     const ads_spend = apiData ? apiData.totalCost : 0;
//                     const ads_spend_with_gst = ads_spend * 1.18;
//                     const ads_conversions = apiData ? apiData.totalConversions : 0;
//                     const spend_clicks = apiData ? apiData.totalClicks : 0;
//                     const estimated_earnings = revenueData ? revenueData.estimatedEarnings : 0;
//                     const revenue_clicks = revenueData ? revenueData.revenueClicks : 0;

//                     const profit = estimated_earnings - ads_spend;
//                     const profit_gst = estimated_earnings - ads_spend_with_gst;
//                     const roi = ads_spend === 0 ? 0 : (profit / ads_spend) * 100;
//                     const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;

//                     const apiRow: RevenueCampaignData = {
//                         // date: formatDateToYMD(new Date()),
//                         rv_revenue: estimated_earnings,
//                         rv_rpc: revenue_clicks === 0 ? 0 : estimated_earnings / revenue_clicks,
//                         profit: profit,
//                         roi: roi,
//                         profit_gst: profit_gst,
//                         roi_gst: roi_gst,
//                         ads_conversions,
//                         ads_click: revenue_clicks,
//                         ads_spend,
//                         ads_cpa: ads_conversions === 0 ? 0 : ads_spend / ads_conversions,
//                         ads_cpc: spend_clicks === 0 ? 0 : ads_spend / spend_clicks,
//                         ads_spend_with_gst,
//                         spend_clicks,
//                         articleid: articleId,
//                         articlename: articleDetails.articlename,
//                         channelid: articleDetails.channelid,
//                         channelname: channelName
//                     };

//                     const key = getGroupingKey(apiRow, processedGroupBy);

//                     if (mergedDataMap.has(key)) {
//                         const existingRow = mergedDataMap.get(key)!;
//                         const mergedRow = mergeRevenueData(existingRow, apiRow);
//                         mergedDataMap.set(key, mergedRow);
//                     } else {
//                         mergedDataMap.set(key, apiRow);
//                     }
//                 }
//             }

//             let combinedData = Array.from(mergedDataMap.values());

//             if (includesCountry) {
//                 combinedData = aggregateCountryData(combinedData, processedGroupBy);
//             }

//             let responseData = combinedData.map(row => {
//                 const { spend_clicks, ...rest } = row;

//                 if (!processedGroupBy.includes('article')) {
//                     delete rest.articleid;
//                     delete rest.articlename;
//                 }
//                 if (!processedGroupBy.includes('channel')) {
//                     delete rest.channelid;
//                     delete rest.channelname;
//                 }
//                 if (!processedGroupBy.includes('date')) {
//                     delete rest.date;
//                 }
//                 if (!processedGroupBy.includes('country')) {
//                     delete rest.country;
//                 }

//                 return rest;
//             });

//             // Apply filters and sorting
//             if (showNoRevenue === 'true') {
//                 responseData = responseData.filter(row => {
//                     const rvRevenue = Number(row.rv_revenue) || 0;
//                     const adsSpend = Number(row.ads_spend) || 0;
//                     return !(rvRevenue === 0 && adsSpend === 0);
//                 });
//             }

//             responseData = sortResponseData(responseData, orderBy, orderDir);

//             if (InrtoUsd) {
//                 const usdToInrRate = await getUSDtoINRRate();
//                 responseData = convertRevenueToINR(responseData, usdToInrRate);
//             }

//             const meta: MetaData = {
//                 recordCount: responseData.length,
//                 apiDataUsed: apiDataUsed,
//                 revenueDataUsed: revenueDataUsed
//             };

//             return NextResponse.json({
//                 success: true,
//                 data: safeJson(responseData),
//                 meta: {
//                     ...meta,
//                     dataSource: 'hybrid',
//                     databaseRecords: databaseData.length,
//                     apiRecords: Object.keys(externalApiData).length + Object.keys(revenueApiData).length
//                 }
//             });
//         }

//         function getGroupingKey(row: RevenueCampaignData, groupBy: string[]): string {
//             const parts: string[] = [];

//             if (groupBy.includes('article')) {
//                 parts.push(`article:${row.articleid || 'unknown'}`);
//             }
//             if (groupBy.includes('channel')) {
//                 parts.push(`channel:${row.channelid || 'unknown'}`);
//             }
//             if (groupBy.includes('date')) {
//                 parts.push(`date:${row.date || 'unknown'}`);
//             }
//             if (groupBy.includes('country')) {
//                 parts.push(`country:${row.country || 'unknown'}`);
//             }

//             return parts.join('|') || 'all';
//         }

//         function mergeRevenueData(row1: RevenueCampaignData, row2: RevenueCampaignData): RevenueCampaignData {
//             // Sum all the numeric values
//             const rv_revenue = (Number(row1.rv_revenue) || 0) + (Number(row2.rv_revenue) || 0);
//             const ads_spend = (Number(row1.ads_spend) || 0) + (Number(row2.ads_spend) || 0);
//             const ads_conversions = (Number(row1.ads_conversions) || 0) + (Number(row2.ads_conversions) || 0);
//             const ads_click = (Number(row1.ads_click) || 0) + (Number(row2.ads_click) || 0);
//             const spend_clicks = (Number(row1.spend_clicks) || 0) + (Number(row2.spend_clicks) || 0);

//             // Calculate derived fields
//             const ads_spend_with_gst = ads_spend * 1.18;
//             const profit = rv_revenue - ads_spend;
//             const profit_gst = rv_revenue - ads_spend_with_gst;

//             const rv_rpc = ads_click === 0 ? 0 : rv_revenue / ads_click;
//             const roi = ads_spend === 0 ? 0 : (profit / ads_spend) * 100;
//             const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;
//             const ads_cpa = ads_conversions === 0 ? 0 : ads_spend / ads_conversions;
//             const ads_cpc = spend_clicks === 0 ? 0 : ads_spend / spend_clicks;

//             return {
//                 rv_revenue,
//                 ads_spend,
//                 ads_conversions,
//                 ads_click,
//                 spend_clicks,
//                 articleid: row1.articleid || row2.articleid,
//                 articlename: row1.articlename || row2.articlename,
//                 channelid: row1.channelid || row2.channelid,
//                 channelname: row1.channelname || row2.channelname,
//                 date: row1.date || row2.date,
//                 country: row1.country || row2.country,
//                 rv_rpc,
//                 profit,
//                 roi,
//                 profit_gst,
//                 roi_gst,
//                 ads_cpa,
//                 ads_cpc,
//                 ads_spend_with_gst
//             };
//         }

//         if (useOnlyExternalApi) {
//             // Declare responseData at the top to avoid scoping issues
//             let responseData: RevenueCampaignData[] = [];

//             if (isOnlyCountryGroupBy) {
//                 let totalCost = 0;
//                 let totalConversions = 0;
//                 let totalClicks = 0;
//                 let totalEstimatedEarnings = 0;
//                 let totalRevenueClicks = 0;

//                 if (apiDataUsed && Object.keys(externalApiData).length > 0) {
//                     for (const [articleId, apiData] of Object.entries(externalApiData)) {
//                         totalCost += apiData.totalCost || 0;
//                         totalConversions += apiData.totalConversions || 0;
//                         totalClicks += apiData.totalClicks || 0;
//                     }
//                 }

//                 if (revenueDataUsed && Object.keys(revenueApiData).length > 0) {
//                     for (const [articleId, revenueData] of Object.entries(revenueApiData)) {
//                         totalEstimatedEarnings += revenueData.estimatedEarnings || 0;
//                         totalRevenueClicks += revenueData.revenueClicks || 0;
//                     }
//                 }

//                 const ads_spend_with_gst = totalCost * 1.18;
//                 const profit = totalEstimatedEarnings - totalCost;
//                 const profit_gst = totalEstimatedEarnings - ads_spend_with_gst;
//                 const roi = totalCost === 0 ? 0 : (profit / totalCost) * 100;
//                 const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;
//                 const rv_rpc = totalRevenueClicks === 0 ? 0 : totalEstimatedEarnings / totalRevenueClicks;
//                 const ads_cpa = totalConversions === 0 ? 0 : totalCost / totalConversions;
//                 const ads_cpc = totalClicks === 0 ? 0 : totalCost / totalClicks;

//                 responseData = [{
//                     rv_revenue: totalEstimatedEarnings,
//                     rv_rpc: rv_rpc,
//                     profit: profit,
//                     roi: roi,
//                     profit_gst: profit_gst,
//                     roi_gst: roi_gst,
//                     ads_conversions: totalConversions,
//                     ads_click: totalRevenueClicks,
//                     ads_spend: totalCost,
//                     ads_cpa: ads_cpa,
//                     ads_cpc: ads_cpc,
//                     ads_spend_with_gst: ads_spend_with_gst,
//                 }];
//             }
//             // Handle case when groupBy is null/empty (no grouping)
//             else if (processedGroupBy.length === 0) {
//                 let totalCost = 0;
//                 let totalConversions = 0;
//                 let totalClicks = 0;
//                 let totalEstimatedEarnings = 0;
//                 let totalRevenueClicks = 0;

//                 // Sum all API data
//                 if (apiDataUsed && Object.keys(externalApiData).length > 0) {
//                     for (const apiData of Object.values(externalApiData)) {
//                         totalCost += apiData.totalCost || 0;
//                         totalConversions += apiData.totalConversions || 0;
//                         totalClicks += apiData.totalClicks || 0;
//                     }
//                 }

//                 // Sum all revenue data
//                 if (revenueDataUsed && Object.keys(revenueApiData).length > 0) {
//                     for (const revenueData of Object.values(revenueApiData)) {
//                         totalEstimatedEarnings += revenueData.estimatedEarnings || 0;
//                         totalRevenueClicks += revenueData.revenueClicks || 0;
//                     }
//                 }

//                 // Calculate derived metrics
//                 const ads_spend_with_gst = totalCost * 1.18;
//                 const profit = totalEstimatedEarnings - totalCost;
//                 const profit_gst = totalEstimatedEarnings - ads_spend_with_gst;
//                 const roi = totalCost === 0 ? 0 : (profit / totalCost) * 100;
//                 const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;
//                 const rv_rpc = totalRevenueClicks === 0 ? 0 : totalEstimatedEarnings / totalRevenueClicks;
//                 const ads_cpa = totalConversions === 0 ? 0 : totalCost / totalConversions;
//                 const ads_cpc = totalClicks === 0 ? 0 : totalCost / totalClicks;

//                 // Create single aggregated record
//                 responseData = [{
//                     rv_revenue: totalEstimatedEarnings,
//                     rv_rpc: rv_rpc,
//                     profit: profit,
//                     roi: roi,
//                     profit_gst: profit_gst,
//                     roi_gst: roi_gst,
//                     ads_conversions: totalConversions,
//                     ads_click: totalRevenueClicks,
//                     ads_spend: totalCost,
//                     ads_cpa: ads_cpa,
//                     ads_cpc: ads_cpc,
//                     ads_spend_with_gst: ads_spend_with_gst,
//                 }];
//             }
//             // Handle other grouping cases (article, channel, date, etc.)
//             else {
//                 // Remove the local declaration here since we declared it at the top
//                 // let responseData: RevenueCampaignData[] = [];

//                 if (Object.keys(externalApiData).length > 0 || Object.keys(revenueApiData).length > 0) {
//                     if (processedGroupBy.includes('channel') && channelId) {
//                         if (processedGroupBy.includes('article')) {
//                             const allArticleIds = new Set([
//                                 ...Object.keys(externalApiData),
//                                 ...Object.keys(revenueApiData)
//                             ]);

//                             const apiDataResults = await Promise.all(Array.from(allArticleIds).map(async (articleId) => {
//                                 const apiData = externalApiData[articleId];
//                                 const revenueData = revenueApiData[articleId];
//                                 const articleDetails = await getArticleDetails(articleId);

//                                 if (articleDetails.channelid !== channelId) {
//                                     return null;
//                                 }

//                                 const channelName = await getChannelName(channelId);
//                                 const ads_spend = apiData ? apiData.totalCost : 0;
//                                 const ads_spend_with_gst = ads_spend * 1.18;
//                                 const ads_conversions = apiData ? apiData.totalConversions : 0;
//                                 const spend_clicks = apiData ? apiData.totalClicks : 0;
//                                 const estimated_earnings = revenueData ? revenueData.estimatedEarnings : 0;
//                                 const revenue_clicks = revenueData ? revenueData.revenueClicks : 0;

//                                 const profit = estimated_earnings - ads_spend;
//                                 const profit_gst = estimated_earnings - ads_spend_with_gst;
//                                 const roi = ads_spend === 0 ? 0 : (profit / ads_spend) * 100;
//                                 const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;

//                                 const result: RevenueCampaignData = {
//                                     rv_revenue: estimated_earnings,
//                                     rv_rpc: revenue_clicks === 0 ? 0 : estimated_earnings / revenue_clicks,
//                                     profit: profit,
//                                     roi: roi,
//                                     profit_gst: profit_gst,
//                                     roi_gst: roi_gst,
//                                     ads_conversions,
//                                     ads_click: revenue_clicks,
//                                     ads_spend,
//                                     ads_cpa: ads_conversions === 0 ? 0 : ads_spend / ads_conversions,
//                                     ads_cpc: spend_clicks === 0 ? 0 : ads_spend / spend_clicks,
//                                     ads_spend_with_gst,
//                                     articleid: articleId,
//                                     articlename: articleDetails.articlename,
//                                     channelid: channelId,
//                                     channelname: channelName,
//                                     spend_clicks
//                                 };

//                                 return result;
//                             }));

//                             responseData = apiDataResults.filter((item): item is RevenueCampaignData => item !== null);
//                             if (includesCountry) {
//                                 responseData = aggregateCountryData(responseData, processedGroupBy);
//                             }
//                         } else {
//                             let totalCost = 0;
//                             let totalConversions = 0;
//                             let totalClicks = 0;
//                             let totalEstimatedEarnings = 0;
//                             let totalRevenueClicks = 0;

//                             for (const [articleId, apiData] of Object.entries(externalApiData)) {
//                                 const articleDetails = await getArticleDetails(articleId);
//                                 if (articleDetails.channelid === channelId) {
//                                     totalCost += apiData.totalCost;
//                                     totalConversions += apiData.totalConversions;
//                                     totalClicks += apiData.totalClicks;
//                                 }
//                             }

//                             for (const [articleId, revenueData] of Object.entries(revenueApiData)) {
//                                 const articleDetails = await getArticleDetails(articleId);
//                                 if (articleDetails.channelid === channelId) {
//                                     totalEstimatedEarnings += revenueData.estimatedEarnings;
//                                     totalRevenueClicks += revenueData.revenueClicks;
//                                 }
//                             }

//                             const channelName = await getChannelName(channelId);
//                             const ads_spend = totalCost;
//                             const ads_spend_with_gst = ads_spend * 1.18;
//                             const profit = totalEstimatedEarnings - ads_spend;
//                             const profit_gst = totalEstimatedEarnings - ads_spend_with_gst;
//                             const roi = ads_spend === 0 ? 0 : (profit / ads_spend) * 100;
//                             const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;

//                             responseData = [{
//                                 rv_revenue: totalEstimatedEarnings,
//                                 rv_rpc: totalRevenueClicks === 0 ? 0 : totalEstimatedEarnings / totalRevenueClicks,
//                                 profit: profit,
//                                 roi: roi,
//                                 profit_gst: profit_gst,
//                                 roi_gst: roi_gst,
//                                 ads_conversions: totalConversions,
//                                 ads_click: totalRevenueClicks,
//                                 ads_spend,
//                                 ads_cpa: totalConversions === 0 ? 0 : ads_spend / totalConversions,
//                                 ads_cpc: totalClicks === 0 ? 0 : ads_spend / totalClicks,
//                                 ads_spend_with_gst,
//                                 channelid: channelId,
//                                 channelname: channelName,
//                             }];
//                         }
//                     } else {
//                         const allArticleIds = new Set([
//                             ...Object.keys(externalApiData),
//                             ...Object.keys(revenueApiData)
//                         ]);

//                         const apiDataResults = await Promise.all(Array.from(allArticleIds).map(async (articleId) => {
//                             const apiData = externalApiData[articleId];
//                             const revenueData = revenueApiData[articleId];

//                             const articleDetails = await getArticleDetails(articleId);
//                             const channelName = await getChannelName(articleDetails.channelid);

//                             const ads_spend = apiData ? apiData.totalCost : 0;
//                             const ads_spend_with_gst = ads_spend * 1.18;
//                             const ads_conversions = apiData ? apiData.totalConversions : 0;
//                             const spend_clicks = apiData ? apiData.totalClicks : 0;
//                             const estimated_earnings = revenueData ? revenueData.estimatedEarnings : 0;
//                             const revenue_clicks = revenueData ? revenueData.revenueClicks : 0;

//                             const profit = estimated_earnings - ads_spend;
//                             const profit_gst = estimated_earnings - ads_spend_with_gst;
//                             const roi = ads_spend === 0 ? 0 : (profit / ads_spend) * 100;
//                             const roi_gst = ads_spend_with_gst === 0 ? 0 : (profit_gst / ads_spend_with_gst) * 100;

//                             const result: RevenueCampaignData = {
//                                 rv_revenue: estimated_earnings,
//                                 rv_rpc: revenue_clicks === 0 ? 0 : estimated_earnings / revenue_clicks,
//                                 profit: profit,
//                                 roi: roi,
//                                 profit_gst: profit_gst,
//                                 roi_gst: roi_gst,
//                                 ads_conversions,
//                                 ads_click: revenue_clicks,
//                                 ads_spend,
//                                 ads_cpa: ads_conversions === 0 ? 0 : ads_spend / ads_conversions,
//                                 ads_cpc: spend_clicks === 0 ? 0 : ads_spend / spend_clicks,
//                                 ads_spend_with_gst,
//                                 spend_clicks
//                             };

//                             if (processedGroupBy.includes('article')) {
//                                 result.articleid = articleId;
//                                 result.articlename = articleDetails.articlename;
//                             }

//                             if (processedGroupBy.includes('channel')) {
//                                 result.channelid = articleDetails.channelid;
//                                 result.channelname = channelName;
//                             }

//                             return result;
//                         }));

//                         responseData = apiDataResults;

//                         if (includesCountry) {
//                             responseData = aggregateCountryData(responseData, processedGroupBy);
//                         }
//                     }
//                 }

//                 responseData = responseData.map(row => {
//                     const { spend_clicks, ...rest } = row;

//                     if (processedGroupBy.includes('article') && processedGroupBy.includes('channel')) {
//                     } else if (processedGroupBy.includes('article') && !processedGroupBy.includes('channel')) {
//                         delete rest.channelid;
//                         delete rest.channelname;
//                     } else if (processedGroupBy.includes('channel') && !processedGroupBy.includes('article')) {
//                         delete rest.articleid;
//                         delete rest.articlename;
//                     }

//                     if (processedGroupBy.includes('country')) {
//                         delete rest.country;
//                     }

//                     return rest;
//                 });
//             }

//             // Apply filters and sorting
//             if (showNoRevenue === 'true') {
//                 responseData = responseData.filter(row => {
//                     const rvRevenue = Number(row.rv_revenue) || 0;
//                     const adsSpend = Number(row.ads_spend) || 0;
//                     return !(rvRevenue === 0 && adsSpend === 0);
//                 });
//             }

//             responseData = sortResponseData(responseData, orderBy, orderDir);

//             if (InrtoUsd) {
//                 const usdToInrRate = await getUSDtoINRRate();
//                 responseData = convertRevenueToINR(responseData, usdToInrRate);
//             }

//             const meta: MetaData = {
//                 recordCount: responseData.length,
//                 apiDataUsed,
//                 revenueDataUsed
//             };

//             return NextResponse.json({
//                 success: true,
//                 data: safeJson(responseData),
//                 meta
//             });
//         }

//         const processedArticleId = articleId ? (Array.isArray(articleId) ? articleId.join(',') : articleId.toString()) : null;
//         const processedChannelId = channelId ? (Array.isArray(channelId) ? channelId.join(',') : channelId.toString()) : null;
//         const groupByString = processedGroupBy.join(',');

//         await prisma.$executeRawUnsafe(
//             `SELECT * FROM fn_ads_revenue_data_test(
//                 $1::text, $2::text, $3::date, $4::date, $5::text, $6::boolean, $7::boolean, $8::text
//             )`,
//             processedArticleId,
//             processedChannelId,
//             new Date(startDate),
//             new Date(endDate),
//             groupByString,
//             showNoRevenue === 'true',
//             InrtoUsd,
//             effectiveUser.styleId || null
//         );
//         // console.log(processedArticleId,
//         //     processedChannelId, effectiveUser.styleId);

//         if (isOnlyCountryGroupBy) {
//             const query = `
//                 SELECT COALESCE(SUM("rv_revenue"::float), 0) as "rv_revenue",
//                        COALESCE(SUM("ads_spend"::float), 0) as "ads_spend",
//                        COALESCE(SUM("ads_conversions"::integer), 0) as "ads_conversions",
//                        COALESCE(SUM("ads_click"::integer), 0) as "ads_click",
//                        COALESCE(SUM("spend_clicks"::integer), 0) as "spend_clicks"
//                 FROM pg_temp.temp_ads_result
//             `;

//             const aggregatedData = await prisma.$queryRawUnsafe<{
//                 rv_revenue: number;
//                 ads_spend: number;
//                 ads_conversions: number;
//                 ads_click: number;
//                 spend_clicks: number;
//             }[]>(query);

//             let responseData: RevenueCampaignData[];
//             if (aggregatedData.length > 0) {
//                 const row = aggregatedData[0];

//                 const ads_spend = Number(row.ads_spend) || 0;
//                 const ads_spend_with_gst = ads_spend * 1.18;
//                 const totalRevenue = Number(row.rv_revenue) || 0;
//                 const totalClicks = Number(row.ads_click) || 0;
//                 const totalConversions = Number(row.ads_conversions) || 0;
//                 const totalSpendClicks = Number(row.spend_clicks) || 0;

//                 responseData = [{
//                     rv_revenue: totalRevenue,
//                     rv_rpc: totalClicks === 0 ? 0 : totalRevenue / totalClicks,
//                     profit: totalRevenue - ads_spend,
//                     roi: ads_spend === 0 ? 0 : ((totalRevenue - ads_spend) / ads_spend) * 100,
//                     profit_gst: totalRevenue - ads_spend_with_gst,
//                     roi_gst: ads_spend_with_gst === 0 ? 0 : ((totalRevenue - ads_spend_with_gst) / ads_spend_with_gst) * 100,
//                     ads_conversions: totalConversions,
//                     ads_click: totalClicks,
//                     ads_spend: ads_spend,
//                     ads_cpa: totalConversions === 0 ? 0 : ads_spend / totalConversions,
//                     ads_cpc: totalSpendClicks === 0 ? 0 : ads_spend / totalSpendClicks,
//                     ads_spend_with_gst: ads_spend_with_gst,
//                 }];
//             } else {
//                 responseData = [{
//                     rv_revenue: 0,
//                     rv_rpc: 0,
//                     profit: 0,
//                     roi: 0,
//                     profit_gst: 0,
//                     roi_gst: 0,
//                     ads_conversions: 0,
//                     ads_click: 0,
//                     ads_spend: 0,
//                     ads_cpa: 0,
//                     ads_cpc: 0,
//                     ads_spend_with_gst: 0,
//                 }];
//             }

//             if (InrtoUsd) {
//                 const usdToInrRate = await getUSDtoINRRate();
//                 responseData = convertRevenueToINR(responseData, usdToInrRate);
//             }

//             const meta: MetaData = {
//                 recordCount: responseData.length,
//                 apiDataUsed: false,
//                 revenueDataUsed: false
//             };

//             return NextResponse.json({
//                 success: true,
//                 data: safeJson(responseData),
//                 meta
//             });
//         }

//         // Validation for orderBy parameter
//         const validSortFields = [
//             'ads_spend', 'rv_revenue', 'profit', 'roi', 'profit_gst', 'roi_gst',
//             'ads_conversions', 'ads_click', 'ads_cpa', 'ads_cpc',
//             'articlename', 'channelname', 'date', 'rv_rpc', 'ads_spend_with_gst',
//             'estimated_earnings', 'revenue_clicks'
//         ];

//         if (orderBy.toLowerCase() === 'country') {
//             orderBy = 'ads_spend';
//         } else if (!validSortFields.includes(orderBy.toLowerCase())) {
//             return NextResponse.json({
//                 error: `Invalid orderBy parameter. Valid options: ${validSortFields.join(', ')}`
//             }, { status: 400 });
//         }

//         if (!['asc', 'desc'].includes(orderDir.toLowerCase())) {
//             return NextResponse.json({ error: 'Invalid orderDir parameter. Use asc or desc' }, { status: 400 });
//         }

//         // Build dynamic query for regular database-only case
//         const selectFieldsArray: string[] = [];
//         const groupByFieldsArray: string[] = [];
//         const dateRangeLabel = `${startDate} to ${endDate}`;

//         if (processedGroupBy.includes('date')) {
//             if (showDateRange === 'true') {
//                 selectFieldsArray.push(`'${dateRangeLabel}' as "date"`);
//             } else {
//                 selectFieldsArray.push(`"date"::text as "date"`);
//                 groupByFieldsArray.push('"date"');
//             }
//         }

//         if (processedGroupBy.includes('article')) {
//             selectFieldsArray.push(`"articleid"::text as "articleid"`);
//             selectFieldsArray.push(`"article_name"::text as "articlename"`);
//             groupByFieldsArray.push('"articleid"', '"article_name"');
//         }

//         if (processedGroupBy.includes('channel')) {
//             selectFieldsArray.push(`"channelid"::text as "channelid"`);
//             selectFieldsArray.push(`"channel_name"::text as "channelname"`);
//             groupByFieldsArray.push('"channelid"', '"channel_name"');
//         }

//         if (processedGroupBy.includes('country')) {
//             selectFieldsArray.push(`"country"::text as "country"`);
//             groupByFieldsArray.push('"country"');
//         }

//         selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) as "rv_revenue"`);
//         selectFieldsArray.push(`CASE
//             WHEN COALESCE(SUM("ads_click"::integer), 0) = 0 THEN 0
//             ELSE COALESCE(SUM("rv_revenue"::float), 0) / COALESCE(SUM("ads_click"::integer), 1)
//         END as "rv_rpc"`);
//         selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) - COALESCE(SUM("ads_spend"::float), 0) as "profit"`);
//         selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) - (COALESCE(SUM("ads_spend"::float), 0) * 1.18) as "profit_gst"`);
//         selectFieldsArray.push(`COALESCE(SUM("ads_spend"::float), 0) as "ads_spend"`);
//         selectFieldsArray.push(`(COALESCE(SUM("ads_spend"::float), 0) * 1.18) as "ads_spend_with_gst"`);
//         selectFieldsArray.push(`CASE 
//             WHEN COALESCE(SUM("ads_spend"::float), 0) = 0 THEN 0 
//             ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - COALESCE(SUM("ads_spend"::float), 0)) 
//                 / COALESCE(SUM("ads_spend"::float), 0)) * 100 
//         END as "roi"`);
//         selectFieldsArray.push(`CASE 
//             WHEN (COALESCE(SUM("ads_spend"::float), 0) * 1.18) = 0 THEN 0 
//             ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) 
//                 / (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) * 100 
//         END as "roi_gst"`);
//         selectFieldsArray.push(`COALESCE(SUM("ads_conversions"::integer), 0) as "ads_conversions"`);
//         selectFieldsArray.push(`COALESCE(SUM("ads_click"::integer), 0) as "ads_click"`);
//         selectFieldsArray.push(`COALESCE(SUM("spend_clicks"::integer), 0) as "spend_clicks"`);
//         selectFieldsArray.push(`CASE
//             WHEN COALESCE(SUM("ads_conversions"::integer), 0) = 0 THEN 0
//             ELSE COALESCE(SUM("ads_spend"::float), 0) / COALESCE(SUM("ads_conversions"::integer), 1)
//         END as "ads_cpa"`);
//         selectFieldsArray.push(`CASE
//             WHEN COALESCE(SUM("spend_clicks"::integer), 0) = 0 THEN 0
//             ELSE COALESCE(SUM("ads_spend"::float), 0) / COALESCE(SUM("spend_clicks"::integer), 1)
//         END as "ads_cpc"`);

//         const selectFields = selectFieldsArray.join(',\n            ');
//         const groupByClause = groupByFieldsArray.length > 0 ? `GROUP BY ${groupByFieldsArray.join(', ')}` : '';

//         const orderByClauseArray = [`"${orderBy}" ${orderDir}`];
//         if (processedGroupBy.includes('article') && orderBy !== 'articlename') orderByClauseArray.push('"article_name"');
//         if (processedGroupBy.includes('channel') && orderBy !== 'channelname') orderByClauseArray.push('"channel_name"');
//         if (processedGroupBy.includes('date') && orderBy !== 'date') orderByClauseArray.push('"date"');
//         if (processedGroupBy.includes('country') && orderBy !== 'country') orderByClauseArray.push('"country"');

//         const query = `
//             SELECT ${selectFields}
//             FROM pg_temp.temp_ads_result
//             ${groupByClause}
//             ORDER BY ${orderByClauseArray.join(', ')}
//         `;

//         let rawData: RevenueCampaignData[] = await prisma.$queryRawUnsafe<RevenueCampaignData[]>(query);

//         if (includesCountry && !isOnlyCountryGroupBy) {
//             rawData = aggregateCountryData(rawData, processedGroupBy);
//         }

//         let responseData = rawData.map(row => {
//             const { spend_clicks, ...rest } = row;

//             if (processedGroupBy.includes('article') && processedGroupBy.includes('channel')) {
//             } else if (processedGroupBy.includes('article') && !processedGroupBy.includes('channel')) {
//                 delete rest.channelid;
//                 delete rest.channelname;
//             } else if (processedGroupBy.includes('channel') && !processedGroupBy.includes('article')) {
//                 delete rest.articleid;
//                 delete rest.articlename;
//             }

//             if (processedGroupBy.includes('country')) {
//                 delete rest.country;
//             }

//             return rest;
//         });

//         if (showNoRevenue === 'true') {
//             responseData = responseData.filter(row => {
//                 const rvRevenue = Number(row.rv_revenue) || 0;
//                 const adsSpend = Number(row.ads_spend) || 0;
//                 return !(rvRevenue === 0 && adsSpend === 0);
//             });
//         }

//         if (InrtoUsd) {
//             const usdToInrRate = await getUSDtoINRRate();
//             responseData = convertRevenueToINR(responseData, usdToInrRate);
//         }

//         const meta: MetaData = {
//             recordCount: responseData.length,
//             apiDataUsed: false,
//             revenueDataUsed: false
//         };

//         return NextResponse.json({
//             success: true,
//             data: safeJson(responseData),
//             meta
//         });

//     } catch (error) {
//         console.error('POST function error:', error);
//         return NextResponse.json(
//             {
//                 error: 'Failed to fetch revenue campaign data',
//                 details: process.env.NODE_ENV === 'development' ? error : undefined
//             },
//             { status: 500 }
//         );
//     }
// }






import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

interface RevenueCampaignData {
    articleid?: string;
    articlename?: string;
    date?: string;
    rv_revenue: number;
    rv_rpc: number;
    profit: number;
    roi: number;
    profit_gst?: number;
    roi_gst?: number;
    ads_conversions: number;
    ads_click: number;
    ads_spend: number;
    ads_cpa: number;
    channelid?: string;
    channelname?: string;
    ads_spend_with_gst?: number;
    spend_clicks?: number;
    country?: string;
    rv_impressions: number;
    rv_rpm: number;
    styleid?: string;
    stylename?: string;
}

interface UserInfo {
    Id: string;
    User_Type: string | null;
    styleId?: string;
}

interface CronJobEndTimes {
    'Spending Data': string | null;
    'Revenue Data': string | null;
}

interface RevenueCampaignRequest {
    articleId?: string | string[];
    channelId?: string | string[];
    domainId?: string;
    subDomainId?: string;
    styleId?: string | string[];
    groupBy?: string | string[];
    orderBy?: string;
    orderDir?: 'asc' | 'desc';
}

const safeJson = (obj: any): any => {
    if (Array.isArray(obj)) {
        return obj.map(safeJson);
    } else if (obj !== null && typeof obj === 'object') {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [key, safeJson(value)])
        );
    } else if (typeof obj === 'bigint') {
        return obj.toString();
    } else {
        return obj;
    }
};

async function getUserInfo(userId: string): Promise<UserInfo | null> {
    try {
        const user = await prisma.adminUser.findFirst({
            where: { Id: userId },
            select: {
                Id: true,
                User_Type: true
            }
        });

        if (!user) {
            return null;
        }

        let styleId: string | undefined;
        if (user.User_Type === 'Partner' || user.User_Type === 'Account') {
            const styleMappings = await prisma.styleIdUserMappings.findMany({
                where: { UserId: userId },
                select: { StyleId: true }
            });

            const validMapping = styleMappings.find(mapping => mapping.StyleId !== null);
            styleId = validMapping?.StyleId ?? undefined;
        }

        return {
            Id: user.Id,
            User_Type: typeof user.User_Type === 'string' ? user.User_Type : null,
            styleId
        };
    } catch (error) {
        return null;
    }
}

async function getLatestCronJobEndTimes(): Promise<CronJobEndTimes> {
    try {
        const cronJobs = await prisma.dailyCronJob.findMany({
            where: {
                status: 'completed',
                OR: [
                    { job_name: { contains: 'Google Ads Sense' } },
                    { job_name: { contains: 'Google Ads' } }
                ]
            },
            select: {
                job_name: true,
                end_time: true
            },
            orderBy: {
                end_time: 'desc'
            }
        });

        const latestEndTimes: CronJobEndTimes = {
            'Spending Data': null,
            'Revenue Data': null
        };

        for (const job of cronJobs) {
            if (job.end_time) {
                const endTime = typeof job.end_time === 'string' ? new Date(job.end_time) : job.end_time;

                const localDateTimeString = endTime.toLocaleString('en-GB', {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });

                if (job.job_name.includes('Google Ads Sense') && !latestEndTimes['Revenue Data']) {
                    latestEndTimes['Revenue Data'] = localDateTimeString;
                } else if (
                    job.job_name.includes('Google Ads') &&
                    !job.job_name.includes('Sense') &&
                    !latestEndTimes['Spending Data']
                ) {
                    latestEndTimes['Spending Data'] = localDateTimeString;
                }

                if (latestEndTimes['Spending Data'] && latestEndTimes['Revenue Data']) {
                    break;
                }
            }
        }

        return latestEndTimes;
    } catch (error) {
        console.error('Error fetching cron job end times:', error);
        return {
            'Spending Data': null,
            'Revenue Data': null
        };
    }
}

const VALID_GROUP_BY_COMBINATIONS = [
    '', 'all', 'date', 'country', 'article', 'channel', 'styleId',
    'date,country', 'date,article', 'date,channel', 'date,styleId',
    'country,article', 'country,channel', 'country,styleId',
    'article,channel', 'article,styleId', 'channel,styleId',
    'date,country,article', 'date,country,channel', 'date,country,styleId',
    'date,article,channel', 'date,article,styleId', 'date,channel,styleId',
    'country,article,channel', 'country,article,styleId', 'country,channel,styleId',
    'article,channel,styleId', 'date,country,article,channel',
    'date,country,article,styleId', 'date,country,channel,styleId',
    'date,article,channel,styleId', 'country,article,channel,styleId'
];

const normalizeFieldName = (field: string): string => {
    const normalized = field.toLowerCase().trim();
    if (normalized === 'styleid' || normalized === 'styleId') {
        return 'styleId';
    }
    return normalized;
};

export async function POST(req: NextRequest) {
    try {
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const url = new URL(req.url);
        const searchParams = url.searchParams;

        const latestEndTimes = await getLatestCronJobEndTimes();

        const defaultEndDate = latestEndTimes['Revenue Data'] ||
            latestEndTimes['Spending Data'] ||
            new Date();

        const defaultStartDate = new Date(defaultEndDate);
        defaultStartDate.setDate(defaultStartDate.getDate() - 30);

        const formatDate = (date: Date) => date.toISOString().split('T')[0];

        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const showNoRevenue = searchParams.get('showNoRevenue') || 'true';
        const showDateRange = searchParams.get('showDateRange') || 'true';
        const showGSTValue = searchParams.get('showGSTValue') === 'true';
        const InrtoUsd = searchParams.get('InrtoUsd') === 'true';
        const userIdParam = searchParams.get('userId') || '';
        const body = await req.json();
        let {
            articleId,
            channelId,
            domainId,
            subDomainId,
            styleId,
            groupBy,
            orderBy = 'ads_spend',
            orderDir = 'desc'
        } = body;

        if (!startDate || !endDate) {
            return NextResponse.json({
                error: 'Both startDate and endDate are required'
            }, { status: 400 });
        }

        const isAdmin = role === 'Super Admin' || role === 'Admin';

        let effectiveUser: UserInfo;

        if (isAdmin && userIdParam) {
            const targetUser = await getUserInfo(userIdParam);
            if (!targetUser) {
                return NextResponse.json({
                    error: 'User not found'
                }, { status: 404 });
            }
            effectiveUser = targetUser;
        } else {
            let styleId: string | undefined;
            if (role === 'Partner' || role === 'Account') {
                const styleMapping = await prisma.styleIdUserMappings.findFirst({
                    where: { UserId: userId },
                    select: { StyleId: true }
                });
                styleId = styleMapping?.StyleId || undefined;
            }

            effectiveUser = {
                Id: userId,
                User_Type: typeof role === 'string' ? role : null,
                styleId
            };
        }

        if (styleId) {
            try {
                const styleIds = Array.isArray(styleId) ? styleId : [styleId];
                const styleFilteredArticles = await prisma.articleDetails.findMany({
                    where: {
                        OR: [
                            { StyleIdLm: { in: styleIds } },
                            { StyleIdDm: { in: styleIds } }
                        ],
                        IsDeleted: false
                    },
                    select: {
                        Id: true
                    }
                });

                if (styleFilteredArticles.length === 0) {
                    return NextResponse.json({
                        success: true,
                        data: []
                    });
                }

                const styleFilteredArticleIds = styleFilteredArticles.map(article => article.Id.toString());
                if (articleId) {
                    const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
                    const filteredIds = requestedIds.filter(id => styleFilteredArticleIds.includes(id.toString()));
                    articleId = filteredIds.length > 0 ? filteredIds : styleFilteredArticleIds;
                } else {
                    articleId = styleFilteredArticleIds;
                }
            } catch (dbError) {
                console.error('Error applying styleId filter:', dbError);
                return NextResponse.json({
                    error: 'Failed to apply styleId filter'
                }, { status: 500 });
            }
        }

        if (role === 'Partner' || role === 'Account') {
            try {
                const styleMapping = await prisma.styleIdUserMappings.findFirst({
                    where: {
                        UserId: userId as string
                    },
                    select: {
                        StyleId: true
                    }
                });

                if (!styleMapping || !styleMapping.StyleId) {
                    return NextResponse.json({
                        error: 'Data not Found for this user'
                    }, { status: 403 });
                }

                const articleWhereClause: any = {
                    OR: [
                        { StyleIdLm: styleMapping.StyleId },
                        { StyleIdDm: styleMapping.StyleId }
                    ],
                    IsDeleted: false,
                    User_Id_Settings: userId
                };

                if (domainId) {
                    articleWhereClause.Domain = domainId.toString();
                }

                if (subDomainId) {
                    articleWhereClause.SubDomain = subDomainId.toString();
                }

                const articleDetails = await prisma.articleDetails.findMany({
                    where: articleWhereClause,
                    select: {
                        Id: true
                    }
                });

                if (articleDetails.length === 0) {
                    return NextResponse.json({
                        success: true,
                        data: []
                    });
                }
                const allowedArticleIds = articleDetails.map(article => article.Id.toString());

                if (articleId) {
                    const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
                    const filteredIds = requestedIds.filter(id => allowedArticleIds.includes(id.toString()));
                    articleId = filteredIds.length > 0 ? filteredIds : allowedArticleIds;
                } else {
                    articleId = allowedArticleIds;
                }
            } catch (dbError) {
                console.error('Error fetching user StyleId mappings:', dbError);
                return NextResponse.json({
                    error: 'Failed to fetch user permissions'
                }, { status: 500 });
            }
        } else {
            if (domainId || subDomainId || userIdParam) {
                try {
                    let styleMapping = null;
                    if (userIdParam) {
                        styleMapping = await prisma.styleIdUserMappings.findFirst({
                            where: {
                                UserId: userIdParam as string
                            },
                            select: {
                                StyleId: true
                            }
                        });

                        if (!styleMapping || !styleMapping.StyleId) {
                            return NextResponse.json({
                                error: 'Data not Found for this user'
                            }, { status: 403 });
                        }
                    }

                    const articleWhereClause: any = {
                        IsDeleted: false,
                    };
                    if (styleMapping?.StyleId) {
                        articleWhereClause.OR = [
                            { StyleIdLm: styleMapping.StyleId },
                            { StyleIdDm: styleMapping.StyleId }
                        ];
                    }
                    if (domainId) {
                        articleWhereClause.Domain = domainId.toString();
                    }

                    if (subDomainId) {
                        articleWhereClause.SubDomain = subDomainId.toString();
                    }
                    if (userIdParam) {
                        articleWhereClause.User_Id_Settings = userIdParam.toString();
                    }
                    const articleDetails = await prisma.articleDetails.findMany({
                        where: articleWhereClause,
                        select: {
                            Id: true
                        }
                    });

                    if (articleDetails.length === 0) {
                        return NextResponse.json({
                            success: true,
                            data: []
                        });
                    }

                    const domainFilteredArticleIds = articleDetails.map(article => article.Id.toString());
                    if (articleId) {
                        const requestedIds = Array.isArray(articleId) ? articleId : [articleId];
                        const filteredIds = requestedIds.filter(id => domainFilteredArticleIds.includes(id.toString()));
                        articleId = filteredIds.length > 0 ? filteredIds : domainFilteredArticleIds;
                    } else {
                        articleId = domainFilteredArticleIds;
                    }
                } catch (dbError) {
                    console.error('Error applying domain filters:', dbError);
                    return NextResponse.json({
                        error: 'Failed to apply domain filters'
                    }, { status: 500 });
                }
            }
        }

        const validSortFields = [
            'ads_spend', 'rv_revenue', 'profit', 'roi',
            'ads_conversions', 'ads_click', 'ads_cpa',
            'articlename', 'channelname', 'date', 'rv_rpc',
            'rv_impressions', 'rv_rpm', 'stylename', 'country',
            ...(showGSTValue ? ['profit_gst', 'roi_gst', 'ads_spend_with_gst'] : [])
        ];

        if (!validSortFields.includes(orderBy.toLowerCase())) {
            return NextResponse.json({
                error: `Invalid orderBy parameter. Valid options: ${validSortFields.join(', ')}`
            }, { status: 400 });
        }

        if (!['asc', 'desc'].includes(orderDir.toLowerCase())) {
            return NextResponse.json({ error: 'Invalid orderDir parameter. Use asc or desc' }, { status: 400 });
        }

        let processedGroupBy: string[] = [];
        if (groupBy) {
            if (Array.isArray(groupBy)) {
                processedGroupBy = groupBy.map(field => field.toString().trim().toLowerCase());
            } else if (typeof groupBy === 'string') {
                processedGroupBy = groupBy.split(',').map(field => field.trim().toLowerCase());
            }
        }

        processedGroupBy = processedGroupBy.map(field => {
            if (field === 'styleid') {
                return 'styleId';
            }
            return field;
        });

        const groupByString = processedGroupBy.join(',');

        const normalizedForValidation = processedGroupBy.map(field => normalizeFieldName(field)).sort().join(',');

        const normalizedValidCombinations = VALID_GROUP_BY_COMBINATIONS.map(combo => {
            if (combo === '' || combo === 'all') return combo;
            return combo.split(',').map(field => normalizeFieldName(field)).sort().join(',');
        });

        const isValidCombination = normalizedValidCombinations.includes(normalizedForValidation);

        if (!isValidCombination) {
            return NextResponse.json({
                error: 'Invalid groupBy combination. Please use one of the valid combinations.',
                validCombinations: VALID_GROUP_BY_COMBINATIONS.filter(combo => combo !== ''),
                received: processedGroupBy.join(',')
            }, { status: 400 });
        }

        const processedArticleId = articleId ? (Array.isArray(articleId) ? articleId.join(',') : articleId.toString()) : null;
        const processedChannelId = channelId ? (Array.isArray(channelId) ? channelId.join(',') : channelId.toString()) : null;

        let styleIdParam: string | null = null;
        if (styleId) {
            const styleIds = Array.isArray(styleId) ? styleId : [styleId];
            styleIdParam = styleIds.join(',');
        }

        if (groupByString === 'all') {
            processedGroupBy = ['date', 'article', 'channel', 'styleId'];
        }

        const dbGroupByString = processedGroupBy.join(',');

        await prisma.$executeRawUnsafe(
            `SELECT * FROM fn_ads_revenue_data_test_styleid(
                        $1::text, $2::text, $3::date, $4::date, $5::text, $6::boolean, $7::boolean, $8::text
                    )`,
            processedArticleId,
            processedChannelId,
            startDate,
            endDate,
            dbGroupByString,
            showNoRevenue === 'true',
            InrtoUsd,
            styleIdParam || effectiveUser.styleId || ''
        );

        const selectFieldsArray: string[] = [];
        const groupByFieldsArray: string[] = [];
        const dateRangeLabel = `${startDate} to ${endDate}`;

        for (const field of processedGroupBy) {
            if (field === 'date') {
                if (showDateRange === 'true') {
                    selectFieldsArray.push(`'${dateRangeLabel}' as "date"`);
                } else {
                    selectFieldsArray.push(`"date"::text as "date"`);
                    groupByFieldsArray.push('"date"');
                }
            } else if (field === 'article') {
                selectFieldsArray.push(`"articleid"::text as "articleid"`);
                selectFieldsArray.push(`"article_name"::text as "articlename"`);
                groupByFieldsArray.push('"articleid"', '"article_name"');
            } else if (field === 'channel') {
                selectFieldsArray.push(`"channelid"::text as "channelid"`);
                selectFieldsArray.push(`"channel_name"::text as "channelname"`);
                groupByFieldsArray.push('"channelid"', '"channel_name"');
                // } else if (field === 'country') {
                //     selectFieldsArray.push(`"country"::text as "country"`);
                //     groupByFieldsArray.push('"country"');
            } else if (field === 'styleId') {
                selectFieldsArray.push(`"styleid"::text as "styleid"`);
                selectFieldsArray.push(`"style_name"::text as "stylename"`);
                groupByFieldsArray.push('"styleid"', '"style_name"');
            }
        }

        selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) as "rv_revenue"`);
        selectFieldsArray.push(`CASE
            WHEN COALESCE(SUM("ads_click"::integer), 0) = 0 THEN 0
            ELSE COALESCE(SUM("rv_revenue"::float), 0) / COALESCE(SUM("ads_click"::integer), 1)
        END as "rv_rpc"`);
        selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) - COALESCE(SUM("ads_spend"::float), 0) as "profit"`);
        selectFieldsArray.push(`COALESCE(SUM("ads_spend"::float), 0) as "ads_spend"`);
        selectFieldsArray.push(`CASE 
            WHEN COALESCE(SUM("ads_spend"::float), 0) = 0 THEN 0 
            ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - COALESCE(SUM("ads_spend"::float), 0)) 
                / COALESCE(SUM("ads_spend"::float), 0)) * 100 
        END as "roi"`);
        selectFieldsArray.push(`COALESCE(SUM("ads_conversions"::integer), 0) as "ads_conversions"`);
        selectFieldsArray.push(`COALESCE(SUM("ads_click"::integer), 0) as "ads_click"`);
        selectFieldsArray.push(`CASE
            WHEN COALESCE(SUM("ads_conversions"::integer), 0) = 0 THEN 0
            ELSE COALESCE(SUM("ads_spend"::float), 0) / COALESCE(SUM("ads_conversions"::integer), 1)
        END as "ads_cpa"`);
        selectFieldsArray.push(`COALESCE(SUM("rv_impressions"::integer), 0) as "rv_impressions"`);
        selectFieldsArray.push(`CASE
            WHEN COALESCE(SUM("rv_impressions"::integer), 0) = 0 THEN 0
            ELSE COALESCE(SUM("rv_revenue"::float), 0) * 1000 / COALESCE(SUM("rv_impressions"::integer), 1)
        END as "rv_rpm"`);

        if (showGSTValue) {
            selectFieldsArray.push(`COALESCE(SUM("rv_revenue"::float), 0) - (COALESCE(SUM("ads_spend"::float), 0) * 1.18) as "profit_gst"`);
            selectFieldsArray.push(`(COALESCE(SUM("ads_spend"::float), 0) * 1.18) as "ads_spend_with_gst"`);
            selectFieldsArray.push(`CASE 
                WHEN (COALESCE(SUM("ads_spend"::float), 0) * 1.18) = 0 THEN 0 
                ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) 
                    / (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) * 100 
            END as "roi_gst"`);
        }

        const selectFields = selectFieldsArray.join(',\n            ');
        const groupByClause = groupByFieldsArray.length > 0 ? `GROUP BY ${groupByFieldsArray.join(', ')}` : '';

        const orderByClauseArray = [`"${orderBy}" ${orderDir}`];

        for (const field of processedGroupBy) {
            if (field === 'article' && orderBy !== 'articlename') {
                orderByClauseArray.push('"article_name"');
            } else if (field === 'channel' && orderBy !== 'channelname') {
                orderByClauseArray.push('"channel_name"');
            } else if (field === 'date' && orderBy !== 'date') {
                orderByClauseArray.push('"date"');
                // } else if (field === 'country' && orderBy !== 'country') {
                //     orderByClauseArray.push('"country"');
            } else if (field === 'styleId' && orderBy !== 'stylename') {
                orderByClauseArray.push('"style_name"');
            }
        }

        const query = `
            SELECT ${selectFields}
            FROM pg_temp.temp_ads_result
            ${groupByClause}
            ORDER BY ${orderByClauseArray.join(', ')}
        `;

        const rawData = await prisma.$queryRawUnsafe<RevenueCampaignData[]>(query);

        return NextResponse.json({
            success: true,
            data: safeJson(rawData),
            cronJobInfo: latestEndTimes
        });

    } catch (error) {
        console.error('Error fetching revenue campaign data:', error);
        return NextResponse.json(
            {
                error: 'Failed to fetch revenue campaign data',
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    }
}