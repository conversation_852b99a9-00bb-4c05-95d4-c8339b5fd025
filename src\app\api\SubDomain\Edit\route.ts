import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import path from 'path';
import { randomUUID } from 'crypto';

function getString(formData: FormData, key: string, fallback?: string | null): string | undefined {
    const val = formData.get(key);
    const fallbackSanitized = fallback ?? undefined;
    return typeof val === 'string' && val.trim() !== '' ? val : fallbackSanitized;
}

function getArray(formData: FormData, key: string, fallback: string[] = []): string[] {
    const val = getString(formData, key);
    try {
        return val ? JSON.parse(val) : fallback;
    } catch {
        return fallback;
    }
}

function getStringAllowNull(formData: FormData, key: string, fallback?: string | null) {
    if (!formData.has(key)) return fallback;
    const val = formData.get(key);
    if (val === null) return null;
    return typeof val === 'string' && val.trim() !== '' ? val : null;
}

function getArrayAllowNull(formData: FormData, key: string, fallback?: string[] | null) {
    if (!formData.has(key)) return fallback;
    const val = formData.get(key);
    if (val === null) return null;
    try {
        return typeof val === 'string' && val.trim() !== '' ? JSON.parse(val) : null;
    } catch {
        return null;
    }
}

export async function PUT(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const formData = await req.formData();
        const Id = getString(formData, 'Id');
        if (!Id) return NextResponse.json({ error: 'Id is required' }, { status: 400 });

        const existingSubdomain = await prisma.subDomain.findUnique({ where: { Id, IsDeleted: false } });
        if (!existingSubdomain) {
            return NextResponse.json({ error: 'Subdomain not found' }, { status: 404 });
        }

        const Name = getString(formData, 'Name', existingSubdomain.Name);
        const Url = getString(formData, 'Url', existingSubdomain.Url);
        const Domain = getString(formData, 'Domain', existingSubdomain.Domain);
        const AssignUsers = getArray(formData, 'AssignUsers');

        const AccountId = getArrayAllowNull(formData, 'AccountId', existingSubdomain.AccountId);
        const CId = getStringAllowNull(formData, 'CId', existingSubdomain.CId);
        const HeadTag = getStringAllowNull(formData, 'HeadTag', existingSubdomain.HeadTag);
        const HeadTagScript = getStringAllowNull(formData, 'HeadTagScript', existingSubdomain.HeadTagScript);
        const HeadTagScriptLandingPage = getStringAllowNull(formData, 'HeadTagScriptLandingPage', existingSubdomain.HeadTagScriptLandingPage);
        const HeadTagScriptSearchPage = getStringAllowNull(formData, 'HeadTagScriptSearchPage', existingSubdomain.HeadTagScriptSearchPage);
        const GId = getStringAllowNull(formData, 'GId', existingSubdomain.GId);
        const AWId = getStringAllowNull(formData, 'AWId', existingSubdomain.AWId);
        const SendTo = getStringAllowNull(formData, 'SendTo', existingSubdomain.SendTo);
        const AdsProviderId = getStringAllowNull(formData, 'AdsProviderId', existingSubdomain.AdsProviderId);
        const AdsProviderDescription = getStringAllowNull(formData, 'AdsProviderDescription');
        const TrackingKey = getStringAllowNull(formData, 'TrackingKey');

        if (Name !== existingSubdomain.Name) {
            const duplicate = await prisma.subDomain.findFirst({
                where: {
                    Name,
                    Domain,
                    IsDeleted: false,
                    Id: { not: Id }
                }
            });
            if (duplicate) {
                return NextResponse.json({ error: 'Subdomain with this name already exists in the domain' }, { status: 409 });
            }
        }

        if (Url !== existingSubdomain.Url) {
            const duplicateUrl = await prisma.subDomain.findFirst({
                where: {
                    Url,
                    Domain,
                    IsDeleted: false,
                    Id: { not: Id }
                }
            });
            if (duplicateUrl) {
                return NextResponse.json({ error: 'Subdomain with this URL already exists in the domain' }, { status: 409 });
            }
        }

        const updateData: any = {
            Name,
            Url,
            Domain,
            AccountId,
            CId,
            HeadTag,
            HeadTagScript,
            HeadTagScriptLandingPage,
            HeadTagScriptSearchPage,
            GId,
            AWId,
            SendTo,
            AdsProviderId,
            UpdatedAt: new Date(),
            TrackingKey
        };

        const result = await prisma.$transaction(async (tx) => {
            if (AdsProviderId) {
                try {
                    await tx.adsProvider.update({
                        where: { Id: AdsProviderId },
                        data: {
                            Description: AdsProviderDescription,
                            TrackingKey: TrackingKey
                        }
                    });
                } catch (error) {
                    console.error('Error updating AdsProvider description:', error);
                }
            }

            const updatedSubdomain = await tx.subDomain.update({
                where: { Id },
                data: updateData
            });

            await tx.subDomainUserMappings.deleteMany({ where: { SubDomainId: Id } });

            const mappings = await Promise.all(
                AssignUsers.map(userId =>
                    tx.subDomainUserMappings.create({
                        data: {
                            SubDomainId: Id,
                            UserId: userId
                        }
                    })
                )
            );

            return { updatedSubdomain, mappings };
        });

        return NextResponse.json({
            success: true,
            message: 'Subdomain updated successfully',
            mappingsUpdated: result.mappings.length
        });

    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(
            { error: 'Internal Server Error', details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    }
}