"use client";

import React from "react";
import BaseModal from "./BaseModal";
import { Button } from "@/components/ui-elements/button";

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmVariant?: "primary" | "green" | "danger" | "dark";
  isLoading?: boolean;
  icon?: React.ReactNode;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  confirmVariant = "danger",
  isLoading = false,
  icon,
}) => {
  const handleConfirm = () => {
    if (!isLoading) {
      onConfirm();
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const actions = (
    <>
      <Button
        label={cancelLabel}
        variant="outlineDark"
        shape="rounded"
        onClick={handleClose}
        disabled={isLoading}
      />
      <Button
        label={isLoading ? "Processing..." : confirmLabel}
        variant={confirmVariant}
        shape="rounded"
        onClick={handleConfirm}
        disabled={isLoading}
      />
    </>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title={title}
      maxWidth="sm"
      fullWidth={false}
      disableBackdropClick={isLoading}
      actions={actions}
    >
      <div className="flex flex-col items-center text-center py-4">
        {icon && (
          <div className="mb-4 text-4xl">
            {icon}
          </div>
        )}
        <p className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
          {message}
        </p>
      </div>
    </BaseModal>
  );
};

export default ConfirmationModal;
