// Application routes constants

export const ROUTES = {
  // Authentication
  AUTH: {
    SIGN_IN: '/auth/sign-in',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
  },

  // Main pages
  HOME: '/',
  DASHBOARD: '/',

  // User management
  USERS: {
    LIST: '/allUser',
    CREATE: '/allUser/create',
    EDIT: (id: string) => `/allUser/edit/${id}`,
    VIEW: (id: string) => `/allUser/view/${id}`,
  },

  // Domain management
  DOMAINS: {
    LIST: '/domains',
    ADD: '/domains/add',
    EDIT: (id: string) => `/domains/edit/${id}`,
    VIEW: (id: string) => `/domains/view/${id}`,
    ASSIGN_USERS: '/domains/assign-users',
  },

  // Subdomain management
  SUBDOMAINS: {
    LIST: '/subdomains',
    CREATE: '/subdomains/create',
    EDIT: (id: string) => `/subdomains/edit/${id}`,
    VIEW: (id: string) => `/subdomains/view/${id}`,
  },

  // Style ID management
  STYLE_IDS: {
    LIST: '/style-ids',
    CREATE: '/style-ids/create',
    EDIT: (id: string) => `/style-ids/edit/${id}`,
    VIEW: (id: string) => `/style-ids/view/${id}`,
    ASSIGN: '/style-ids/assign',
  },

  // Article management
  ARTICLES: {
    LIST: '/articles',
    CREATE: '/articles/create',
    EDIT: (id: string) => `/articles/edit/${id}`,
    VIEW: (id: string) => `/articles/view/${id}`,
  },

  // Category management
  CATEGORIES: {
    LIST: '/category',
    CREATE: '/category/create',
    EDIT: (id: string) => `/category/edit/${id}`,
    VIEW: (id: string) => `/category/view/${id}`,
  },

  // Reports
  REPORTS: {
    REVENUE: '/revenue',
    REVENUE_CAMPAIGNS: '/revenuecampaingsmanagement',
    KEYWORDS_ANALYTICS: '/keywords-analytics',
    TODAY_REFERRALS: '/today-referals',
    CUSTOM_CHANNELS: '/reports/custom-channels',
  },

  // Settings
  SETTINGS: '/settings',

  // Ads management
  ADS: {
    ACCOUNTS: '/ads-accounts',
  },

  // Other
  LOGOUT: '/logout',
} as const;

// Public routes (no authentication required)
export const PUBLIC_ROUTES = [
  ROUTES.AUTH.SIGN_IN,
  ROUTES.AUTH.FORGOT_PASSWORD,
  ROUTES.AUTH.RESET_PASSWORD,
] as const;

// Protected routes (authentication required)
export const PROTECTED_ROUTES = [
  ROUTES.HOME,
  ROUTES.USERS.LIST,
  ROUTES.DOMAINS.LIST,
  ROUTES.DOMAINS.ADD,
  ROUTES.DOMAINS.ASSIGN_USERS,
  ROUTES.SUBDOMAINS.LIST,
  ROUTES.STYLE_IDS.LIST,
  ROUTES.STYLE_IDS.ASSIGN,
  ROUTES.ARTICLES.LIST,
  ROUTES.CATEGORIES.LIST,
  ROUTES.REPORTS.REVENUE,
  ROUTES.REPORTS.REVENUE_CAMPAIGNS,
  ROUTES.REPORTS.KEYWORDS_ANALYTICS,
  ROUTES.REPORTS.TODAY_REFERRALS,
  ROUTES.REPORTS.CUSTOM_CHANNELS,
  ROUTES.SETTINGS,
  ROUTES.ADS.ACCOUNTS,
] as const;

// Admin only routes
export const ADMIN_ROUTES = [
  ROUTES.USERS.LIST,
  ROUTES.DOMAINS.LIST,
  ROUTES.DOMAINS.ADD,
  ROUTES.DOMAINS.ASSIGN_USERS,
  ROUTES.SUBDOMAINS.LIST,
  ROUTES.STYLE_IDS.LIST,
  ROUTES.STYLE_IDS.ASSIGN,
  ROUTES.SETTINGS,
] as const;

// Navigation menu structure
export const NAVIGATION_MENU = [
  {
    title: 'Dashboard',
    url: ROUTES.HOME,
    icon: 'DashboardIcon',
  },
  {
    title: 'Articles',
    url: ROUTES.ARTICLES.LIST,
    icon: 'ArticleIcon',
  },
  {
    title: 'Revenue Campaigns Management',
    url: ROUTES.REPORTS.REVENUE_CAMPAIGNS,
    icon: 'RevenueIcon',
  },
  {
    title: 'Revenue',
    url: ROUTES.REPORTS.REVENUE,
    icon: 'RevenueIcon',
  },
  {
    title: 'Keywords Analytics',
    url: ROUTES.REPORTS.KEYWORDS_ANALYTICS,
    icon: 'KeywordsAnalyticsIcon',
  },
  {
    title: 'Users',
    url: ROUTES.USERS.LIST,
    icon: 'UsersIcon',
  },
  {
    title: 'Settings',
    url: ROUTES.SETTINGS,
    icon: 'SettingsIcon',
  },
  {
    title: 'Manage Sites',
    icon: 'DomainIcon',
    items: [
      {
        title: 'Get Ads Accounts',
        url: ROUTES.ADS.ACCOUNTS,
      },
      {
        title: 'Domains',
        url: ROUTES.DOMAINS.ADD,
      },
      {
        title: 'Sub Domains',
        url: ROUTES.SUBDOMAINS.LIST,
      },
      {
        title: 'Categories',
        url: ROUTES.CATEGORIES.LIST,
      },
      {
        title: 'Assign Users',
        url: ROUTES.DOMAINS.ASSIGN_USERS,
      },
    ],
  },
  {
    title: 'Style IDs',
    icon: 'StyleIdIcon',
    items: [
      {
        title: 'Style ID List',
        url: ROUTES.STYLE_IDS.LIST,
      },
      {
        title: 'Assign Style ID',
        url: ROUTES.STYLE_IDS.ASSIGN,
      },
    ],
  },
] as const;
