"use client";
import React, { useState, useEffect, useCallback } from "react";
import { FaChevronDown, FaChevronRight, FaFilter } from "react-icons/fa";
import Swal from "sweetalert2";
import { Button } from "@/components/ui-elements/button";
import DateRangePicker from "@/components/DateRangePicker";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import { formatDate, getCurrency, decodeJWT } from "@/utils/functions";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  TableSortLabel,
  Box,
  CircularProgress,
} from "@mui/material";
import { visuallyHidden } from "@mui/utils";
import ArticleModal from "../../articles/Components/ArticleModal";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import apiClient from "@/utils/axiosInstance";
import Checkbox from "@/components/FormElements/checkbox";
import InputGroup from "@/components/FormElements/InputGroup";

// Utility function to validate formData structure
const validateFormData = (formData) => {
  const defaultFormData = {
    title: { val: "", err: "" },
    category: { val: "", err: "" },
    url: { val: "", err: "" },
    description: { val: "", err: "" },
    shortDescription: { val: "", err: "" },
    metatitle: { val: "", err: "" },
    metadescription: { val: "", err: "" },
    metakeys: { val: "", err: "" },
    hashtag: { val: "", err: "" },
    customChannal: { val: "", err: "" },
    styleIdLm: { val: "", err: "" },
    styleIdDm: { val: "", err: "" },
    domain: { val: "", err: "" },
    subdomain: { val: "", err: "" },
    adrelatedsearches: { val: "7", err: "" },
    remark: { val: "", err: "" },
    campaigns: { val: [], err: "" },
    readTime: { val: "", err: "" },
  };
  return {
    ...defaultFormData,
    ...Object.keys(formData).reduce((acc, key) => {
      acc[key] = formData[key] || defaultFormData[key] || { val: "", err: "" };
      return acc;
    }, {}),
  };
};

const RevenueCampaignsManagement = () => {
  const getYesterdayDate = () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday;
  };
  const [campaignsData, setCampaignsData] = useState([]);
  const [grandTotals, setGrandTotals] = useState({});
  const [showLoader, setShowLoader] = useState(false);
  const [expandedRows, setExpandedRows] = useState({});
  const [campaignDetails, setCampaignDetails] = useState({});
  const [loadingDetails, setLoadingDetails] = useState({});
  const [startDate, setStartDate] = useState(getYesterdayDate());
  const [endDate, setEndDate] = useState(getYesterdayDate());
  const [orderBy, setOrderBy] = useState("ads_spend");
  const [orderDir, setOrderDir] = useState("desc");
  const [isDateRangeSelected, setIsDateRangeSelected] = useState(false);
  const [selectedBreakPoints, setSelectedBreakPoints] = useState([]);
  const [channelOptions, setChannelOptions] = useState([]);
  const [selectedChannelIds, setSelectedChannelIds] = useState([]);
  const [articleOptions, setArticleOptions] = useState([]);
  const [selectedArticleIds, setSelectedArticleIds] = useState([]);
  const [tableHeaders, setTableHeaders] = useState([]);
  const [fieldMap, setFieldMap] = useState({});
  const [showNoRevenue, setShowNoRevenue] = useState(true);
  const [isDetailsLoading, setIsDetailsLoading] = useState(false);
  const [showInUSD, setShowInUSD] = useState(false); // false shows INR, true shows USD
  const [totalCurrency, setTotalCurrency] = useState(false);
  const [showGSTColumns, setShowGSTColumns] = useState(false);

  const [selectedDomainId, setSelectedDomainId] = useState(""); // Default to "All Domains"
  const [selectedSubdomainFilter, setSelectedSubdomainFilter] = useState({ Id: "", Name: "All Subdomains" }); // Default to "All Subdomains"
  const [subdomainOptions, setSubdomainOptions] = useState([{ Id: "", Name: "All Subdomains" }]); // Default subdomain option
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  // ArticleModal state
  const [modalShow, setModalShow] = useState(false);
  const [editId, setEditId] = useState(null);
  const [formData, setFormData] = useState({
    title: { val: "", err: "" },
    category: { val: "", err: "" },
    url: { val: "", err: "" },
    description: { val: "", err: "" },
    shortDescription: { val: "", err: "" },
    metatitle: { val: "", err: "" },
    metadescription: { val: "", err: "" },
    metakeys: { val: "", err: "" },
    hashtag: { val: "", err: "" },
    customChannal: { val: "", err: "" },
    styleIdLm: { val: "", err: "" },
    styleIdDm: { val: "", err: "" },
    domain: { val: "", err: "" },
    subdomain: { val: "", err: "" },
    adrelatedsearches: { val: "7", err: "" },
    remark: { val: "", err: "" },
    campaigns: { val: [], err: "" },
    readTime: { val: "", err: "" },
  });
  const [description, setDescription] = useState("");
  const [shortDescription, setShortDescription] = useState("");
  const [formdataImage, setFormdataImage] = useState("");
  const [base64Image, setBase64Image] = useState("");
  const [published, setPublished] = useState(false);
  const [showArticle, setShowArticle] = useState(true);
  const [showAds, setShowAds] = useState(true);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [selectedSubdomain, setSelectedSubdomain] = useState("");
  const [selectedCampaignIds, setSelectedCampaignIds] = useState([]);
  const [domains, setDomains] = useState([]);
  const [subdomains, setSubdomains] = useState([]);
  const [categories, setCategories] = useState([]);
  const [assignChannels, setAssignChannels] = useState([]);
  const [styleIds, setStyleIds] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [editSlugMode, setEditSlugMode] = useState(false);
  const [token, setToken] = useState(null);
  const [userData, setUserData] = useState(null);
  const [partnerUsers, setPartnerUsers] = useState([]);
  const [selectedPartner, setSelectedPartner] = useState("");
  const [cronJobInfo, setCroneJobInfo] = useState({});
  const [styleOptions, setStyleOptions] = useState([]);
  const [selectedStyleIds, setSelectedStyleIds] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [appliedGSTColumns, setAppliedGSTColumns] = useState(false);
  const breakPointOptions = [
    { value: "article", label: "Article" },
    { value: "channel", label: "Channel" },
    { value: "styleId", label: "Style ID" },
    { value: "country", label: "Country" },
    { value: "date", label: "Date" },  // Add this line
  ];

  const formatDateForAPI = (date) => {
    if (!date) return "";
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const formatDateForDisplay = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toISOString().split("T")[0];
  };

  function getCellBackgroundColor(value) {
    if (value < 0) return "bg-red-100";
    if (value >= 0) return "bg-green-100";
    return "";
  }

  const handleQuickDateSelect = (range) => {
    const today = new Date();
    let newStartDate, newEndDate;

    switch (range) {
      case "today":
        newStartDate = new Date(today);
        newEndDate = new Date(today);
        break;
      case "yesterday":
        newStartDate = new Date(today);
        newStartDate.setDate(today.getDate() - 1);
        newEndDate = new Date(newStartDate);
        break;
      case "last7days":
        newStartDate = new Date(today);
        newStartDate.setDate(today.getDate() - 6);
        newEndDate = new Date(today);
        break;
      case "last30days":
        newStartDate = new Date(today);
        newStartDate.setDate(today.getDate() - 29);
        newEndDate = new Date(today);
        break;
      case "thismonth":
        newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
        newEndDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case "lastmonth":
        newStartDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        newEndDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      default:
        newStartDate = null;
        newEndDate = null;
        break;
    }

    if (newStartDate && newEndDate) {
      setStartDate(newStartDate);
      setEndDate(newEndDate);
    }
  };
  const filterDataBySearch = (data, searchTerm) => {
    if (!searchTerm.trim()) return data;
    
    const lowercasedSearch = searchTerm.toLowerCase();
    
    return data.filter(item => {
      const original = item.original;
      
      // Search in various fields
      return (
        (original.articlename && original.articlename.toLowerCase().includes(lowercasedSearch)) ||
        (original.channelname && original.channelname.toLowerCase().includes(lowercasedSearch)) ||
        (original.stylename && original.stylename.toLowerCase().includes(lowercasedSearch)) ||
        (original.country && original.country.toLowerCase().includes(lowercasedSearch)) ||
        (original.articleid && original.articleid.toString().includes(lowercasedSearch)) ||
        (original.channelid && original.channelid.toString().includes(lowercasedSearch)) ||
        (original.styleId && original.styleId.toString().includes(lowercasedSearch))
      );
    });
  };
  const validateField = (name, value) => {
    switch (name) {
      case "title":
        return value.trim() ? "" : "Title is required";
      case "category":
        return value ? "" : "Category is required";
      case "url":
        return value.trim() ? "" : "URL is required";
      case "description":
        return value.trim() ? "" : "Description is required";
      case "shortDescription":
        return value.trim() ? "" : "Short Description is required";
      case "domain":
        return value ? "" : "Domain is required";
      case "subdomain":
        return value ? "" : "Subdomain is required";
      case "customChannal":
        return value ? "" : "Channel is required";
      case "readTime":
        return value && !isNaN(value) && parseInt(value) >= 0
          ? ""
          : "Valid read time is required";
      default:
        return "";
    }
  };

  const autoslug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  };

// Fetch domains
const fetchDomains = useCallback(async () => {
  try {
    const response = await apiClient.get("/Domain/GetDropDown");
    if (response?.status === 200) {
      setDomains(response.data.data || []);
    }
  } catch (error) {
    console.error("Error fetching domains:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Failed to load domains",
      timer: 3000,
      showConfirmButton: false,
    });
  }
}, []);

// Fetch subdomains
const fetchSubdomains = useCallback(async (domainId) => {
  if (!domainId) {
    // Reset when no domain is selected
    setSubdomains([]);
    setSubdomainOptions([{ Id: "", Name: "All Subdomains" }]);
    setSelectedSubdomainFilter({ Id: "", Name: "All Subdomains" });
    return [];
  }

  try {
    const response = await apiClient.get(`/SubDomain/GetDropDown`, {
      params: { DomainId: domainId },
    });    
    if (response?.status === 200) {
      const subs = response.data.data || [];
      setSubdomains(subs);
      const subdomainOptions = [
        { Id: "", Name: "All Subdomains" }, // Always include "All Subdomains"
        ...subs.map((s) => ({ Id: s.Id, Name: s.Name })),
      ];
      setSubdomainOptions(subdomainOptions);
      setSelectedSubdomainFilter({ Id: "", Name: "All Subdomains" }); // Reset to "All Subdomains"
      return subs;
    }
    return [];
  } catch (error) {
    console.error("Error fetching subdomains:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Failed to load subdomains",
      timer: 3000,
      showConfirmButton: false,
    });
    return [];
  }
}, []);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await apiClient.get("/category/GetDropdown");
      if (response?.status === 200) {
        setCategories(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load categories",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchChannels = useCallback(async () => {
    try {
      const response = await apiClient.get("/Channals/GetDropdown");
      if (response?.status === 200) {
        setAssignChannels(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching channels:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load channels",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchStyleIds = useCallback(async (userId = null) => {
    try {
      const url = userId
        ? `/StyleIds/getdropdown?userId=${userId}`
        : `/StyleIds/getdropdown`;
      const response = await apiClient.get(url);
      if (response?.status === 200) {
        setStyleIds(response.data.data || []);

        const options = response.data.data.map((style) => ({
          value: style.Id,
          label: style.Name,
        }));
        setStyleOptions(options);
      }
    } catch (error) {
      console.error("Error fetching style IDs:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load style IDs",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchCampaigns = useCallback(async (subdomainId) => {
    if (!subdomainId) {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
      return;
    }

    try {
      const response = await apiClient.get("/Campaigns/GetDropdown", {
        params: { subdomainId },
      });      
      setCampaigns(response.data.data || []);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch campaigns",
        timer: 3000,
        showConfirmButton: false,
      });
      setCampaigns([]);
    }
  }, []);

  const fetchPartnerUsers = useCallback(async () => {
    try {
      const response = await apiClient.get("/adminuser/userDropdown");
      if (response?.status === 200) {
        setPartnerUsers(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching partner users:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load partner users",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const handleChange = (e) => {
    const { name, value, type, files } = e.target;
    if (!Object.keys(formData).includes(name)) {
      console.warn(`Invalid form field: ${name}`);
      return;
    }
    if (type === "file") {
      const file = files[0];
      if (file) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setBase64Image(reader.result);
          setFormdataImage(file);
        };
        reader.readAsDataURL(file);
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: {
          val: value,
          err: validateField(name, value),
        },
      }));
      if (name === "title" && !editSlugMode) {
        setFormData((prev) => ({
          ...prev,
          url: {
            val: autoslug(value),
            err: validateField("url", autoslug(value)),
          },
        }));
      }
    }
  };

  const handleChannelSelect = (channel) => {
    setFormData((prev) => ({
      ...prev,
      customChannal: {
        val: channel?.Id || "",
        err: validateField("customChannal", channel?.Id || ""),
      },
    }));
  };

  const handleCampaignSelect = (campaignIds) => {
    setSelectedCampaignIds(campaignIds);
    setFormData((prev) => ({
      ...prev,
      campaigns: {
        val: campaignIds || [],
        err: validateField("campaigns", campaignIds),
      },
    }));
  };

  const resetFormState = () => {
    setFormData({
      title: { val: "", err: "" },
      category: { val: "", err: "" },
      url: { val: "", err: "" },
      description: { val: "", err: "" },
      shortDescription: { val: "", err: "" },
      metatitle: { val: "", err: "" },
      metadescription: { val: "", err: "" },
      metakeys: { val: "", err: "" },
      hashtag: { val: "", err: "" },
      customChannal: { val: "", err: "" },
      styleIdLm: { val: "", err: "" },
      styleIdDm: { val: "", err: "" },
      domain: { val: "", err: "" },
      subdomain: { val: "", err: "" },
      adrelatedsearches: { val: "7", err: "" },
      remark: { val: "", err: "" },
      campaigns: { val: [], err: "" },
      readTime: { val: "", err: "" },
    });
    setDescription("");
    setShortDescription("");
    setFormdataImage("");
    setBase64Image("");
    setPublished(false);
    setShowArticle(true);
    setShowAds(true);
    setSelectedDomain("");
    setSelectedSubdomain("");
    setSelectedCampaignIds([]);
    setEditSlugMode(false);
  };

  const handleEditArticle = async (articleId) => {
    setShowLoader(true);
    try {
      const loadPromises = [];
      if (assignChannels.length === 0) loadPromises.push(fetchChannels());
      if (domains.length === 0) loadPromises.push(fetchDomains());
      if (categories.length === 0) loadPromises.push(fetchCategories());
      if (styleIds.length === 0) loadPromises.push(fetchStyleIds());
      if (campaigns.length === 0) loadPromises.push(fetchCampaigns());
      await Promise.all(loadPromises);

      const response = await apiClient.get("/article/GetById", {
        params: { id: articleId },
      });      

      if (!response.data.success || !response.data.data?.length) {
        throw new Error("No article data found");
      }

      const article = response.data.data[0];
      if (!article) {
        throw new Error("Article data is undefined");
      }
      setEditId(articleId);
      setModalShow(true);
      setEditSlugMode(false);

      setPublished(!!article.Published);
      setShowArticle(!!article.ShowArticle);
      setShowAds(!!article.ShowsAds);
      setDescription(article.Description || "");
      setShortDescription(article.ShortDescription || "");
      setSelectedDomain(article.DomainId || "");
      setSelectedSubdomain(article.SubDomainId || "");
      setSelectedCampaignIds(article.CampaignIds || []);

      if (article.DomainId) {
        await fetchSubdomains(article.DomainId);
      }
      if (article.SubDomainId) {
        await fetchCampaigns(article.SubDomainId);
        if (article.Campaigns && article.Campaigns.length > 0) {
          const campaignIds = article.Campaigns.map((c) => c.SNo.toString());
          setSelectedCampaignIds(campaignIds);
        }
      }

      const newFormData = {
        title: { val: article.Title || "", err: "" },
        category: { val: article.CategoryId || "", err: "" },
        url: { val: article.Url || "", err: "" },
        description: { val: article.Description || "", err: "" },
        shortDescription: { val: article.ShortDescription || "", err: "" },
        metatitle: { val: article.MetaTitle || "", err: "" },
        metadescription: { val: article.MetaDescription || "", err: "" },
        metakeys: { val: article.MetaKeys || "", err: "" },
        hashtag: { val: article.Hashtag || "", err: "" },
        customChannal: { val: article.ChannelId || "", err: "" },
        styleIdLm: { val: article.StyleIdLm, err: "" },
        styleIdDm: { val: article.StyleIdDm, err: "" },
        domain: { val: article.DomainId || "", err: "" },
        subdomain: { val: article.SubDomainId || "", err: "" },
        adrelatedsearches: { val: article.AdRelatedSearches || "7", err: "" },
        remark: { val: article.Remark || "", err: "" },
        campaigns: { val: article.CampaignIds || [], err: "" },
        readTime: { val: article.ReadTime?.toString() || "", err: "" },
      };

      setFormData(validateFormData(newFormData));

      if (article.Image) {
        setFormdataImage(
          fetchImage(
            article.Image.includes("cloudinary") ? "cloud" : "small",
            article.Image
          )
        );
      } else {
        setFormdataImage("");
      }
      setBase64Image("");
    } catch (error) {
      console.error("Error fetching article:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.error || "Failed to load article data",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    const errors = {};
    Object.keys(formData).forEach((key) => {
      errors[key] = validateField(key, formData[key].val);
    });

    if (Object.values(errors).some((err) => err)) {
      setFormData((prev) => ({
        ...prev,
        ...Object.keys(errors).reduce(
          (acc, key) => ({
            ...acc,
            [key]: { ...prev[key], err: errors[key] },
          }),
          {}
        ),
      }));
      Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Please fill in all required fields correctly",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    setShowLoader(true);
    try {
      const formDataToSend = new FormData();
      formDataToSend.append("Id", editId);
      formDataToSend.append("Title", formData.title.val);
      formDataToSend.append("CategoryId", formData.category.val);
      formDataToSend.append("Url", formData.url.val);
      formDataToSend.append("Description", description);
      formDataToSend.append("ShortDescription", shortDescription);
      formDataToSend.append("MetaTitle", formData.metatitle.val);
      formDataToSend.append("MetaDescription", formData.metadescription.val);
      formDataToSend.append("MetaKeys", formData.metakeys.val);
      formDataToSend.append("Hashtag", formData.hashtag.val);
      formDataToSend.append("ChannelId", formData.customChannal.val);
      formDataToSend.append("StyleIdDm", formData.styleIdDm.val);
      formDataToSend.append("StyleIdLm", formData.styleIdLm.val);
      formDataToSend.append("DomainId", selectedDomain);
      formDataToSend.append("SubDomainId", selectedSubdomain);
      formDataToSend.append("AdRelatedSearches", formData.adrelatedsearches.val);
      formDataToSend.append("Remark", formData.remark.val);
      formDataToSend.append("Published", published);
      formDataToSend.append("ShowArticle", showArticle);
      formDataToSend.append("ShowsAds", showAds);
      formDataToSend.append("ReadTime", formData.readTime.val);
      selectedCampaignIds.forEach((id) =>
        formDataToSend.append("CampaignIds[]", id)
      );
      if (formdataImage && typeof formdataImage !== "string") {
        formDataToSend.append("Image", formdataImage);
      }

      const response = await apiClient.put("/article/edit", formDataToSend);

      if (response.data.success) {
        Swal.fire({
          icon: "success",
          title: "Success",
          text: "Article updated successfully",
          timer: 3000,
          showConfirmButton: false,
        });
        setModalShow(false);
        resetFormState();
        // Refresh article options
        fetchArticleOptions();
        // Refresh campaigns data
        if (isDateRangeSelected && startDate && endDate) {
          processDataWithSort(orderBy, orderDir);
        }
      } else {
        throw new Error(response.data.error || "Failed to update article");
      }
    } catch (error) {
      console.error("Error updating article:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.error || "Failed to update article",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const addTimeOffset = (timeString, hours = 5, minutes = 30) => {
    // Convert string to Date
    const date = new Date(timeString);
  
    // Add offset
    date.setHours(date.getHours() + hours);
    date.setMinutes(date.getMinutes() + minutes);
  
    // Format back to a readable string
    return date.toLocaleString('en-GB', { 
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true 
    });
  };
  const generateRowKey = (item, selectedBreakPoints, index) => {
    const keyParts = [];
    
    // Always include articleid and date as base identifiers
    if (item.articleid) keyParts.push(`article_${item.articleid}`);
    if (item.date) keyParts.push(`date_${item.date}`);
    
    // Add other identifiers based on selected breakpoints
    if (selectedBreakPoints.includes("channel") && item.channelid) {
      keyParts.push(`channel_${item.channelid}`);
    }
    if (selectedBreakPoints.includes("country") && item.country) {
      keyParts.push(`country_${item.country}`);
    }
    if (selectedBreakPoints.includes("styleId") && (item.styleId || item.styleid)) {
      keyParts.push(`style_${item.styleId || item.styleid}`);
    }
    
    // Use index as fallback - it's stable between renders
    if (keyParts.length === 0) {
      keyParts.push(`index_${index}`);
    } else {
      // Always include index to guarantee uniqueness
      keyParts.push(`index_${index}`);
    }
    
    return keyParts.join("_");
  };
  
 const processDataWithSort = async (sortBy, sortDir) => {
    if (!startDate || !endDate) {
      Swal.fire({
        icon: "warning",
        title: "Date Range Required",
        text: "Please select both start and end dates before applying.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }
    setIsDetailsLoading(true);
    setShowLoader(true);
    setExpandedRows({});
    setCampaignDetails({});
    setLoadingDetails({});
  
    try {
      const formattedStartDate = formatDateForAPI(startDate);
      const formattedEndDate = formatDateForAPI(endDate);
      const groupBy = getGroupByValue();
      const channelId = selectedChannelIds.join(",");
      const articleId = selectedArticleIds.join(",");
  
      const response = await apiClient.post(
        "/RCAnalytics/GetDetails",
        {
          channelId: channelId || "",
          articleId: articleId || "",
          styleId: selectedStyleIds.join(",") || "",
          groupBy,
          domainId: selectedDomainId || "",
          subDomainId: selectedSubdomainFilter?.Id || "",
          orderBy: sortBy,
          orderDir: sortDir,
        },
        {
          params: {
            startDate: formattedStartDate,
            endDate: formattedEndDate,
            showNoRevenue,
            userId: selectedPartner || "",
            InrtoUsd: showInUSD,
            showGSTValue: showGSTColumns,
          },
        }
      );      
      const { data, cronJobInfo } = response.data;
     
      const updatedCronJobInfo = {
        "Spending Data": addTimeOffset(cronJobInfo["Spending Data"]),
        "Revenue Data": addTimeOffset(cronJobInfo["Revenue Data"])
      };

      setCroneJobInfo(updatedCronJobInfo);
      setAppliedGSTColumns(showGSTColumns);
      if (!data || data.length === 0) {
        Swal.fire({
          icon: "info",
          title: "No Data Found",
          text: "No data available for the selected date range and filters.",
          timer: 3000,
          showConfirmButton: false,
        });
        setCampaignsData([]);
        setGrandTotals({});
        setTableHeaders([]);
        return;
      }
  
      const sampleItem = data[0];
  
      // Define dimension columns in the desired order
      const dimensionColumns = [];
      if (selectedBreakPoints.length > 0) {
        dimensionColumns.push({ name: "EXPAND", type: "DIMENSION" });
      }
      if (selectedBreakPoints.includes("article") && sampleItem.articlename) {
        dimensionColumns.push({ name: "ARTICLENAME", type: "DIMENSION" });
      }
      if (selectedBreakPoints.includes("date")) {
        dimensionColumns.push({ name: "DATE", type: "DIMENSION" });
      }
      if (selectedBreakPoints.includes("channel") && sampleItem.channelname) {
        dimensionColumns.push({ name: "CHANNELNAME", type: "DIMENSION" });
      }
      if (selectedBreakPoints.includes("country")) {
        dimensionColumns.push({ name: "COUNTRY", type: "DIMENSION" });
      }
      if (selectedBreakPoints.includes("styleId") && sampleItem.stylename) {
        dimensionColumns.push({ name: "STYLENAME", type: "DIMENSION" }); // Add this line
      }
  
      const metricColumns = [
        { name: "ADS_SPEND", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
        ...(showGSTColumns ? [
          { name: "ADS_SPEND_WITH_GST", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
        ] : []),
        { name: "RV_REVENUE", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
        { name: "PROFIT", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
        { name: "ROI", type: "METRIC", isPercentage: true },
        // Conditionally include GST columns
        ...(showGSTColumns ? [
          { name: "PROFIT_GST", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
          { name: "ROI_GST", type: "METRIC", isPercentage: true },
        ] : []),
        { name: "ADS_CPA", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
        { name: "ADS_CPC", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
        { name: "RV_RPM", type: "METRIC", currencyCode: showInUSD ? "USD" : "INR" },
        { name: "RV_IMPRESSIONS", type: "METRIC" },
        { name: "ADS_CONVERSIONS", type: "METRIC" },
        { name: "ADS_CLICK", type: "METRIC" },
      ].filter(col => {
        const fieldName = col.name.toLowerCase();
        return Object.keys(sampleItem).includes(fieldName);
      });

      const headers = [...dimensionColumns, ...metricColumns];
  
      const newFieldMap = {
        ARTICLENAME: "articlename",
        DATE: "date",
        CHANNELNAME: "channelname",
        PROFIT_GST: "profit_gst",
        ROI_GST: "roi_gst",
        STYLENAME: "stylename",
        COUNTRY: "country",
        ...metricColumns.reduce(
          (acc, col) => ({
            ...acc,
            [col.name]: col.name.toLowerCase(),
          }),
          {}
        ),
      };
  
      setTableHeaders(headers);
      setFieldMap(newFieldMap);
  
      // Rest of the function remains unchanged...
      const rows = data.map((item, index) => {
        const key = generateRowKey(item, selectedBreakPoints, index);
        const cells = headers.map((header) => {
          if (header.name === "EXPAND") return { value: "" };
          const fieldName = newFieldMap[header.name];
          let value;
          if (header.name === "DATE") {
            value = `${formatDate(startDate)} - ${formatDate(endDate)}`;
          } else if (header.name === "ARTICLENAME") {
            value = item.articlename || "-";
          } else if (header.name === "CHANNELNAME") {
            value = item.channelname || "-";
          }  else if (header.name === "STYLENAME") {
            value = item.stylename || "-";
          }  else if (header.name === "COUNTRY")   {
            value = item.country || "-";
          } else if (header.type === "METRIC") {
            const numericValue = parseFloat(item[fieldName]);
            if (!isNaN(numericValue)) {
              if (header.currencyCode) {
                value = `${getCurrency(header.currencyCode)}${numericValue.toFixed(2)}`;
              } else if (header.isPercentage) {
                value = `${numericValue.toFixed(2)}%`;
              } else {
                value = numericValue.toLocaleString();
              }
            } else {
              value = "-";
            }
          }
          return { value };
        });
        return { id: key, cells, original: item };
      });
  
      setCampaignsData(rows);
  
      const totals = data.reduce(
        (acc, item) => ({
          totalRevenue: acc.totalRevenue + (parseFloat(item.rv_revenue) || 0),
          totalProfit: acc.totalProfit + (parseFloat(item.profit) || 0),
          totalProfitGst: acc.totalProfitGst + (parseFloat(item.profit_gst) || 0),
          totalSpendWithGst: acc.totalSpendWithGst + (parseFloat(item.ads_spend_with_gst) || 0),
          totalSpend: acc.totalSpend + (parseFloat(item.ads_spend) || 0),
          totalConversions: acc.totalConversions + (parseFloat(item.ads_conversions) || 0),
          totalClicks: acc.totalClicks + (parseFloat(item.ads_click) || 0),
        }),
        {
          totalRevenue: 0,
          totalProfit: 0,
          totalProfitGst: 0,
          totalSpend: 0,
          totalSpendWithGst: 0,
          totalConversions: 0,
          totalClicks: 0,
        }
      );
  
      totals.roi =
        totals.totalSpend !== 0
          ? ((totals.totalRevenue - totals.totalSpend) / totals.totalSpend) * 100
          : totals.totalRevenue > 0
          ? Infinity
          : totals.totalRevenue < 0
          ? -Infinity
          : 0;
  
      totals.roiGst = totals.totalSpend !== 0
        ? ((totals.totalRevenue - totals.totalSpend * 1.18) / (totals.totalSpend * 1.18)) * 100
        : totals.totalRevenue > 0
        ? 10000
        : totals.totalRevenue < 0
        ? -10000
        : 0;

      setGrandTotals(totals);
      setIsDateRangeSelected(true);
    } catch (error) {
      console.error("Error in processDataWithSort:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.error || "Failed to process campaigns data",
        timer: 3000,
        showConfirmButton: false,
      });
      setCampaignsData([]);
      setGrandTotals({});
      setTableHeaders([]);
    } finally {
      setIsDetailsLoading(false);
      setShowLoader(false);
    }
  };
  const processData = () => {
    setTotalCurrency(showInUSD);
    processDataWithSort(orderBy, orderDir);
  };

  const handleSort = (column) => {
    const newOrderBy = column;
    const newOrderDir = orderBy === column && orderDir === "asc" ? "desc" : "asc";

    if (isDateRangeSelected && startDate && endDate) {
      processDataWithSort(newOrderBy, newOrderDir);
    }

    setOrderBy(newOrderBy);
    setOrderDir(newOrderDir);
  };

  const getGroupByValue = () => {
    if (selectedBreakPoints.length === breakPointOptions.length) {
      return "all";
    }
    
    if (selectedBreakPoints.length === 0) {
      return "";
    }
  
    // Define the strict backend order
    const backendOrder = ['date', 'country', 'article', 'channel', 'styleId'];
    
    // Sort selected breakpoints according to backend order
    const sortedBreakPoints = selectedBreakPoints.sort((a, b) => {
      return backendOrder.indexOf(a) - backendOrder.indexOf(b);
    });
  
    return sortedBreakPoints.join(",");
  };
  
  const fetchChannelOptions = useCallback(async () => {
    try {
      const response = await apiClient.get("/Channals/GetDropdown");
      if (response?.status === 200) {
        const options = response.data.data.map((channel) => ({
          value: channel.Id,
          label: channel.DisplayName,
        }));
        setChannelOptions(options);
      }
    } catch (error) {
      console.error(
        "Error fetching channels:",
        error?.response?.data?.error || "Internal server error"
      );
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load channel options",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchArticleOptions = useCallback(async () => {
    try {
      const response = await apiClient.get("/article/GetDropdown");
      if (response?.status === 200) {
        const options = response.data.data.map((article) => ({
          value: article.Id,
          label: article.Title,
        }));
        setArticleOptions(options);
      }
    } catch (error) {
      console.error(
        "Error fetching articles:",
        error?.response?.data?.error || "Internal server error"
      );
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load article options",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchCampaignDetails = async (articleId, row, index) => {
    const key = generateRowKey(row, selectedBreakPoints, index);
    setLoadingDetails((prev) => ({ ...prev, [key]: true }));
  
    try {
      const formattedStartDate = formatDateForAPI(startDate);
      const formattedEndDate = formatDateForAPI(endDate);
      const groupBy = getGroupByValue();
  
      const response = await apiClient.post(
        "/RCAnalytics/Get",
        {
          articleId,
          channelId: selectedChannelIds.join(",") || row.channelid || "",
          styleId: selectedStyleIds.join(",") || row.styleid || "",
          groupBy,
          domainId: selectedDomainId || "",
          subDomainId: selectedSubdomainFilter?.Id || "",
          orderBy,
          orderDir,
        },
        {
          params: {
            startDate: formattedStartDate,
            endDate: formattedEndDate,
            userId: selectedPartner || "",
            showNoRevenue,
            InrtoUsd: showInUSD,
            showGSTValue: showGSTColumns,
          },
        }
      );      
  
      let details = response.data.data?.map((item, detailIndex) => {
        const detailKey = generateRowKey(item, selectedBreakPoints, detailIndex);
  
        const cells = tableHeaders.map((header) => {
          if (header.name === "EXPAND") return { value: "" };
          const fieldName = fieldMap[header.name];
          let value;
          
          if (header.name === "DATE") {
            value = formatDateForDisplay(item[fieldName]);
          } else if (header.name === "ARTICLENAME") {
            value = item.articlename || "-";
          } else if (header.name === "CHANNELNAME") {
            value = item.channelname || "-";
          } else if (header.name === "COUNTRY") {
            value = item.country || "-";
          } else if (header.type === "METRIC") {
            const numericValue = parseFloat(item[fieldName]);
            if (!isNaN(numericValue)) {
              if (header.currencyCode) {
                // Use totalCurrency instead of showInUSD for consistency
                value = `${getCurrency(totalCurrency ? "USD" : "INR")}${numericValue.toFixed(2)}`;
              } else if (header.isPercentage) {
                value = `${numericValue.toFixed(2)}%`;
              } else {
                value = numericValue.toLocaleString();
              }
            } else {
              value = "-";
            }
          }
          return { value };
        });
  
        return {
          id: detailKey,
          cells,
          original: item,
          headers: tableHeaders,
        };
      }) || [];
  
      // Rest of your sorting logic...
      if (selectedBreakPoints.includes("date") && orderBy === "ads_spend") {
        details.sort((a, b) => {
          const dateA = new Date(a.original.date);
          const dateB = new Date(b.original.date);
          if (dateA !== dateB) return dateB - dateA;
          const spendA = parseFloat(a.original.ads_spend) || 0;
          const spendB = parseFloat(b.original.ads_spend) || 0;
          return spendB - spendA;
        });
      }
  
      if (details.length > 0) {
        setCampaignDetails((prev) => ({
          ...prev,
          [key]: details,
        }));
      }
    } catch (error) {
      console.error("Error fetching campaign details:", error);
      setCampaignDetails((prev) => ({ ...prev, [key]: [] }));
    } finally {
      setLoadingDetails((prev) => ({ ...prev, [key]: false }));
    }
  };
  const toggleRowExpansion = (articleId, row, index) => {
    // Don't allow expansion if no breakpoints are selected
    if (selectedBreakPoints.length === 0) return;
    const key = generateRowKey(row, selectedBreakPoints, index);
    
    if (expandedRows[key]) {
      setExpandedRows((prev) => ({ ...prev, [key]: false }));
    } else {
      setExpandedRows((prev) => ({ ...prev, [key]: true }));
      if (!campaignDetails[key]) {
        fetchCampaignDetails(articleId, row, index);
      }
    }
  };

  const columnNameMap = {
    ARTICLENAME: "Article Name",
    DATE: "Date",
    CHANNELNAME: "Channel Name",
    STYLENAME: "Style Name",
    COUNTRY: "Country",
    RV_REVENUE: "Revenue",
    RV_RPC: "RPC",
    PROFIT: "Profit",
    PROFIT_GST: "Profit+GST",
    ROI: "ROI",
    RV_RPM: "RPM",
    RV_IMPRESSIONS: "Impressions",
    ROI_GST: "ROI+GST",
    ADS_CONVERSIONS: "Conversions",
    ADS_CLICK: "Ad Clicks",
    ADS_SPEND: "Spend",
    ADS_SPEND_WITH_GST: "Spend+GST",
    ADS_CPA: "CPC",
    ADS_CPC: "Avg.CPC",
  };
  const filteredCampaignsData = filterDataBySearch(campaignsData, searchTerm);
  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken");
    setToken(accessToken);
    if (accessToken) {
      setUserData(decodeJWT(accessToken));
    }
  }, []);

  useEffect(() => {
    if (["Admin", "Super Admin"].includes(userData?.User_Type)) {
      fetchPartnerUsers();
    }
  }, [userData?.User_Type, fetchPartnerUsers]);

  useEffect(() => {
    setSelectedDomainId("");
    setSelectedSubdomainFilter({ Id: "", Name: "All Subdomains" });
    setSubdomainOptions([{ Id: "", Name: "All Subdomains" }]);
    fetchChannelOptions();
    fetchArticleOptions();
    fetchDomains();
  }, [fetchChannelOptions, fetchArticleOptions, fetchDomains]);
  useEffect(() => {
    fetchStyleIds();
  }, []);
  useEffect(() => {
    fetchSubdomains(selectedDomainId);
  }, [selectedDomainId, fetchSubdomains]);

  return (
    <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
      <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-white">
            ROI Analytics
          </h1>
        </div>
      </div>

      <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
      <div className="mb-6 flex flex-col gap-4">
          <div className="flex flex-col gap-4">
            {/* Main filters row */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 2xl:grid-cols-7 items-end">
            
              {/* Show No Revenue Toggle */}
              <div className="flex flex-col gap-1 space-y-2">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Show No Revenue</h3>
                <div className="flex items-center gap-2">
                  <label className="relative inline-flex cursor-pointer items-center">
                    <input
                      type="checkbox"
                      checked={showNoRevenue}
                      onChange={() => setShowNoRevenue(!showNoRevenue)}
                      className="peer sr-only"
                    />
                    <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-primary/25"></div>
                  </label>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {showNoRevenue ? "On" : "Off"}
                  </span>
                </div>
              </div>

              <div className="flex flex-col gap-1 space-y-2">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Show GST Columns</h3>
                <Checkbox
                  label={showGSTColumns ? "Yes" : "No"}
                  name="showGSTColumns"
                  checked={showGSTColumns}
                  onChange={() => setShowGSTColumns(!showGSTColumns)}
                  withIcon="check"
                  withBg
                  radius="default"
                />
              </div>

              {/* Currency Toggle */}
              <div className="flex flex-col gap-1 space-y-2">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Currency</h3>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">INR</span>
                  <label className="relative inline-flex cursor-pointer items-center">
                    <input
                      type="checkbox"
                      checked={showInUSD}
                      onChange={() => setShowInUSD(!showInUSD)}
                      className="peer sr-only"
                    />
                    <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-primary/25"></div>
                  </label>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">USD</span>
                </div>
              </div>

              {/* Breakpoints Dropdown */}
              <div className="min-w-0 z-50">
                <MultiSelectDropdown
                  label="Breakpoints"
                  placeholder="Select Breakpoints"
                  options={breakPointOptions}
                  value={selectedBreakPoints}
                  onChange={setSelectedBreakPoints}
                  displayKey="label"
                  idKey="value"
                  className="w-full"
                />
              </div>

              

              {/* Date Range Picker */}
              <div className="min-w-0 z-30">
                <DateRangePicker
                  startDate={startDate}
                  endDate={endDate}
                  onDateChange={(selectedDates) => {
                    if (selectedDates.length === 2) {
                      setStartDate(selectedDates[0]);
                      setEndDate(selectedDates[1]);
                    }
                  }}
                  onQuickDateSelect={handleQuickDateSelect}
                  label="Date Range"
                  placeholder="Select date range"
                  showQuickSelectButtons={true}
                  className="w-full"
                />
              </div>
              <div className="min-w-0">
                <InputGroup
                  label="Search"
                  placeholder="Search articles, channels, styles..."
                  value={searchTerm}
                  handleChange={(e) => setSearchTerm(e.target.value)}
                  type="text"
                  className="w-full"
                />
              </div>
              {/* Filter & Apply Buttons (Hidden on mobile) */}
              <div className="hidden 2xl:flex items-center gap-2">
                <button
                  onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                  className="flex h-[40px] w-[40px] items-center justify-center rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                  title={isFilterExpanded ? "Hide Filters" : "More Filters"}
                >
                  <FaFilter size={16} />
                </button>
                <Button
                  label="Apply"
                  variant="primary"
                  shape="rounded"
                  className="flex items-center w-[100%] justify-center gap-2 rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 disabled:opacity-50"
                  onClick={processData}
                  disabled={showLoader}
                />
              </div>
            </div>

            {/* Expanded Filters (for Domain, Subdomain, Channels) */}
            {isFilterExpanded && (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-4 2xl:grid-cols-6 items-end">
                {/* Articles Dropdown */}
                <div className="min-w-0 z-40">
                  <MultiSelectDropdown
                    label="Articles"
                    placeholder="Select Articles"
                    options={articleOptions}
                    value={selectedArticleIds}
                    onChange={setSelectedArticleIds}
                    displayKey="label"
                    idKey="value"
                    className="w-full"
                  />
                </div>
                {/* Domain Dropdown */}
                <div className="min-w-0 z-30">
                  <SearchableDropdown
                    label="Domain"
                    placeholder="Select Domain"
                    options={[{ Id: "", Name: "All Domains" }, ...domains]}
                    value={selectedDomainId}
                    onChange={(item) => setSelectedDomainId(item?.Id || "")}
                    className="w-full"
                    displayKey="Name"
                    displayKey2="ShowUrlName"
                    idKey="Id"
                  />
                </div>

                {/* Subdomain Dropdown */}
                <div className="min-w-0 z-20">
                  <SearchableDropdown
                    label="Subdomain"
                    placeholder={selectedDomainId ? "Select Subdomain" : "Select a domain first"}
                    options={subdomainOptions}
                    value={selectedSubdomainFilter.Id || ""}
                    onChange={(item) => setSelectedSubdomainFilter(item || { Id: "", Name: "All Subdomains" })}
                    displayKey="Name"
                    idKey="Id"
                    disabled={!selectedDomainId}
                    className="w-full"
                  />
                </div>
                {["Admin", "Super Admin"].includes(userData?.User_Type) && (
                <div className="min-w-0 z-9">
                  <SearchableDropdown
                    label="All Users"
                    placeholder="Select Users..."
                    options={[{ Id: "", Name: "All Users" }, ...partnerUsers]}
                    value={selectedPartner}
                    onChange={(partner) => {
                      setSelectedPartner(partner?.Id || "");
                    }}
                    displayKey="Name"
                    idKey="Id"
                    className="w-full"
                  />
                </div>
              )}
                <div className="min-w-0 z-1">
                  <MultiSelectDropdown
                    label="Channels"
                    placeholder="Select Channels"
                    options={channelOptions}
                    value={selectedChannelIds}
                    onChange={setSelectedChannelIds}
                    displayKey="label"
                    idKey="value"
                    className="w-full"
                  />
                </div>

                <div className="min-w-0 z-0">
                  <MultiSelectDropdown
                    label="Style IDs"
                    placeholder="Select Style IDs"
                    options={styleOptions}
                    value={selectedStyleIds}
                    onChange={setSelectedStyleIds}
                    displayKey="label"
                    idKey="value"
                    className="w-full"
                  />
                </div>
              </div>
            )}

            {/* Mobile Filter & Apply Buttons */}
            <div className="flex justify-end items-center gap-2 2xl:hidden">
              <button
                onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                className="flex h-[40px] w-[40px] items-center justify-center rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                title={isFilterExpanded ? "Hide Filters" : "More Filters"}
              >
                <FaFilter size={16} />
              </button>
              <Button
                label="Apply"
                variant="primary"
                shape="rounded"
                className="flex items-center w-[100%] justify-center gap-2 rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 disabled:opacity-50 sm:w-[100px]"
                onClick={processData}
                disabled={showLoader}
              />
            </div>
          </div>
        </div>

        {Object.keys(grandTotals).length > 0 && (
          <div className="mt-6 grid gap-5 rounded-lg bg-gray-100 p-4 dark:bg-dark-2 grid-cols-2 md:grid-cols-4 2xl:grid-cols-5">
            <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Date Range
              </span>
              <span className="text-base font-semibold text-dark dark:text-white">
                {formatDate(startDate)} - {formatDate(endDate)}
              </span>
            </div>
            <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Spend {appliedGSTColumns  && "/ +GST" }
              </span>
              <span className={`rounded p-1 text-base font-semibold text-dark dark:text-white`}>
                <span className={`rounded p-1 text-base ${getCellBackgroundColor(
                  grandTotals.totalSpend
                )} font-semibold text-dark dark:text-white`}>
                {getCurrency(totalCurrency ? "USD" : "INR")}
                {grandTotals.totalSpend?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) || "0.00"}
                </span>{appliedGSTColumns && "/"}{appliedGSTColumns && (
                  <span
                    className={`rounded p-1 text-base ${getCellBackgroundColor(
                      grandTotals.totalSpendWithGst
                    )} font-semibold text-dark dark:text-white`}
                  >
                    {getCurrency(totalCurrency ? "USD" : "INR")}
                    {grandTotals.totalSpendWithGst?.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }) || "0.00"}
                  </span>
                )}
              </span>
            </div>
            <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Revenue
              </span>
              <span className={`rounded p-1 text-base font-semibold ${getCellBackgroundColor(
                  grandTotals.totalRevenue
                )} text-dark dark:text-white`}>
                {getCurrency(totalCurrency ? "USD" : "INR")}
                {grandTotals.totalRevenue?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) || "0.00"}
              </span>
            </div>
            <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                [Profit / Loss] /  ROI
              </span>
              <span className={`text-base font-semibold text-dark dark:text-white rounded p-1`}>
                <span className={`text-base font-semibold text-dark dark:text-white ${getCellBackgroundColor(
                  grandTotals.totalProfit
                )} rounded p-1`}>
                {getCurrency(totalCurrency ? "USD" : "INR")}
                {grandTotals.totalProfit?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) || "0.00"}
                </span> / 
                <span className={`text-base font-semibold text-dark dark:text-white ${getCellBackgroundColor(
                  grandTotals.roi
                )} rounded p-1`}>
                  {grandTotals.roi?.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) || "0.00"}% 
                </span>
              </span>
            </div>
            {appliedGSTColumns && (
              <div className="flex flex-col items-center justify-center border-r border-gray-300 dark:border-dark-3 md:items-start">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  [Profit / Loss] + GST / ROI + GST
                </span>
                <span className="text-base font-semibold text-dark dark:text-white rounded p-1">
                  <span
                    className={`text-base font-semibold text-dark dark:text-white ${getCellBackgroundColor(
                      grandTotals.totalProfitGst
                    )} rounded p-1`}
                  >
                    {getCurrency(totalCurrency ? "USD" : "INR")}
                    {grandTotals.totalProfitGst?.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }) || "0.00"}
                  </span>{" "}
                  /{" "}
                  <span
                    className={`text-base font-semibold text-dark dark:text-white ${getCellBackgroundColor(
                      grandTotals.roiGst
                    )} rounded p-1`}
                  >
                    {grandTotals.roiGst?.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }) || "0.00"}
                    %
                  </span>
                </span>
              </div>
            )}
          </div>
        )}
        {cronJobInfo && Object.keys(cronJobInfo).length > 0 && (
          <div className="mt-2 px-4 py-2 overflow-x-auto rounded-lg font-bold text-sm">
            <marquee behavior="scroll" direction="left">
              Last Updated : Spending Data : {cronJobInfo["Spending Data"]} | Revenue Data : {cronJobInfo["Revenue Data"]} 
            </marquee>
          </div>
        )}
        <div className="mt-2 overflow-x-auto rounded-lg mb-4 border border-gray-200 shadow-sm">
          {showLoader && campaignsData.length === 0 ? (
            <div className="flex h-64 items-center justify-center">
              <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <TableContainer>
              <Table className="min-w-full divide-y divide-gray-200">
                <TableHead>
                <TableRow>
                  {tableHeaders.map((header, index) => (
                    // Only render the header if it's not EXPAND or if there are breakpoints selected
                    (header.name !== "EXPAND" || selectedBreakPoints.length > 0) && (
                      <TableCell
                        key={index}
                        sx={{
                          backgroundColor: "#635de7",
                          color: "white",
                          fontWeight: 700,
                          fontSize: "0.75rem",
                          textTransform: "uppercase",
                          padding: "12px 24px",
                        }}
                      >
                        {header.name !== "EXPAND" ? (
                          <TableSortLabel
                            active={orderBy === fieldMap[header.name]}
                            direction={
                              orderBy === fieldMap[header.name] ? orderDir : "asc"
                            }
                            onClick={() => handleSort(fieldMap[header.name])}
                          >
                            {columnNameMap[header.name] ||
                              header.name.replace(/_/g, " ")}
                            {orderBy === fieldMap[header.name] ? (
                              <Box component="span" sx={visuallyHidden}>
                                {orderDir === "desc"
                                  ? "sorted descending"
                                  : "sorted ascending"}
                              </Box>
                            ) : null}
                          </TableSortLabel>
                        ) : (
                          columnNameMap[header.name] ||
                          header.name.replace(/_/g, " ")
                        )}
                      </TableCell>
                    )
                  ))}
                </TableRow>
                </TableHead>
                <TableBody className="divide-y divide-gray-200 bg-white">
                {isDetailsLoading ? (
                  <TableRow>
                    <TableCell colSpan={tableHeaders.length} align="center">
                      <div className="flex items-center justify-center py-8">
                        <CircularProgress size={40} />
                        <span className="ml-3">Loading campaign data...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : isDateRangeSelected && campaignsData.length > 0 ? (
                    filterDataBySearch(campaignsData, searchTerm).map((row, index) => {
                      // const key = row.id;
                      const key = generateRowKey(row.original, selectedBreakPoints, index);
                      return (
                        <React.Fragment key={key}>
                          <TableRow
                            className={`transition-colors hover:bg-blue-50 ${
                              index % 2 === 0 ? "bg-gray-50" : "bg-white"
                            }`}
                          >
                            {row.cells.map((cell, cellIndex) => (
                              (tableHeaders[cellIndex].name !== "EXPAND" || selectedBreakPoints.length > 0) && (
                                <TableCell
                                  key={cellIndex}
                                  className={`whitespace-nowrap px-6 py-4 ${
                                    (tableHeaders[cellIndex].name === "PROFIT" ||
                                     tableHeaders[cellIndex].name === "PROFIT_GST" ||  // NEW
                                     tableHeaders[cellIndex].name === "ROI" ||
                                     tableHeaders[cellIndex].name === "ROI_GST") &&    // NEW
                                    cell.value !== '-' && 
                                    cell.value !== '0.00%'
                                      ? getCellBackgroundColor(
                                          tableHeaders[cellIndex].name === "ROI" || tableHeaders[cellIndex].name === "ROI_GST"
                                            ? parseFloat(cell.value.replace('%', ''))
                                            : parseFloat(cell.value.replace(/[^0-9.-]/g, ''))
                                        )
                                      : ""
                                  }`}
                                >
                                  {tableHeaders[cellIndex].name === "EXPAND" ? (
                                    <button
                                      onClick={() =>
                                        toggleRowExpansion(
                                          row.original.articleid,
                                          row.original,
                                          index
                                        )
                                      }
                                      className="text-gray-600 hover:text-blue-600 focus:outline-none"
                                    >
                                      {expandedRows[key] ? (
                                        <FaChevronDown className="h-4 w-4" />
                                      ) : (
                                        <FaChevronRight className="h-4 w-4" />
                                      )}
                                    </button>
                                  ) : tableHeaders[cellIndex].name === "ARTICLENAME" ? (
                                    <button
                                      onClick={() =>
                                        handleEditArticle(row.original.articleid)
                                      }
                                      className="text-sm text-blue-600 hover:underline focus:outline-none"
                                      disabled={cell.value === "-"}
                                    >
                                      {cell.value}
                                    </button>
                                  ) : (
                                    <div className="text-sm text-gray-900">
                                      {cell.value}
                                    </div>
                                  )}
                                </TableCell>
                              )
                            ))}
                          </TableRow>
                          {expandedRows[key] &&
                            (loadingDetails[key] ? (
                              <TableRow className="bg-gray-50">
                                <TableCell
                                  colSpan={tableHeaders.length}
                                  className="px-6 py-4 text-center text-sm text-gray-900"
                                >
                                  <div className="flex items-center justify-center">
                                    <div className="mr-3 h-5 w-5 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                                    <span>Loading details...</span>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ) : campaignDetails[key]?.length === 0 ? (
                              <TableRow className="bg-gray-50">
                                <TableCell
                                  colSpan={tableHeaders.length}
                                  className="px-6 py-4 text-center text-sm text-gray-900"
                                >
                                  No details available
                                </TableCell>
                              </TableRow>
                            ) : (
                              campaignDetails[key]?.map((detail, idx) => (
                                <TableRow
                                  key={`${key}-${idx}`}
                                  className="bg-gray-50"
                                >
                                  {detail.cells.map((cell, cellIndex) => (
                                    <TableCell
                                      key={cellIndex}
                                      className={`whitespace-nowrap px-6 py-4 ${
                                        (tableHeaders[cellIndex].name === "PROFIT" || 
                                          tableHeaders[cellIndex].name === "PROFIT_GST" ||
                                          tableHeaders[cellIndex].name === "ROI" ||
                                          tableHeaders[cellIndex].name === "ROI_GST") &&
                                        cell.value !== '-' && 
                                        cell.value !== '0.00%'
                                          ? getCellBackgroundColor(
                                              tableHeaders[cellIndex].name === "ROI" || tableHeaders[cellIndex].name === "ROI_GST"
                                                ? parseFloat(cell.value.replace('%', ''))
                                                : parseFloat(cell.value.replace(/[^0-9.-]/g, ''))
                                            )
                                          : ""
                                      } ${
                                        tableHeaders[cellIndex].name === "EXPAND" ||
                                        tableHeaders[cellIndex].name === "ARTICLENAME"
                                          ? ""
                                          : ""
                                      }`}
                                    >
                                      <div className="text-sm text-gray-900">
                                        {tableHeaders[cellIndex].name === "ARTICLENAME"
                                          ? ""
                                          : cell.value}
                                      </div>
                                    </TableCell>
                                  ))}
                                </TableRow>
                              ))
                            ))}
                        </React.Fragment>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={tableHeaders.length}
                        className="px-6 py-4 text-center text-sm text-gray-500"
                      >
                        {showLoader
                          ? "Loading..."
                          : "Please select a date range and apply filters."}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </div>

        {modalShow && Object.keys(formData).length > 0 && (
          <ArticleModal
            open={modalShow}
            onClose={() => {
              setModalShow(false);
              resetFormState();
            }}
            mode="edit"
            formData={validateFormData(formData)}
            setFormData={setFormData}
            description={description}
            setDescription={setDescription}
            shortDescription={shortDescription}
            setShortDescription={setShortDescription}
            formdataImage={formdataImage}
            setFormdataImage={setFormdataImage}
            base64Image={base64Image}
            setBase64Image={setBase64Image}
            published={published}
            setPublished={setPublished}
            showArticle={showArticle}
            setShowArticle={setShowArticle}
            showAds={showAds}
            setShowAds={setShowAds}
            selectedDomain={selectedDomain}
            setSelectedDomain={setSelectedDomain}
            selectedSubdomain={selectedSubdomain}
            setSelectedSubdomain={setSelectedSubdomain}
            selectedCampaignIds={selectedCampaignIds}
            setSelectedCampaignIds={setSelectedCampaignIds}
            domains={domains}
            subdomains={subdomains}
            categories={categories}
            assignChannels={assignChannels}
            styleIds={styleIds}
            campaigns={campaigns}
            editId={editId}
            editSlugMode={editSlugMode}
            setEditSlugMode={setEditSlugMode}
            handleFormSubmit={handleFormSubmit}
            handleChange={handleChange}
            handleChannelSelect={handleChannelSelect}
            handleCampaignSelect={handleCampaignSelect}
            validateField={validateField}
            autoslug={autoslug}
            showLoader={showLoader}
          />
        )}
      </div>
    </div>
  );
};

const fetchImage = (type, url) => {
  // Placeholder for fetchImage function, implement as needed
  return url;
};

export default RevenueCampaignsManagement;