import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { getCustomChannels } from '@/utils/functions';
import { google } from 'googleapis';

interface CustomChannel {
    name?: string | null;
    displayName?: string | null;
    reportingDimensionId?: string | null;
}

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const code = searchParams.get('code');
        const customChannelsTrue = searchParams.get("customChannels");

        const userSettings = await prisma.adminUserSetting.findFirst({});
        if (!userSettings?.RevenueClientId || !userSettings?.RevenueClientSecret) {
            return NextResponse.json(
                { message: 'OAuth credentials not found in database' },
                { status: 404 }
            );
        }

        const oauth2Client = new google.auth.OAuth2(
            userSettings.RevenueClientId,
            userSettings.RevenueClientSecret,
            userSettings.RevenueRedirectUrl as string
        );

        const authUrl = oauth2Client.generateAuthUrl({
            access_type: 'offline',
            scope: [
                'https://www.googleapis.com/auth/adsense.readonly',
                'https://www.googleapis.com/auth/adsense',
            ],
            prompt: 'consent',
            include_granted_scopes: true,
        });

        let tokens = null;
        if (userSettings.RevenueAccessToken && userSettings.RevenueRefreshToken) {
            tokens = {
                access_token: userSettings.RevenueAccessToken,
                refresh_token: userSettings.RevenueRefreshToken,
            };
        }

        if (!code && !tokens) {
            return NextResponse.redirect(authUrl);
        }

        if (code) {
            try {
                const { tokens: newTokens } = await oauth2Client.getToken(code);
                oauth2Client.setCredentials(newTokens);
                tokens = newTokens;

                await prisma.adminUserSetting.updateMany({
                    data: {
                        RevenueAccessToken: newTokens.access_token,
                        RevenueRefreshToken: newTokens.refresh_token,
                    },
                });

                return NextResponse.json(
                    { message: 'Authorization successful', tokens: newTokens },
                    { status: 200 }
                );
            } catch (error: unknown) {
                console.error('Error during token exchange:', error);
                const errorMessage = error instanceof Error ? error.message : 'Unknown error during token exchange';
                return NextResponse.json(
                    { error: 'Failed to exchange authorization code for tokens', details: errorMessage },
                    { status: 500 }
                );
            }
        }

        if (tokens) {
            oauth2Client.setCredentials(tokens);

            try {
                const adsense = google.adsense({ version: 'v2', auth: oauth2Client });
                await adsense.accounts.list({ pageSize: 1 });
            } catch (error: unknown) {
                const isAuthError =
                    error instanceof Error &&
                    (error.message.includes('invalid_token') ||
                        (typeof (error as any).code === 'number' && (error as any).code === 401));

                if (isAuthError) {
                    try {
                        const { credentials } = await oauth2Client.refreshAccessToken();
                        tokens = {
                            access_token: credentials.access_token,
                            refresh_token: credentials.refresh_token || userSettings.RevenueRefreshToken,
                        };

                        await prisma.adminUserSetting.updateMany({
                            data: {
                                RevenueAccessToken: credentials.access_token,
                                RevenueRefreshToken: credentials.refresh_token || userSettings.RevenueRefreshToken,
                            },
                        });

                        oauth2Client.setCredentials(tokens);
                    } catch (refreshError: unknown) {
                        console.error('Token refresh failed:', refreshError);
                        return NextResponse.redirect(authUrl);
                    }
                } else {
                    console.error('Unexpected API error:', error);
                    throw error;
                }
            }
        }

        if (!userSettings?.AdsAccountId || !userSettings?.AdsClientId) {
            return NextResponse.json(
                { message: 'AdSense account ID or client ID not found' },
                { status: 404 }
            );
        }

        const exampleAdClientId = `${userSettings.AdsAccountId}/${userSettings.AdsClientId}`;

        if (customChannelsTrue === "true") {
            const customChannels = await getCustomChannels(oauth2Client, exampleAdClientId, google);

            if (!customChannels || customChannels.length === 0) {
                return NextResponse.json({ message: 'No custom channels found' }, { status: 404 });
            }

            // First, get all existing channels from the database
            const allExistingChannels = await prisma.channals.findMany();

            // Create a Set of channel names returned from the API for quick lookup
            const apiChannelNames = new Set(
                customChannels
                    .map((channel: CustomChannel) => channel.name || '')
                    .filter((name: string) => name !== '')
            );

            // Process channels returned from API
            for (const channel of customChannels) {
                const customChannel = channel as CustomChannel;
                const name = customChannel.name || '';
                const displayName = customChannel.displayName || undefined;
                const reportingDimensionId = customChannel.reportingDimensionId || undefined;

                if (!name) continue;

                // Find if this channel exists in our database by name
                const existingChannel = allExistingChannels.find(dbChannel => dbChannel.Name === name);

                if (existingChannel) {
                    // Update the channel with API data
                    await prisma.channals.update({
                        where: {
                            Name: name
                        },
                        data: {
                            DisplayName: displayName,
                            ReportingDimensionId: reportingDimensionId,
                            Active: true,
                            UpdatedAt: new Date(),
                        },
                    });
                } else {
                    // Create a new channel if it doesn't exist
                    await prisma.channals.create({
                        data: {
                            Name: name,
                            DisplayName: displayName,
                            ReportingDimensionId: reportingDimensionId,
                            Active: true,
                            CreatedAt: new Date(),
                        },
                    });
                }
            }

            for (const dbChannel of allExistingChannels) {
                if (!apiChannelNames.has(dbChannel.Name)) {
                    const newDisplayName = dbChannel.DisplayName
                        ? dbChannel.DisplayName.startsWith("arc_")
                            ? dbChannel.DisplayName 
                            : `arc_${dbChannel.DisplayName}`
                        : "arc_unknown";

                    await prisma.channals.update({
                        where: {
                            Name: dbChannel.Name as string
                        },
                        data: {
                            DisplayName: newDisplayName,
                            Active: false,
                            UpdatedAt: new Date(),
                        },
                    });
                }
            }


            return NextResponse.json({ message: 'Channels synced successfully' });
        }

        return NextResponse.json({ message: 'Custom channels sync not requested' });

    } catch (error: unknown) {
        console.error('[ERROR] Failed to sync channels:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return NextResponse.json(
            {
                error: 'Failed to sync channels',
                details: errorMessage,
                stack: process.env.NODE_ENV === 'development' ? error : undefined,
            },
            { status: 500 }
        );
    }
}