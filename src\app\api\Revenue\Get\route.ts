// import { prisma } from '@/lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
// import { google } from "googleapis";
// import {
//     formatDate,
//     generateReport,
//     getAdClients,
//     getAdSenseAccounts,
// } from '@/utils/functions';

// interface CountResult {
//     total_count: bigint;
// }

// interface RevenueResult {
//     date?: string;
//     country?: string;
//     article?: string;
//     channel?: string;
//     styleid?: string;
//     estimated_earnings: string | null;
//     impressions: string | null;
//     rpm: string | null;
//     clicks: string | null;
//     impressionsctr: string | null;
//     cpc: string | null;
// }

// type OrderableField =
//     | 'date'
//     | 'country'
//     | 'article'
//     | 'channel'
//     | 'styleid'
//     | 'estimated_earnings'
//     | 'impressions'
//     | 'rpm'
//     | 'clicks'
//     | 'impressionsctr'
//     | 'cpc';

// type NumericField =
//     | 'estimated_earnings'
//     | 'impressions'
//     | 'rpm'
//     | 'clicks'
//     | 'impressionsctr'
//     | 'cpc';

// const ORDERABLE_FIELDS: Record<string, OrderableField> = {
//     DATE: 'date',
//     COUNTRY: 'country',
//     ARTICLE: 'article',
//     CHANNEL: 'channel',
//     STYLE_ID: 'styleid',
//     EARNINGS: 'estimated_earnings',
//     IMPRESSIONS: 'impressions',
//     RPM: 'rpm',
//     CLICKS: 'clicks',
//     CTR: 'impressionsctr',
//     CPC: 'cpc'
// };

// const NUMERIC_FIELDS: NumericField[] = [
//     'estimated_earnings',
//     'impressions',
//     'rpm',
//     'clicks',
//     'impressionsctr',
//     'cpc'
// ];

// const BATCH_SIZE = 200;
// const MAX_CONCURRENT_BATCHES = 2;

// function batchArray<T>(array: T[], batchSize: number): T[][] {
//     const batches: T[][] = [];
//     for (let i = 0; i < array.length; i += batchSize) {
//         batches.push(array.slice(i, i + batchSize));
//     }
//     return batches;
// }

// async function getChannelDisplayName(reportingDimensionId: string): Promise<string> {
//     const numericPart = reportingDimensionId.includes(':')
//         ? reportingDimensionId.split(':')[1]
//         : reportingDimensionId;

//     const channel = await prisma.channals.findFirst({
//         where: {
//             ReportingDimensionId: reportingDimensionId
//         },
//         select: {
//             DisplayName: true
//         }
//     });
//     return channel?.DisplayName ? `${channel.DisplayName}[${numericPart}]` : numericPart;
// }

// async function getStyleName(styleId: string): Promise<string> {
//     const style = await prisma.styleIds.findFirst({
//         where: {
//             StyleId: styleId
//         },
//         select: {
//             Name: true
//         }
//     });
//     return style?.Name ? `${style.Name}[${styleId}]` : styleId;
// }

// async function getCountryName(countryCode: string): Promise<string> {
//     if (!countryCode) return '';

//     const country = await prisma.ads_ISO_CountryMaster.findFirst({
//         where: {
//             CountryCode: countryCode
//         },
//         select: {
//             Name: true
//         }
//     });

//     return country?.Name ? `${country.Name}` : countryCode;
// }

// async function getArticleDisplayName(articleId: string): Promise<string> {
//     const article = await prisma.articleDetails.findFirst({
//         where: {
//             Id: articleId
//         },
//         select: {
//             Title: true,
//             Url: true
//         }
//     });

//     if (article?.Title) {
//         return `${article.Title}`;
//     } else if (article?.Url) {
//         return `${article.Url}`;
//     }
//     return articleId;
// }

// function isOrderableField(field: string): field is OrderableField {
//     return Object.values(ORDERABLE_FIELDS).includes(field as OrderableField);
// }

// function isNumericField(field: string): field is NumericField {
//     return NUMERIC_FIELDS.includes(field as NumericField);
// }

// function getSafeOrderField(field: string): OrderableField {
//     return isOrderableField(field) ? field : 'estimated_earnings';
// }

// function sanitizeStringForSQL(value: string): string {
//     return value.replace(/'/g, "''");
// }

// function convertBreakpointsToString(breakpointsObj: any): string {
//     const mapping: Record<string, string> = {
//         date: 'date',
//         country: 'country',
//         article: 'article',
//         customChannel: 'channel',
//         styleId: 'styleId'
//     };

//     const activeBreakpoints: string[] = [];

//     for (const [key, value] of Object.entries(breakpointsObj)) {
//         if (value === true && mapping[key]) {
//             activeBreakpoints.push(mapping[key]);
//         }
//     }

//     return activeBreakpoints.join(',');
// }

// function convertBreakpointsToObject(breakpointsStr: string): any {
//     const mapping: Record<string, string> = {
//         date: 'date',
//         country: 'country',
//         article: 'article',
//         channel: 'customChannel',
//         styleId: 'styleId'
//     };

//     const breakpointsObj: any = {};
//     const breakpointArray = breakpointsStr.split(',').filter(bp => bp.trim());

//     for (const bp of breakpointArray) {
//         const trimmedBp = bp.trim();
//         if (mapping[trimmedBp]) {
//             breakpointsObj[mapping[trimmedBp]] = true;
//         }
//     }

//     return breakpointsObj;
// }

// function deduplicateAndMergeReportData(data: any[], breakpoints: string): any[] {
//     const activeBreakpoints = breakpoints === 'all' || breakpoints === ''
//         ? ['date', 'country', 'article', 'channel', 'styleId']
//         : breakpoints.split(',').filter(bp => bp.trim());

//     const dataMap = new Map();

//     data.forEach(item => {
//         const keyParts: string[] = [];

//         if (activeBreakpoints.includes('date') && item.date) {
//             keyParts.push(`date:${item.date}`);
//         }
//         if (activeBreakpoints.includes('country') && item.country) {
//             keyParts.push(`country:${item.country}`);
//         }
//         if (activeBreakpoints.includes('article') && item.article) {
//             keyParts.push(`article:${item.article}`);
//         }
//         if (activeBreakpoints.includes('channel') && item.channel) {
//             keyParts.push(`channel:${item.channel}`);
//         }
//         if (activeBreakpoints.includes('styleId') && item.styleid) {
//             keyParts.push(`style:${item.styleid}`);
//         }

//         const key = keyParts.join('|') || 'aggregate';

//         if (dataMap.has(key)) {
//             const existing = dataMap.get(key);
//             const merged = mergeRevenueRecords(existing, item);
//             dataMap.set(key, merged);
//         } else {
//             dataMap.set(key, { ...item });
//         }
//     });

//     return Array.from(dataMap.values());
// }

// function mergeRevenueRecords(record1: any, record2: any): any {
//     const earnings1 = parseFloat(record1.estimated_earnings || '0');
//     const earnings2 = parseFloat(record2.estimated_earnings || '0');
//     const impressions1 = parseFloat(record1.impressions || '0');
//     const impressions2 = parseFloat(record2.impressions || '0');
//     const clicks1 = parseFloat(record1.clicks || '0');
//     const clicks2 = parseFloat(record2.clicks || '0');

//     const totalEarnings = earnings1 + earnings2;
//     const totalImpressions = impressions1 + impressions2;
//     const totalClicks = clicks1 + clicks2;

//     const rpm = totalImpressions > 0 ? (totalEarnings / totalImpressions) * 1000 : 0;
//     const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
//     const cpc = totalClicks > 0 ? (totalEarnings / totalClicks) : 0;

//     return {
//         date: record1.date || record2.date,
//         country: record1.country || record2.country,
//         article: record1.article || record2.article,
//         channel: record1.channel || record2.channel,
//         styleid: record1.styleid || record2.styleid,
//         estimated_earnings: totalEarnings.toFixed(2),
//         impressions: totalImpressions.toFixed(0),
//         clicks: totalClicks.toFixed(0),
//         rpm: rpm.toFixed(2),
//         impressionsctr: ctr.toFixed(2),
//         cpc: cpc.toFixed(2)
//     };
// }

// function mergeApiRowData(existing: any, newRow: any): any {
//     const earnings1 = parseFloat(existing.ESTIMATED_EARNINGS || '0');
//     const earnings2 = parseFloat(newRow.ESTIMATED_EARNINGS || '0');
//     const impressions1 = parseFloat(existing.IMPRESSIONS || '0');
//     const impressions2 = parseFloat(newRow.IMPRESSIONS || '0');
//     const clicks1 = parseFloat(existing.CLICKS || '0');
//     const clicks2 = parseFloat(newRow.CLICKS || '0');

//     const totalEarnings = earnings1 + earnings2;
//     const totalImpressions = impressions1 + impressions2;
//     const totalClicks = clicks1 + clicks2;

//     const rpm = totalImpressions > 0 ? (totalEarnings / totalImpressions) * 1000 : 0;
//     const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
//     const cpc = totalClicks > 0 ? (totalEarnings / totalClicks) : 0;

//     return {
//         ...existing,
//         ESTIMATED_EARNINGS: totalEarnings.toFixed(2),
//         IMPRESSIONS: totalImpressions.toFixed(0),
//         CLICKS: totalClicks.toFixed(0),
//         IMPRESSIONS_RPM: rpm.toFixed(2),
//         IMPRESSIONS_CTR: ctr.toFixed(2),
//         COST_PER_CLICK: cpc.toFixed(2)
//     };
// }

// async function normalizeDbData(
//     dbResult: RevenueResult[],
//     breakpoints: string
// ): Promise<RevenueResult[]> {
//     const activeBreakpoints = breakpoints === 'all' || breakpoints === ''
//         ? ['date', 'country', 'article', 'channel', 'styleId']
//         : breakpoints.split(',').filter(bp => bp.trim());

//     return await Promise.all(dbResult.map(async (item) => {
//         const normalized: Partial<RevenueResult> = {
//             estimated_earnings: item.estimated_earnings,
//             impressions: item.impressions,
//             rpm: item.rpm,
//             clicks: item.clicks,
//             impressionsctr: item.impressionsctr,
//             cpc: item.cpc
//         };

//         if (activeBreakpoints.includes('date') && item.date) {
//             normalized.date = formatDate(item.date);
//         }
//         if (activeBreakpoints.includes('country') && item.country) {
//             normalized.country = await getCountryName(item.country);
//         }
//         if (activeBreakpoints.includes('article') && item.article) {
//             normalized.article = item.article;
//         }
//         if (activeBreakpoints.includes('channel') && item.channel) {
//             normalized.channel = await getChannelDisplayName(item.channel);
//         }
//         if (activeBreakpoints.includes('styleId') && item.styleid) {
//             normalized.styleid = await getStyleName(item.styleid);
//         }

//         return normalized as RevenueResult;
//     }));
// }

// async function mapApiDataToArticles(
//     apiData: any[],
//     articleMap: Map<string, any>,
//     channels: any[],
//     styles: any[],
//     breakpoints: string
// ): Promise<RevenueResult[]> {
//     const channelMap = new Map(channels.map(c => [c.ReportingDimensionId, c.Id]));
//     const styleMap = new Map(styles.map(s => [s.StyleId, s.Id]));

//     const articleDataMap = new Map();

//     for (const row of apiData) {
//         const channelId = channelMap.get(row.CUSTOM_CHANNEL_ID);
//         const styleId = styleMap.get(row.CUSTOM_SEARCH_STYLE_ID);

//         const matchingArticles = Array.from(articleMap.values()).filter(article => {
//             const channelMatch = !channelId || article.channel === channelId;
//             const styleMatch = !styleId ||
//                 article.styleIdLm === styleId ||
//                 article.styleIdDm === styleId;
//             return channelMatch && styleMatch;
//         });

//         for (const article of matchingArticles) {
//             const key = `${article.id}-${row.DATE || ''}-${row.COUNTRY_CODE || ''}`;

//             if (articleDataMap.has(key)) {
//                 const existing = articleDataMap.get(key);
//                 const merged = mergeApiRowData(existing, row);
//                 articleDataMap.set(key, merged);
//             } else {
//                 articleDataMap.set(key, {
//                     ...row,
//                     ARTICLE_ID: article.id,
//                     ARTICLE_TITLE: article.title,
//                     ARTICLE_URL: article.url
//                 });
//             }
//         }
//     }

//     const result = await Promise.all(
//         Array.from(articleDataMap.values()).map(async (item) => {
//             const activeBreakpoints = breakpoints === 'all' || breakpoints === ''
//                 ? ['date', 'country', 'article', 'channel', 'styleId']
//                 : breakpoints.split(',').filter(bp => bp.trim());
//             const rawCtr = parseFloat(item.IMPRESSIONS_CTR || '0');
//             const ctrPercentage = rawCtr * 100;

//             const revenueResult: Partial<RevenueResult> = {
//                 estimated_earnings: item.ESTIMATED_EARNINGS || null,
//                 impressions: item.IMPRESSIONS || null,
//                 rpm: item.IMPRESSIONS_RPM || null,
//                 clicks: item.CLICKS || null,
//                 impressionsctr: ctrPercentage.toFixed(2),
//                 cpc: item.COST_PER_CLICK || null,
//             };

//             if (activeBreakpoints.includes('date') && item.DATE) {
//                 revenueResult.date = item.DATE;
//             }

//             if (activeBreakpoints.includes('country') && item.COUNTRY_CODE) {
//                 revenueResult.country = await getCountryName(item.COUNTRY_CODE);
//             }

//             if (activeBreakpoints.includes('article') && item.ARTICLE_ID) {
//                 revenueResult.article = item.ARTICLE_TITLE
//                     ? `${item.ARTICLE_TITLE}`
//                     : item.ARTICLE_URL
//                         ? `${item.ARTICLE_URL}`
//                         : item.ARTICLE_ID;
//             }

//             if (activeBreakpoints.includes('channel') && item.CUSTOM_CHANNEL_ID) {
//                 revenueResult.channel = await getChannelDisplayName(item.CUSTOM_CHANNEL_ID);
//             }

//             if (activeBreakpoints.includes('styleId') && item.CUSTOM_SEARCH_STYLE_ID) {
//                 revenueResult.styleid = await getStyleName(item.CUSTOM_SEARCH_STYLE_ID);
//             }

//             return revenueResult as RevenueResult;
//         })
//     );

//     return result;
// }

// function aggregateRevenueData(data: RevenueResult[], preserveBreakdownData: boolean = false): RevenueResult {
//     const validData = data.filter(item => {
//         const earnings = parseFloat(item.estimated_earnings || '0');
//         const impressions = parseFloat(item.impressions || '0');
//         const clicks = parseFloat(item.clicks || '0');
//         return earnings > 0 || impressions > 0 || clicks > 0;
//     });

//     if (validData.length === 0) {
//         return {
//             estimated_earnings: '0.00',
//             impressions: '0',
//             clicks: '0',
//             rpm: '0.00',
//             impressionsctr: '0.00',
//             cpc: '0.00'
//         };
//     }

//     let totalEarnings = 0;
//     let totalImpressions = 0;
//     let totalClicks = 0;

//     validData.forEach(item => {
//         const earnings = parseFloat(item.estimated_earnings || '0');
//         const impressions = parseFloat(item.impressions || '0');
//         const clicks = parseFloat(item.clicks || '0');

//         totalEarnings += earnings;
//         totalImpressions += impressions;
//         totalClicks += clicks;
//     });

//     const rpm = totalImpressions > 0 ? (totalEarnings / totalImpressions) * 1000 : 0;
//     const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
//     const cpc = totalClicks > 0 ? (totalEarnings / totalClicks) : 0;

//     const aggregatedResult = {
//         estimated_earnings: totalEarnings.toFixed(2),
//         impressions: totalImpressions.toFixed(0),
//         clicks: totalClicks.toFixed(0),
//         rpm: rpm.toFixed(2),
//         impressionsctr: ctr.toFixed(2),
//         cpc: cpc.toFixed(2)
//     };

//     if (preserveBreakdownData && validData.length > 0) {
//         const dates = validData.map(item => item.date).filter(date => date);
//         if (dates.length > 0) {
//             const mostRecentDate = dates.sort((a, b) => new Date(b!).getTime() - new Date(a!).getTime())[0];
//             (aggregatedResult as any).date = mostRecentDate;
//         }
//     }

//     return aggregatedResult;
// }

// async function fetchFilteredLiveData(
//     oauth2Client: any,
//     userSettings: any,
//     domainId: string,
//     subdomainId: string,
//     userId: string,
//     isAdmin: boolean,
//     breakpoints: string,
//     InrtoUsd: string,
//     startDate: string,
//     endDate: string,
//     specificChannelIds?: string[],
//     specificStyleIds?: string[],
//     specificArticleIds?: string[]
// ): Promise<RevenueResult[]> {
//     try {
//         const whereClause: any = {
//             IsDeleted: false
//         };

//         if (isAdmin && userId) {
//             whereClause.User_Id_Settings = userId;
//         }
//         if (!isAdmin && userId) {
//             whereClause.User_Id_Settings = userId;
//         }

//         if (domainId) {
//             whereClause.Domain = domainId;
//         }

//         if (subdomainId) {
//             whereClause.SubDomain = subdomainId;
//         }

//         if (specificChannelIds && specificChannelIds.length > 0) {
//             whereClause.CustomChannal = { in: specificChannelIds };
//         }

//         // UPDATED: Add article filter to whereClause if specified
//         if (specificArticleIds && specificArticleIds.length > 0) {
//             whereClause.Id = { in: specificArticleIds };
//         }

//         const articleDetails = await prisma.articleDetails.findMany({
//             where: whereClause,
//             select: {
//                 Id: true,
//                 Title: true,
//                 Url: true,
//                 StyleIdLm: true,
//                 StyleIdDm: true,
//                 CustomChannal: true
//             }
//         });

//         if (articleDetails.length === 0) {
//             return [];
//         }

//         const articleMap = new Map();
//         const styleIds = new Set<string>();
//         const channelIds = new Set<string>();

//         articleDetails.forEach(article => {
//             articleMap.set(article.Id, {
//                 id: article.Id,
//                 title: article.Title,
//                 url: article.Url,
//                 styleIdLm: article.StyleIdLm,
//                 styleIdDm: article.StyleIdDm,
//                 channel: article.CustomChannal
//             });

//             if (article.StyleIdLm) styleIds.add(article.StyleIdLm);
//             if (article.StyleIdDm) styleIds.add(article.StyleIdDm);
//             if (article.CustomChannal) channelIds.add(article.CustomChannal);
//         });

//         let finalStyleIds = Array.from(styleIds);
//         if (specificStyleIds && specificStyleIds.length > 0) {
//             finalStyleIds = finalStyleIds.filter(id => specificStyleIds.includes(id));
//         } else {
//             finalStyleIds = [];
//         }

//         let channelsForApi: string[] = [];
//         let reportingStyleIds: string[] = [];

//         if (breakpoints === 'styleId') {
//             const styles = finalStyleIds.length > 0 ? await prisma.styleIds.findMany({
//                 where: { Id: { in: finalStyleIds } },
//                 select: { StyleId: true, Id: true, Name: true }
//             }) : [];

//             reportingStyleIds = styles
//                 .map(s => s.StyleId)
//                 .filter(Boolean) as string[];

//             channelsForApi = [];
//         } else {
//             const [channels, styles] = await Promise.all([
//                 Array.from(channelIds).length > 0 ? prisma.channals.findMany({
//                     where: { Id: { in: Array.from(channelIds) } },
//                     select: { ReportingDimensionId: true, Id: true, DisplayName: true }
//                 }) : [],
//                 finalStyleIds.length > 0 ? prisma.styleIds.findMany({
//                     where: { Id: { in: finalStyleIds } },
//                     select: { StyleId: true, Id: true, Name: true }
//                 }) : []
//             ]);

//             channelsForApi = channels
//                 .map(c => c.ReportingDimensionId)
//                 .filter(Boolean) as string[];

//             reportingStyleIds = styles
//                 .map(s => s.StyleId)
//                 .filter(Boolean) as string[];
//         }

//         if (channelsForApi.length === 0 && reportingStyleIds.length === 0) {
//             return [];
//         }

//         const startDateParts = startDate.split('-');
//         const endDateParts = endDate.split('-');

//         const dateStart = {
//             day: startDateParts[2],
//             month: startDateParts[1],
//             year: startDateParts[0]
//         };

//         const dateEnd = {
//             day: endDateParts[2],
//             month: endDateParts[1],
//             year: endDateParts[0]
//         };

//         const metrics = [
//             "ESTIMATED_EARNINGS",
//             "IMPRESSIONS",
//             "IMPRESSIONS_RPM",
//             "CLICKS",
//             "IMPRESSIONS_CTR",
//             "COST_PER_CLICK",
//         ];

//         let breakpointsObj;
//         if (breakpoints === 'all' || breakpoints === '') {
//             breakpointsObj = {
//                 country: true,
//                 date: true,
//                 customChannel: true,
//                 styleId: true
//             };
//         } else {
//             breakpointsObj = convertBreakpointsToObject(breakpoints);
//             delete breakpointsObj.platform;
//         }

//         const includeInrToUsd = InrtoUsd === 'false';

//         if (!oauth2Client) {
//             console.error('OAuth2 client is null or undefined');
//             return [];
//         }

//         if (!userSettings?.AdsAccountId) {
//             console.error('AdsAccountId is missing from user settings');
//             return [];
//         }

//         let allReportsData: any[] = [];

//         if (channelsForApi.length > BATCH_SIZE) {
//             const channelBatches = batchArray(channelsForApi, BATCH_SIZE);

//             for (let i = 0; i < channelBatches.length; i += MAX_CONCURRENT_BATCHES) {
//                 const currentBatches = channelBatches.slice(i, i + MAX_CONCURRENT_BATCHES);

//                 const batchPromises = currentBatches.map((batch) => {
//                     return processReportBatch(
//                         oauth2Client,
//                         userSettings,
//                         batch,
//                         reportingStyleIds,
//                         dateStart,
//                         dateEnd,
//                         metrics,
//                         breakpointsObj,
//                         includeInrToUsd
//                     );
//                 });

//                 const batchResults = await Promise.all(batchPromises);
//                 const flatResults = batchResults.flat();

//                 allReportsData = allReportsData.concat(flatResults);

//                 if (i + MAX_CONCURRENT_BATCHES < channelBatches.length) {
//                     await new Promise(resolve => setTimeout(resolve, 500));
//                 }
//             }
//         } else {
//             const batchResult = await processReportBatch(
//                 oauth2Client,
//                 userSettings,
//                 channelsForApi,
//                 reportingStyleIds,
//                 dateStart,
//                 dateEnd,
//                 metrics,
//                 breakpointsObj,
//                 includeInrToUsd
//             );
//             allReportsData = batchResult;
//         }

//         const filteredData = allReportsData.filter(row => {
//             const hasEarnings = row.ESTIMATED_EARNINGS && parseFloat(row.ESTIMATED_EARNINGS) > 0;
//             const hasImpressions = row.IMPRESSIONS && parseInt(row.IMPRESSIONS) > 0;
//             const hasClicks = row.CLICKS && parseInt(row.CLICKS) > 0;
//             return hasEarnings || hasImpressions || hasClicks;
//         });

//         if (filteredData.length === 0) {
//             return [];
//         }

//         const [channels, styles] = await Promise.all([
//             Array.from(channelIds).length > 0 ? prisma.channals.findMany({
//                 where: { Id: { in: Array.from(channelIds) } },
//                 select: { ReportingDimensionId: true, Id: true, DisplayName: true }
//             }) : [],
//             Array.from(styleIds).length > 0 ? prisma.styleIds.findMany({
//                 where: { Id: { in: Array.from(styleIds) } },
//                 select: { StyleId: true, Id: true, Name: true }
//             }) : []
//         ]);

//         const articleBasedData = await mapApiDataToArticles(
//             filteredData,
//             articleMap,
//             channels,
//             styles,
//             breakpoints
//         );

//         return articleBasedData;
//     } catch (error) {
//         console.error('Error in fetchFilteredLiveData:', error);
//         if (error instanceof Error) {
//             console.error('Error details:', {
//                 name: error.name,
//                 message: error.message,
//                 stack: error.stack
//             });
//         }
//         return [];
//     }
// }

// async function processReportBatch(
//     oauth2Client: any,
//     userSettings: any,
//     channelBatch: string[],
//     styleIds: string[],
//     startDate: any,
//     endDate: any,
//     metrics: string[],
//     breakpointsObj: any,
//     includeInrToUsd: boolean
// ): Promise<any[]> {
//     try {
//         const report = await generateReport(
//             oauth2Client,
//             userSettings.AdsAccountId,
//             google,
//             startDate,
//             endDate,
//             metrics,
//             breakpointsObj,
//             undefined,
//             includeInrToUsd.toString(),
//             channelBatch.length > 0 ? channelBatch : undefined,
//             styleIds.length > 0 ? styleIds : undefined
//         );

//         let batchData: any[] = [];

//         if (typeof report === 'string') {
//             const lines = report.trim().split('\n');
//             if (lines.length > 1) {
//                 const headers = lines[0].split(',').map(h => h.trim());

//                 batchData = lines.slice(1).map(line => {
//                     const values = line.split(',');
//                     return headers.reduce((obj: any, header, index) => {
//                         obj[header] = values[index]?.trim() || null;
//                         return obj;
//                     }, {});
//                 });
//             }
//         } else if (Array.isArray(report)) {
//             batchData = report;
//         }

//         return batchData;
//     } catch (error) {
//         console.error('Error processing batch:', error);
//         if (error instanceof Error) {
//             console.error('Batch error details:', {
//                 name: error.name,
//                 message: error.message,
//                 channelCount: channelBatch.length,
//                 styleCount: styleIds.length
//             });
//         }
//         return [];
//     }
// }

// export async function POST(req: NextRequest) {
//     try {
//         // const user = await verifyToken(req);
//         // if (!user) {
//         //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         // }
//         const role = req.headers.get('x-user-role') as string;
//         const userId = req.headers.get('x-user-id') as string;
//         const email = req.headers.get('x-user-email');
//         const name = req.headers.get('x-user-name');

//         const body = await req.json();
//         const {
//             domainId = '',
//             subdomainId = '',
//             channelId = '',
//             styleId = '',
//             articleId = ''
//         } = body;

//         const { searchParams } = new URL(req.url);
//         const startDate = searchParams.get('startDate');
//         const endDate = searchParams.get('endDate');
//         const breakpoints = searchParams.get('breakPoints') || '';
//         const InrtoUsd = searchParams.get('InrtoUsd') || 'false';
//         const userIdParam = searchParams.get('userId') || '';
//         const start = parseInt(searchParams.get('page') || '1');
//         const length = parseInt(searchParams.get("length") || "10");
//         const orderBy = searchParams.get("orderBy") || 'estimated_earnings';
//         const orderDir = searchParams.get("orderDir") || "desc";
//         const draw = parseInt(searchParams.get("draw") || "1");

//         if (!startDate || !endDate) {
//             return NextResponse.json(
//                 { error: 'Start date and end date are required' },
//                 { status: 400 }
//             );
//         }

//         const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
//         if (!dateRegex.test(startDate) || !dateRegex.test(endDate)) {
//             return NextResponse.json(
//                 { error: 'Invalid date format. Use YYYY-MM-DD format' },
//                 { status: 400 }
//             );
//         }

//         const validBreakpoints = [
//             '', 'all', 'date', 'country', 'article', 'channel', 'styleId',
//             'date,country', 'date,article', 'date,channel', 'date,styleId',
//             'country,article', 'country,channel', 'country,styleId',
//             'article,channel', 'article,styleId', 'channel,styleId',
//             'date,country,article', 'date,country,channel', 'date,country,styleId',
//             'date,article,channel', 'date,article,styleId', 'date,channel,styleId',
//             'country,article,channel', 'country,article,styleId', 'country,channel,styleId',
//             'article,channel,styleId', 'date,country,article,channel',
//             'date,country,article,styleId', 'date,country,channel,styleId',
//             'date,article,channel,styleId', 'country,article,channel,styleId'
//         ];

//         if (!validBreakpoints.includes(breakpoints)) {
//             return NextResponse.json(
//                 { error: 'Invalid breakpoints parameter' },
//                 { status: 400 }
//             );
//         }

//         const isAdmin = role === 'Super Admin' || role === 'Admin';
//         const effectiveUserId = isAdmin ? userIdParam || '' : userId;

//         const specificChannelIds = (channelId && typeof channelId === 'string')
//             ? channelId.split(',').filter((id: string) => id.trim())
//             : [];
//         const specificStyleIds = (styleId && typeof styleId === 'string')
//             ? styleId.split(',').filter((id: string) => id.trim())
//             : [];
//         const specificArticleIds = (articleId && typeof articleId === 'string')
//             ? articleId.split(',').filter((id: string) => id.trim())
//             : [];

//         let result: RevenueResult[] = [];
//         let totalRecords = 0;
//         let isLiveData = false;

//         const today = new Date();
//         const yesterday = new Date(today);
//         yesterday.setDate(today.getDate() - 1);

//         const todayFormatted = today.toISOString().split('T')[0];
//         const yesterdayFormatted = yesterday.toISOString().split('T')[0];

//         const needsLiveData = endDate >= yesterdayFormatted;
//         const liveDataStartDate = startDate >= yesterdayFormatted ? startDate : yesterdayFormatted;

//         const historicalEndDate = needsLiveData ?
//             (startDate < yesterdayFormatted ?
//                 new Date(yesterdayFormatted).getTime() - 24 * 60 * 60 * 1000 : null) :
//             endDate;

//         // HISTORICAL DATA SECTION
//         if (startDate < yesterdayFormatted && historicalEndDate) {
//             const historicalEndDateStr = new Date(historicalEndDate).toISOString().split('T')[0];

//             try {
//                 const whereClause: any = {};

//                 if (effectiveUserId) {
//                     if (isAdmin && effectiveUserId) {
//                         whereClause.User_Id_Settings = effectiveUserId;
//                     }
//                     if (!isAdmin) {
//                         whereClause.User_Id_Settings = effectiveUserId;
//                     }
//                 }

//                 if (domainId) {
//                     whereClause.Domain = domainId;
//                 }
//                 if (subdomainId) {
//                     whereClause.SubDomain = subdomainId;
//                 }

//                 // UPDATED: If specific articles are requested, filter by them first
//                 if (specificArticleIds.length > 0) {
//                     whereClause.Id = { in: specificArticleIds };
//                 }

//                 const articleDetails = await prisma.articleDetails.findMany({
//                     where: whereClause,
//                     select: {
//                         StyleIdLm: true,
//                         StyleIdDm: true,
//                         CustomChannal: true,
//                         Id: true
//                     }
//                 });

//                 if (articleDetails.length > 0) {
//                     const styleIds = new Set<string>();
//                     const channelIds = new Set<string>();
//                     const articleIds = new Set<string>();

//                     articleDetails.forEach(article => {
//                         if (article.StyleIdLm) styleIds.add(article.StyleIdLm);
//                         if (article.StyleIdDm) styleIds.add(article.StyleIdDm);
//                         if (article.CustomChannal) channelIds.add(article.CustomChannal);
//                         if (article.Id) articleIds.add(article.Id);
//                     });

//                     let finalChannelIds = Array.from(channelIds);
//                     let finalStyleIds = Array.from(styleIds);
//                     let finalArticleIds = Array.from(articleIds);

//                     // Apply user-specific style filtering if needed
//                     if (effectiveUserId) {
//                         const existingStyles = await prisma.styleIdUserMappings.findMany({
//                             where: {
//                                 StyleId: { in: Array.from(styleIds) },
//                                 UserId: effectiveUserId
//                             },
//                             select: { StyleId: true }
//                         });

//                         const existingStyleIds = new Set(existingStyles.map(style => style.StyleId));
//                         finalStyleIds = finalStyleIds.filter(id => existingStyleIds.has(id));
//                     }

//                     // Apply additional filters if provided
//                     if (specificChannelIds.length > 0) {
//                         finalChannelIds = finalChannelIds.filter(id => specificChannelIds.includes(id));
//                     }

//                     if (specificStyleIds.length > 0) {
//                         finalStyleIds = finalStyleIds.filter(id => specificStyleIds.includes(id));
//                     }

//                     if (finalChannelIds.length > 0 || finalStyleIds.length > 0 || finalArticleIds.length > 0) {
//                         const styleIdForSql = finalStyleIds.join(',');
//                         const channelIdForSql = finalChannelIds.join(',');

//                         // UPDATED: Only pass articleId if it's provided AND breakpoints includes article
//                         let articleIdForSql = '';
//                         if (specificArticleIds.length > 0 && breakpoints.includes('article')) {
//                             articleIdForSql = finalArticleIds.join(',');
//                         } else if (breakpoints.includes('article') && specificArticleIds.length === 0) {
//                             // If article is in breakpoints but no specific articles requested, use all user articles
//                             articleIdForSql = finalArticleIds.join(',');
//                         }
//                         // If article NOT in breakpoints, articleIdForSql remains empty string

//                         const sanitizedStyleId = sanitizeStringForSQL(styleIdForSql);
//                         const sanitizedChannelId = sanitizeStringForSQL(channelIdForSql);
//                         const sanitizedArticleId = sanitizeStringForSQL(articleIdForSql);
//                         const sanitizedBreakpoints = sanitizeStringForSQL(breakpoints);
//                         const booleanInrtoUsd = InrtoUsd === 'true';

//                         // Call the database function
//                         await prisma.$executeRawUnsafe(`
//                             SELECT * FROM fn_revenue_data_final(
//                                 $1::text,
//                                 $2::text,
//                                 $3::date,
//                                 $4::date,
//                                 $5::text,
//                                 $6::boolean,
//                                 $7::text
//                             )
//                         `, sanitizedStyleId, sanitizedChannelId, startDate, historicalEndDateStr, sanitizedBreakpoints, booleanInrtoUsd, sanitizedArticleId);

//                         const historicalResult = await prisma.$queryRawUnsafe<RevenueResult[]>(`
//                             SELECT * FROM pg_temp.temp_revenue_result
//                         `);

//                         const processedHistorical = await normalizeDbData(historicalResult, breakpoints);
//                         result = result.concat(processedHistorical);
//                     }
//                 }
//             } catch (error) {
//                 console.error('Historical data fetch error:', error);
//                 if (error instanceof Error && error.message.includes('syntax error')) {
//                     return NextResponse.json(
//                         {
//                             error: 'Database function error',
//                             details: process.env.NODE_ENV === 'development'
//                                 ? 'SQL syntax error in fn_revenue_data_final function'
//                                 : 'Internal database error'
//                         },
//                         { status: 500 }
//                     );
//                 }
//                 throw error;
//             }
//         }

//         // LIVE DATA SECTION
//         if (needsLiveData) {
//             try {
//                 const userSettings = await prisma.adminUserSetting.findFirst();
//                 if (userSettings) {
//                     const oauth2Client = new google.auth.OAuth2(
//                         userSettings.RevenueClientId as string,
//                         userSettings.RevenueClientSecret as string,
//                         userSettings.RevenueRedirectUrl as string
//                     );

//                     oauth2Client.setCredentials({
//                         access_token: userSettings.RevenueAccessToken,
//                         refresh_token: userSettings.RevenueRefreshToken,
//                     });

//                     let finalSpecificChannelIds = specificChannelIds;
//                     let finalSpecificStyleIds = specificStyleIds;
//                     let finalSpecificArticleIds = specificArticleIds;

//                     if (!isAdmin || (isAdmin && userIdParam)) {
//                         const whereClause: any = {
//                             User_Id_Settings: effectiveUserId
//                         };

//                         if (domainId) {
//                             whereClause.Domain = domainId;
//                         }
//                         if (subdomainId) {
//                             whereClause.SubDomain = subdomainId;
//                         }

//                         // UPDATED: If articleId is provided, add it to whereClause
//                         if (specificArticleIds.length > 0) {
//                             whereClause.Id = { in: specificArticleIds };
//                         }

//                         const articleDetails = await prisma.articleDetails.findMany({
//                             where: whereClause,
//                             select: {
//                                 Id: true,
//                                 StyleIdLm: true,
//                                 StyleIdDm: true,
//                                 CustomChannal: true
//                             }
//                         });

//                         if (articleDetails.length > 0) {
//                             const userStyleIds = new Set<string>();
//                             const userChannelIds = new Set<string>();
//                             const userArticleIds = new Set<string>();

//                             articleDetails.forEach(article => {
//                                 if (article.StyleIdLm) userStyleIds.add(article.StyleIdLm);
//                                 if (article.StyleIdDm) userStyleIds.add(article.StyleIdDm);
//                                 if (article.CustomChannal) userChannelIds.add(article.CustomChannal);
//                                 userArticleIds.add(article.Id);
//                             });

//                             // Set the final arrays based on what was found
//                             finalSpecificChannelIds = Array.from(userChannelIds);
//                             finalSpecificStyleIds = Array.from(userStyleIds);
//                             finalSpecificArticleIds = Array.from(userArticleIds);

//                             // Apply additional filters if provided
//                             if (specificChannelIds.length > 0) {
//                                 finalSpecificChannelIds = finalSpecificChannelIds.filter(id => specificChannelIds.includes(id));
//                             }
//                             if (specificStyleIds.length > 0) {
//                                 finalSpecificStyleIds = finalSpecificStyleIds.filter(id => specificStyleIds.includes(id));
//                             }
//                         } else {
//                             // No articles found, set empty arrays
//                             finalSpecificChannelIds = [];
//                             finalSpecificStyleIds = [];
//                             finalSpecificArticleIds = [];
//                         }
//                     }

//                     const liveData = await fetchFilteredLiveData(
//                         oauth2Client,
//                         userSettings,
//                         domainId,
//                         subdomainId,
//                         effectiveUserId,
//                         isAdmin,
//                         breakpoints,
//                         InrtoUsd,
//                         liveDataStartDate,
//                         endDate,
//                         finalSpecificChannelIds.length > 0 ? finalSpecificChannelIds : undefined,
//                         finalSpecificStyleIds.length > 0 ? finalSpecificStyleIds : undefined,
//                         finalSpecificArticleIds.length > 0 ? finalSpecificArticleIds : undefined
//                     );

//                     result = result.concat(liveData);
//                     isLiveData = true;
//                 }
//             } catch (error) {
//                 console.error('Error fetching live data:', error);
//                 if (process.env.NODE_ENV === 'development') {
//                     return NextResponse.json(
//                         {
//                             error: 'Failed to fetch live data',
//                             details: error instanceof Error ? error.message : 'Unknown error',
//                             historicalDataCount: result.length
//                         },
//                         { status: 500 }
//                     );
//                 }
//             }
//         }

//         // DATA PROCESSING AND RESPONSE
//         result = deduplicateAndMergeReportData(result, breakpoints) as RevenueResult[];
//         totalRecords = result.length;

//         const summary = aggregateRevenueData(result);

//         if (!breakpoints || breakpoints === '') {
//             const aggregatedData = aggregateRevenueData(result, true);
//             result = [aggregatedData];
//             totalRecords = 1;
//         }

//         const safeOrderField = getSafeOrderField(orderBy);
//         const safeOrderDir = orderDir.toLowerCase() === 'asc' ? 1 : -1;

//         result.sort((a, b) => {
//             let aVal: any = a[safeOrderField];
//             let bVal: any = b[safeOrderField];

//             if (isNumericField(safeOrderField)) {
//                 aVal = parseFloat(aVal as string) || 0;
//                 bVal = parseFloat(bVal as string) || 0;
//             } else if (safeOrderField === 'date') {
//                 aVal = new Date(aVal as string);
//                 bVal = new Date(bVal as string);
//             }

//             if (aVal < bVal) return -1 * safeOrderDir;
//             if (aVal > bVal) return 1 * safeOrderDir;
//             return 0;
//         });

//         let paginatedResult = result;
//         if (!isLiveData && length !== -1) {
//             const offset = (start - 1) * length;
//             paginatedResult = result.slice(offset, offset + length);
//         }

//         const actualCurrentPageCount = isLiveData
//             ? totalRecords
//             : Math.min(
//                 length === -1 ? totalRecords : length,
//                 Math.max(0, totalRecords - (start - 1) * length)
//             );

//         return NextResponse.json({
//             success: true,
//             data: paginatedResult,
//             summary: summary,
//             ...(isLiveData ? {} : {
//                 pagination: {
//                     draw,
//                     recordsTotal: totalRecords,
//                     currentPageCount: actualCurrentPageCount,
//                     start: start,
//                     length: length,
//                     currentPage: start,
//                     totalPages: length === -1 ? 1 : Math.ceil(totalRecords / length),
//                     hasNextPage: length === -1 ? false : start * length < totalRecords,
//                     hasPreviousPage: start > 1,
//                 }
//             })
//         });

//     } catch (error) {
//         console.error('Main error:', error);
//         return NextResponse.json(
//             {
//                 error: 'Failed to fetch revenue data',
//                 details: process.env.NODE_ENV === 'development' && error instanceof Error
//                     ? error.message
//                     : 'Internal server error'
//             },
//             { status: 500 }
//         );
//     }
// }





import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

interface CountResult {
    total_count: bigint;
}

interface RevenueResult {
    date?: string;
    country?: string;
    article?: string;
    channel?: string;
    styleid?: string;
    estimated_earnings: string | null;
    impressions: string | null;
    rpm: string | null;
    clicks: string | null;
    impressionsctr: string | null;
    cpc: string | null;
}

type OrderableField =
    | 'date'
    | 'country'
    | 'article'
    | 'channel'
    | 'styleid'
    | 'estimated_earnings'
    | 'impressions'
    | 'rpm'
    | 'clicks'
    | 'impressionsctr'
    | 'cpc';

type NumericField =
    | 'estimated_earnings'
    | 'impressions'
    | 'rpm'
    | 'clicks'
    | 'impressionsctr'
    | 'cpc';

const ORDERABLE_FIELDS: Record<string, OrderableField> = {
    DATE: 'date',
    COUNTRY: 'country',
    ARTICLE: 'article',
    CHANNEL: 'channel',
    STYLE_ID: 'styleid',
    EARNINGS: 'estimated_earnings',
    IMPRESSIONS: 'impressions',
    RPM: 'rpm',
    CLICKS: 'clicks',
    CTR: 'impressionsctr',
    CPC: 'cpc'
};

const NUMERIC_FIELDS: NumericField[] = [
    'estimated_earnings',
    'impressions',
    'rpm',
    'clicks',
    'impressionsctr',
    'cpc'
];

interface CronJobEndTimes {
    'Revenue Data': string | null;
}

async function getLatestCronJobEndTimes(): Promise<CronJobEndTimes> {
    try {
        const cronJobs = await prisma.dailyCronJob.findMany({
            where: {
                status: 'completed',
                OR: [
                    { job_name: { contains: 'Google Ads Sense' } }
                ]
            },
            select: {
                job_name: true,
                end_time: true
            },
            orderBy: {
                end_time: 'desc'
            }
        });

        const latestEndTimes: CronJobEndTimes = {
            'Revenue Data': null
        };

        for (const job of cronJobs) {
            if (job.end_time) {
                const endTime = typeof job.end_time === 'string' ? new Date(job.end_time) : job.end_time;

                const localDateTimeString = endTime.toLocaleString('en-GB', {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });

                if (job.job_name.includes('Google Ads Sense') && !latestEndTimes['Revenue Data']) {
                    latestEndTimes['Revenue Data'] = localDateTimeString;
                }
                if (latestEndTimes['Revenue Data']) {
                    break;
                }
            }
        }

        return latestEndTimes;
    } catch (error) {
        console.error('Error fetching cron job end times:', error);
        return {
            'Revenue Data': null
        };
    }
}

function isOrderableField(field: string): field is OrderableField {
    return Object.values(ORDERABLE_FIELDS).includes(field as OrderableField);
}

function isNumericField(field: string): field is NumericField {
    return NUMERIC_FIELDS.includes(field as NumericField);
}

function getSafeOrderField(field: string): OrderableField {
    return isOrderableField(field) ? field : 'estimated_earnings';
}

function sanitizeStringForSQL(value: string): string {
    return value.replace(/'/g, "''");
}

async function normalizeDbData(
    dbResult: RevenueResult[],
    breakpoints: string
): Promise<RevenueResult[]> {
    const activeBreakpoints = breakpoints === 'all' || breakpoints === ''
        ? ['date', 'country', 'article', 'channel', 'styleId']
        : breakpoints.split(',').filter(bp => bp.trim());

    return await Promise.all(dbResult.map(async (item) => {
        const normalized: Partial<RevenueResult> = {
            estimated_earnings: item.estimated_earnings,
            impressions: item.impressions,
            rpm: item.rpm,
            clicks: item.clicks,
            impressionsctr: item.impressionsctr,
            cpc: item.cpc
        };

        if (activeBreakpoints.includes('date') && item.date) {
            normalized.date = item.date;
        }
        if (activeBreakpoints.includes('country') && item.country) {
            normalized.country = item.country;
        }
        if (activeBreakpoints.includes('article') && item.article) {
            normalized.article = item.article;
        }
        if (activeBreakpoints.includes('channel') && item.channel) {
            normalized.channel = item.channel;
        }
        if (activeBreakpoints.includes('styleId') && item.styleid) {
            normalized.styleid = item.styleid;
        }

        return normalized as RevenueResult;
    }));
}

function aggregateRevenueData(data: RevenueResult[], preserveBreakdownData: boolean = false): RevenueResult {
    const validData = data.filter(item => {
        const earnings = parseFloat(item.estimated_earnings || '0');
        const impressions = parseFloat(item.impressions || '0');
        const clicks = parseFloat(item.clicks || '0');
        return earnings > 0 || impressions > 0 || clicks > 0;
    });

    if (validData.length === 0) {
        return {
            estimated_earnings: '0.00',
            impressions: '0',
            clicks: '0',
            rpm: '0.00',
            impressionsctr: '0.00',
            cpc: '0.00'
        };
    }

    let totalEarnings = 0;
    let totalImpressions = 0;
    let totalClicks = 0;

    validData.forEach(item => {
        const earnings = parseFloat(item.estimated_earnings || '0');
        const impressions = parseFloat(item.impressions || '0');
        const clicks = parseFloat(item.clicks || '0');

        totalEarnings += earnings;
        totalImpressions += impressions;
        totalClicks += clicks;
    });

    const rpm = totalImpressions > 0 ? (totalEarnings / totalImpressions) * 1000 : 0;
    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
    const cpc = totalClicks > 0 ? (totalEarnings / totalClicks) : 0;

    const aggregatedResult = {
        estimated_earnings: totalEarnings.toFixed(2),
        impressions: totalImpressions.toFixed(0),
        clicks: totalClicks.toFixed(0),
        rpm: rpm.toFixed(2),
        impressionsctr: ctr.toFixed(2),
        cpc: cpc.toFixed(2)
    };

    if (preserveBreakdownData && validData.length > 0) {
        const dates = validData.map(item => item.date).filter(date => date);
        if (dates.length > 0) {
            const mostRecentDate = dates.sort((a, b) => new Date(b!).getTime() - new Date(a!).getTime())[0];
            (aggregatedResult as any).date = mostRecentDate;
        }
    }

    return aggregatedResult;
}

export async function POST(req: NextRequest) {
    try {
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const body = await req.json();
        const {
            domainId = '',
            subdomainId = '',
            channelId = '',
            styleId = '',
            articleId = ''
        } = body;

        const latestEndTimes = await getLatestCronJobEndTimes();

        const { searchParams } = new URL(req.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const breakpoints = searchParams.get('breakPoints') || '';
        const InrtoUsd = searchParams.get('InrtoUsd') || 'false';
        const userIdParam = searchParams.get('userId') || '';
        const start = parseInt(searchParams.get('page') || '1');
        const length = parseInt(searchParams.get('length') || '10');
        const orderBy = searchParams.get('orderBy') || 'estimated_earnings';
        const orderDir = searchParams.get('orderDir') || 'desc';
        const draw = parseInt(searchParams.get('draw') || '1');

        if (!startDate || !endDate) {
            return NextResponse.json(
                { error: 'Start date and end date are required' },
                { status: 400 }
            );
        }

        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(startDate) || !dateRegex.test(endDate)) {
            return NextResponse.json(
                { error: 'Invalid date format. Use YYYY-MM-DD format' },
                { status: 400 }
            );
        }

        const validBreakpoints = [
            '', 'all', 'date', 'country', 'article', 'channel', 'styleId',
            'date,country', 'date,article', 'date,channel', 'date,styleId',
            'country,article', 'country,channel', 'country,styleId',
            'article,channel', 'article,styleId', 'channel,styleId',
            'date,country,article', 'date,country,channel', 'date,country,styleId',
            'date,article,channel', 'date,article,styleId', 'date,channel,styleId',
            'country,article,channel', 'country,article,styleId', 'country,channel,styleId',
            'article,channel,styleId', 'date,country,article,channel',
            'date,country,article,styleId', 'date,country,channel,styleId',
            'date,article,channel,styleId', 'country,article,channel,styleId'
        ];

        if (!validBreakpoints.includes(breakpoints)) {
            return NextResponse.json(
                { error: 'Invalid breakpoints parameter' },
                { status: 400 }
            );
        }

        const isAdmin = role === 'Super Admin' || role === 'Admin';
        const effectiveUserId = isAdmin ? userIdParam || '' : userId;

        // Process input IDs only if they are non-empty
        const specificChannelIds = (channelId && typeof channelId === 'string' && channelId.trim() !== '')
            ? channelId.split(',').filter((id: string) => id.trim())
            : null; // Use null to indicate no specific channel IDs provided

        const specificStyleIds = (styleId && typeof styleId === 'string' && styleId.trim() !== '')
            ? styleId.split(',').filter((id: string) => id.trim())
            : null; // Use null to indicate no specific style IDs provided

        const specificArticleIds = (articleId && typeof articleId === 'string' && articleId.trim() !== '')
            ? articleId.split(',').filter((id: string) => id.trim())
            : null; // Use null to indicate no specific article IDs provided

        let result: RevenueResult[] = [];
        let totalRecords = 0;

        try {
            const whereClause: any = {};

            if (effectiveUserId) {
                whereClause.User_Id_Settings = effectiveUserId;
            }

            if (domainId && domainId.trim() !== '') {
                whereClause.Domain = domainId;
            }
            if (subdomainId && subdomainId.trim() !== '') {
                whereClause.SubDomain = subdomainId;
            }

            if (specificArticleIds) {
                whereClause.Id = { in: specificArticleIds };
            }

            const articleDetails = await prisma.articleDetails.findMany({
                where: whereClause,
                select: {
                    StyleIdLm: true,
                    StyleIdDm: true,
                    CustomChannal: true,
                    Id: true
                }
            });

            if (articleDetails.length > 0) {
                const styleIds = new Set<string>();
                const channelIds = new Set<string>();
                const articleIds = new Set<string>();

                articleDetails.forEach(article => {
                    if (article.StyleIdLm) styleIds.add(article.StyleIdLm);
                    if (article.StyleIdDm) styleIds.add(article.StyleIdDm);
                    if (article.CustomChannal) channelIds.add(article.CustomChannal);
                    if (article.Id) articleIds.add(article.Id);
                });

                // Use all IDs from articleDetails if specific IDs are not provided
                let finalChannelIds = specificChannelIds ? specificChannelIds.filter(id => channelIds.has(id)) : Array.from(channelIds);
                let finalStyleIds = specificStyleIds ? specificStyleIds.filter(id => styleIds.has(id)) : Array.from(styleIds);
                let finalArticleIds = specificArticleIds ? specificArticleIds.filter(id => articleIds.has(id)) : Array.from(articleIds);

                if (effectiveUserId) {
                    const existingStyles = await prisma.styleIdUserMappings.findMany({
                        where: {
                            StyleId: { in: Array.from(styleIds) },
                            UserId: effectiveUserId
                        },
                        select: { StyleId: true }
                    });

                    const existingStyleIds = new Set(existingStyles.map(style => style.StyleId));
                    finalStyleIds = finalStyleIds.filter(id => existingStyleIds.has(id));
                }

                // If no valid channels remain after filtering, return empty response
                if (finalChannelIds.length === 0 && specificChannelIds && specificChannelIds.length > 0) {
                    return NextResponse.json({
                        success: true,
                        data: [],
                        summary: aggregateRevenueData([]),
                        pagination: {
                            draw,
                            recordsTotal: 0,
                            currentPageCount: 0,
                            start: start,
                            length: length,
                            currentPage: start,
                            totalPages: 0,
                            hasNextPage: false,
                            hasPreviousPage: false,
                        },
                        cronJobInfo: latestEndTimes
                    });
                }

                let channelIdForSql = '';
                if (finalChannelIds.length > 0) {
                    channelIdForSql = finalChannelIds.join(',');
                }

                let articleIdForSql = '';
                if (specificArticleIds || breakpoints.includes('article')) {
                    articleIdForSql = finalArticleIds.join(',');
                }

                const sanitizedStyleId = sanitizeStringForSQL(finalStyleIds.join(','));
                const sanitizedChannelId = sanitizeStringForSQL(channelIdForSql);
                const sanitizedArticleId = sanitizeStringForSQL(articleIdForSql);
                const sanitizedBreakpoints = sanitizeStringForSQL(breakpoints);
                const booleanInrtoUsd = InrtoUsd === 'true';

                // console.log("Query parameters:", {
                //     sanitizedStyleId,
                //     sanitizedChannelId,
                //     sanitizedArticleId,
                //     startDate,
                //     endDate,
                //     sanitizedBreakpoints,
                //     booleanInrtoUsd
                // });

                await prisma.$executeRawUnsafe(`
                    SELECT * FROM fn_revenue_data_final(
                        $1::text,
                        $2::text,
                        $3::date,
                        $4::date,
                        $5::text,
                        $6::boolean,
                        $7::text
                    )
                `, sanitizedStyleId, sanitizedChannelId, startDate, endDate, sanitizedBreakpoints, booleanInrtoUsd, sanitizedArticleId);

                const dbResult = await prisma.$queryRawUnsafe<RevenueResult[]>(`
                    SELECT * FROM pg_temp.temp_revenue_result
                `);

                const processedData = await normalizeDbData(dbResult, breakpoints);
                result = processedData;

            } else {
                result = [];
            }
        } catch (error) {
            console.error('Database query error:', error);
            if (error instanceof Error && error.message.includes('syntax error')) {
                return NextResponse.json(
                    {
                        error: 'Database function error',
                        details: process.env.NODE_ENV === 'development'
                            ? 'SQL syntax error in fn_revenue_data_final function'
                            : 'Internal database error'
                    },
                    { status: 500 }
                );
            }
            throw error;
        }

        totalRecords = result.length;
        const summary = aggregateRevenueData(result);

        if (!breakpoints || breakpoints === '') {
            const aggregatedData = aggregateRevenueData(result, true);
            result = [aggregatedData];
            totalRecords = 1;
        }

        const safeOrderField = getSafeOrderField(orderBy);
        const safeOrderDir = orderDir.toLowerCase() === 'asc' ? 1 : -1;

        result.sort((a, b) => {
            let aVal: any = a[safeOrderField];
            let bVal: any = b[safeOrderField];

            if (isNumericField(safeOrderField)) {
                aVal = parseFloat(aVal as string) || 0;
                bVal = parseFloat(bVal as string) || 0;
            } else if (safeOrderField === 'date') {
                aVal = new Date(aVal as string);
                bVal = new Date(bVal as string);
            }

            if (aVal < bVal) return -1 * safeOrderDir;
            if (aVal > bVal) return 1 * safeOrderDir;
            return 0;
        });

        let paginatedResult = result;
        if (length !== -1) {
            const offset = (start - 1) * length;
            paginatedResult = result.slice(offset, offset + length);
        }

        const actualCurrentPageCount = Math.min(
            length === -1 ? totalRecords : length,
            Math.max(0, totalRecords - (start - 1) * length)
        );

        return NextResponse.json({
            success: true,
            data: paginatedResult,
            summary: summary,
            pagination: {
                draw,
                recordsTotal: totalRecords,
                currentPageCount: actualCurrentPageCount,
                start: start,
                length: length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(totalRecords / length),
                hasNextPage: length === -1 ? false : start * length < totalRecords,
                hasPreviousPage: start > 1,
            },
            cronJobInfo: latestEndTimes
        });

    } catch (error) {
        console.error('Main error:', error);
        return NextResponse.json(
            {
                error: 'Failed to fetch revenue data',
                details: process.env.NODE_ENV === 'development' && error instanceof Error
                    ? error.message
                    : 'Internal server error'
            },
            { status: 500 }
        );
    }
}