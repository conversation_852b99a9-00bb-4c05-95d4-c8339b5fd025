-- DROP FUNCTION public.fn_revenue_data(text, text, date, date, text, bool);

CREATE OR REPLACE FUNCTION public.fn_revenue_data_final(p_style_id text, p_channel_id text, p_from_date date, p_to_date date, p_group_mode text, p_convert_to_inr boolean, p_article_id text)
 RETURNS void
 LANGUAGE plpgsql
AS $function$

DECLARE

 sql TEXT;
	selectefield TEXT := '';
	selectfieldforinnerquery TEXT := '';
    selectfieldforjoin TEXT := '';
	selectfieldforgroup TEXT := '';
	nullable_where_clause  TEXT := '';
	selectfieldforwherecls  TEXT := '';
	selectfieldforchannelstylewherecls TEXT := '';
	selectfieldforcountryjoin TEXT := '';
	today_rate numeric(10,2);
 	
BEGIN

	-- Cleanup any existing temp table
    EXECUTE 'DROP TABLE IF EXISTS pg_temp.temp_revenue_result';

	EXECUTE '
    CREATE TEMP TABLE pg_temp.temp_revenue_result (
        date date,
        country text,
        article text,
        channel text,
        styleId text,
        estimated_earnings numeric,
        impressions numeric,
        rpm numeric,
        clicks numeric,
        impressionsctr numeric,
        cpc numeric
    )';

	today_rate := 
		(SELECT ROUND("USDtoINR", 2)
		FROM "Ads_RateConversion" 
		WHERE "Date" = (SELECT MAX("Date") FROM "Ads_RateConversion"));
		

	IF TRIM(p_channel_id) <> '' AND TRIM(p_style_id) <> '' AND TRIM(p_article_id) <> '' THEN
	selectfieldforchannelstylewherecls := ' AND ch."Id" = ANY(string_to_array($2, '','')::UUID[]) AND si."Id" = ANY(string_to_array($1, '','')::UUID[]) AND art."Id" = ANY(string_to_array($7, '','')::UUID[])';

	ELSEIF TRIM(p_channel_id) <> '' AND TRIM(p_style_id) <> '' THEN
	selectfieldforchannelstylewherecls := ' AND ch."Id" = ANY(string_to_array($2, '','')::UUID[]) AND si."Id" = ANY(string_to_array($1, '','')::UUID[])';

	ELSEIF TRIM(p_channel_id) <> '' AND TRIM(p_article_id) <> '' THEN
	selectfieldforchannelstylewherecls := ' AND ch."Id" = ANY(string_to_array($2, '','')::UUID[]) AND art."Id" = ANY(string_to_array($7, '','')::UUID[])';

	ELSEIF TRIM(p_style_id) <> '' AND TRIM(p_article_id) <> '' THEN
	selectfieldforchannelstylewherecls := ' AND si."Id" = ANY(string_to_array($1, '','')::UUID[]) AND art."Id" = ANY(string_to_array($7, '','')::UUID[])';
	
	ELSEIF TRIM(p_channel_id) <> '' THEN
	selectfieldforchannelstylewherecls := ' AND ch."Id" = ANY(string_to_array($2, '','')::UUID[])';
	
	ELSEIF TRIM(p_style_id) <> '' THEN
	selectfieldforchannelstylewherecls := ' AND si."Id" = ANY(string_to_array($1, '','')::UUID[])';

	ELSEIF TRIM(p_article_id) <> '' THEN
	selectfieldforchannelstylewherecls := ' AND art."Id" = ANY(string_to_array($7, '','')::UUID[])';
	
	ELSE
    selectfieldforchannelstylewherecls := '';  
    END IF;

	selectfieldforcountryjoin := ' left join "Ads_ISO_CountryMaster" isocm on isocm."CountryCode" = rv."Country" ';

	IF p_group_mode IS NULL OR p_group_mode = '' THEN
        selectefield := ' NULL::date , '''' , '''' , '''' , '''' ';
		selectfieldforinnerquery := '';
		selectfieldforjoin := '';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := '';

		ELSIF p_group_mode = 'date' THEN
        selectefield := 'x.date, '''', '''', '''', '''' ';
		selectfieldforinnerquery := ',rv."Date" as date';
        selectfieldforjoin := '';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date"';

		ELSIF p_group_mode = 'country' THEN
        selectefield := ' NULL::date, x.country, '''', '''', ''''  ';
		selectfieldforinnerquery := ',isocm."Name" as country';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name"';

		ELSIF p_group_mode = 'article' THEN
        selectefield := ' NULL::date, '''',  x.article, '''', '''' ';
		selectfieldforinnerquery := ',art."Title" AS article';
        selectfieldforjoin := '';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by art."Title"'; 

		ELSIF p_group_mode = 'channel' THEN
        selectefield := ' NULL::date, '''', '''',  x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, '''' ';
		selectfieldforinnerquery := ',ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by ch."DisplayName",rv."ChannalId" '; 

		ELSIF p_group_mode = 'styleId' THEN
        selectefield := ' NULL::date, '''', '''', '''',  x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by si."Name",rv."StyleId"  '; 

		ELSIF p_group_mode = 'date,country' THEN
        selectefield := 'x.date, x.country, '''', '''', '''' ';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name"';

		ELSIF p_group_mode = 'date,article' THEN
        selectefield := 'x.date, '''', x.article, '''', '''' ';
		selectfieldforinnerquery := ',rv."Date" as date , art."Title" AS article';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", art."Title"';

		ELSIF p_group_mode = 'date,channel' THEN
        selectefield := ' x.date, '''', '''',   x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, '''' ';
		selectfieldforinnerquery:= ' ,rv."Date" as date ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", ch."DisplayName",rv."ChannalId"';

		ELSIF p_group_mode = 'date,styleId' THEN
        selectefield := ' x.date, '''', '''', '''',   x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ' ,rv."Date" as date ,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'country,article' THEN
        selectefield := ' NULL::date, x.country, x.article, '''', '''' ';
		selectfieldforinnerquery := ',isocm."Name" as country ,art."Title" AS article';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name", art."Title"';

		ELSIF p_group_mode = 'country,channel' THEN
        selectefield :=  ' NULL::date, x.country, '''',  x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, ''''   ' ;
		selectfieldforinnerquery := ',isocm."Name" as country, ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name",ch."DisplayName",rv."ChannalId"';

		ELSIF p_group_mode = 'country,styleId' THEN
        selectefield := ' NULL::date, x.country, '''', '''',  x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',isocm."Name" as country ,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'article,channel' THEN
        selectefield := ' NULL::date, '''',  x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, ''''  ';
		selectfieldforinnerquery := ',art."Title" AS article ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by art."Title" , ch."DisplayName",rv."ChannalId"'; 

		ELSIF p_group_mode = 'article,styleId' THEN
      	selectefield := ' NULL::date, '''', x.article, '''', x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',art."Title" AS article ,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by art."Title",si."Name",rv."StyleId"'; 

		ELSIF p_group_mode = 'channel,styleId' THEN
	    selectefield := ' NULL::date,'''','''',x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName ,x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',ch."DisplayName" as customChannel,rv."ChannalId" as channalId, rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by ch."DisplayName",rv."ChannalId" , si."Name",rv."StyleId"'; 

		ELSIF p_group_mode = 'date,country,article' THEN
        selectefield := 'x.date, x.country, x.article,'''','''' ';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country,art."Title" AS article';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name", art."Title"';

		ELSIF p_group_mode = 'date,country,channel' THEN
       	selectefield := 'x.date, x.country, '''' ,x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, '''' ';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country, ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name", ch."DisplayName",rv."ChannalId"';

		ELSIF p_group_mode = 'date,country,styleId' THEN
        selectefield := 'x.date, x.country, '''' , '''' ,x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'date,article,channel' THEN
        selectefield := 'x.date, '''',  x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, '''' ';
		selectfieldforinnerquery := ',rv."Date" as date , art."Title" AS article ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", art."Title",  ch."DisplayName",rv."ChannalId"';

		ELSIF p_group_mode = 'date,article,styleId' THEN
        selectefield := 'x.date, '''' ,x.article, '''' ,x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',rv."Date" as date , art."Title" AS article ,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", art."Title", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'date,channel,styleId' THEN
        selectefield := ' x.date, '''', '''' ,x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, 
		x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ' ,rv."Date" as date ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId ,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", ch."DisplayName",rv."ChannalId", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'country,article,channel' THEN
        selectefield := ' NULL::date ,x.country, x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, ''''  ';
		selectfieldforinnerquery := ',isocm."Name" as country ,art."Title" AS article ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name", art."Title" , ch."DisplayName",rv."ChannalId"';

		ELSIF p_group_mode = 'country,article,styleId' THEN
        selectefield := ' NULL::date, x.country, x.article, '''' , x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',isocm."Name" as country ,art."Title" AS article,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name", art."Title", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'country,channel,styleId' THEN
        selectefield := ' NULL::date ,x.country, '''' , x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName,
		x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',isocm."Name" as country, ch."DisplayName" as customChannel,rv."ChannalId" as channalId,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name",ch."DisplayName",rv."ChannalId", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'article,channel,styleId' THEN
        selectefield := ' NULL::date,'''', x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',art."Title" AS article ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId 
		,rv."StyleId" as styleId ,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by art."Title" , ch."DisplayName",rv."ChannalId", si."Name",rv."StyleId"'; 
		

		ELSIF p_group_mode = 'date,country,article,channel' THEN
        selectefield := 'x.date, x.country, x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, ''''  ';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country,art."Title" AS article,ch."DisplayName" as customChannel,rv."ChannalId" as channalId';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name", art."Title",  ch."DisplayName",rv."ChannalId"';

		ELSIF p_group_mode = 'date,country,article,styleId' THEN
        selectefield := 'x.date, x.country, '''' ,x.article,x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country,art."Title" AS article,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name", art."Title", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'date,country,channel,styleId' THEN
        selectefield := 'x.date, x.country,'''',x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName,
		x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country, ch."DisplayName" as customChannel,rv."ChannalId" as channalId,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name", ch."DisplayName",rv."ChannalId", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'date,article,channel,styleId' THEN
       	selectefield := 'x.date, '''' ,x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, 
		x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',rv."Date" as date , art."Title" AS article ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := ' ';
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", art."Title",  ch."DisplayName",rv."ChannalId" , si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'country,article,channel,styleId' THEN
        selectefield := ' NULL::date,x.country, x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName ,
		x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',isocm."Name" as country ,art."Title" AS article ,ch."DisplayName" as customChannel,rv."ChannalId" as channalId,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
        selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by isocm."Name", art."Title" , ch."DisplayName",rv."ChannalId", si."Name",rv."StyleId"';

		ELSIF p_group_mode = 'all' THEN
        selectefield := 'x.date, x.country, x.article, x.customChannel || ''['' || split_part(x.channalId, '':'', 2) || '']''  as customChannelName, x.styleName || ''['' || x.styleId  || '']'' as styleId';
		selectfieldforinnerquery := ',rv."Date" as date, isocm."Name" as country,art."Title" AS article,ch."DisplayName" as customChannel,rv."ChannalId" as channalId,rv."StyleId" as styleId
			,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''''::text), '''') as styleName';
        selectfieldforjoin := selectfieldforcountryjoin;
		selectfieldforwherecls := selectfieldforchannelstylewherecls;
		selectfieldforgroup := 'group by rv."Date", isocm."Name", art."Title",  ch."DisplayName",rv."ChannalId" ,si."Name",rv."StyleId"';

		 ELSE
        RAISE EXCEPTION 'Unsupported group mode: %', p_group_mode;
    END IF;

	sql := format($fmt$
		INSERT INTO pg_temp.temp_revenue_result
        select 
			--x.customChannel || '[' || split_part(x.channalId, ':', 2) || ']'  as customChannelName , 
			--x.styleName || '[' || x.styleId  || ']' as styleId, 
			--x.date, 
			--x.article, 
			--x.country, 
			%s 
			ROUND(x.estimated_earnings,2),
			x.impressions, 
			ROUND((x.estimated_earnings /NULLIF(x.impressions,0 :: NUMERIC) * 1000),2) as rpm,
			x.clicks,
			ROUND((x.clicks /NULLIF(x.impressions,0 :: NUMERIC) * 100 ),2) as impressionsctr, 
			Round((x.estimated_earnings /NULLIF(x.clicks,0 :: NUMERIC)),2) as cpc 
			from (
			SELECT distinct

			CASE 
		        WHEN $6 THEN sum(ROUND(rv."EstimatedEarnings", 2)) / %s 
		        ELSE sum(ROUND(rv."EstimatedEarnings", 2))
		    END AS estimated_earnings,

			sum(rv."Impressions"::BIGINT) AS impressions,

			sum(rv."Clicks"::BIGINT) AS clicks

			%s
			--,rv."Date" as date
			--,rv."Country" as country
			--,art."Title" AS article
			--,ch."DisplayName" as customChannel
			--,rv."ChannalId" as channalId
			--,rv."StyleId" as styleId
			--,COALESCE(NULLIF(TRIM(BOTH FROM si."Name"), ''::text), '') as styleName
        	FROM "Revenue" rv
			%s
			left join "Channals" ch on ch."ReportingDimensionId" = rv."ChannalId"
			left join "StyleIds" si on si."StyleId" = rv."StyleId"
			left join "ArticleDetails" art on art."StyleIdLm" = si."Id" and art."CustomChannal" = ch."Id"
			WHERE rv."Date" between $3 and $4
			%s
			%s
			--rv."Date",rv."Country",art."Title",ch."DisplayName",rv."ChannalId", si."Name",rv."StyleId" 
			)
			as x
    $fmt$,
        CASE WHEN selectefield <> '' THEN selectefield || ', ' ELSE '' END,
		today_rate,
		selectfieldforinnerquery,
        selectfieldforjoin,
        selectfieldforwherecls,
		selectfieldforgroup
    );

    -- Execute final SQL
    EXECUTE sql USING p_style_id, p_channel_id, p_from_date, p_to_date,p_group_mode,p_convert_to_inr, p_article_id;
END;
$function$
;
