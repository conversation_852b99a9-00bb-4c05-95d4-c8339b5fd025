"use client";
import React, { useCallback, useEffect, useState } from "react";
import Swal from "sweetalert2";
import useDebounce from "@/hooks/useDebounce";
import { useSearchParams } from "next/navigation";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { FaPlus } from "react-icons/fa";
import CategoryModal from "./CategoryModal";
import apiClient from "@/utils/axiosInstance";

const Category = () => {
  const searchParams = useSearchParams();
  const [showLoader, setShowLoader] = useState(false);
  const [categories, setCategories] = useState([]);
  const [domainList, setDomainList] = useState([]);
  const [domainListLoader, setDomainListLoader] = useState(false);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("Name");

  // Modal state
  const [modalState, setModalState] = useState({
    open: false,
    mode: "add",
    initialData: null,
  });

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const columns = [
    {
      id: "Name",
      label: "Category Name",
    },
    {
      id: "ShowUrlName",
      label: "URL Name",
    },
  ];

  // Fetch domains for dropdown
  useEffect(() => {
    const fetchDomainList = async () => {
      try {
        setDomainListLoader(true);
        const response = await apiClient.get("/Domain/GetDropDown");
        if (response.data.success) {
          setDomainList(response.data.data);

          // Pre-select domain from URL parameters
          const domainIdFromUrl = searchParams.get('domainId');
          if (domainIdFromUrl && response.data.data.some(d => d.Id === domainIdFromUrl)) {
            setSelectedDomain(domainIdFromUrl);
          }
        }
      } catch (error) {
        console.error("Error fetching domains:", error);
      } finally {
        setDomainListLoader(false);
      }
    };

    fetchDomainList();
  }, [searchParams]);

  const fetchCategories = useCallback(async () => {
    if (!selectedDomain) {
      setCategories([]);
      setTotalCount(0);
      return;
    }

    try {
      setShowLoader(true);
      const response = await apiClient.get("/category/getAllCategory", {
        params: {
          page: page + 1,
          length: rowsPerPage,
          q: debouncedSearchTerm,
          orderBy,
          orderDir: order,
          domainId: selectedDomain,
        },
      });      

      if (response.status === 200) {
        setCategories(response.data.data);
        setTotalCount(
          response.data.pagination?.recordsFiltered ||
            response.data.data?.length ||
            0,
        );
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch categories",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  }, [selectedDomain, page, rowsPerPage, debouncedSearchTerm, orderBy, order]);

  const handleOpenAddModal = () => {
    setModalState({
      open: true,
      mode: "add",
      initialData: null,
    });
  };

  const handleOpenEditModal = async (rowData) => {
    try {
      Swal.fire({
        title: "Loading Category Data",
        html: "Please wait while we fetch the category details...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const response = await apiClient.get(`/category/GetById?id=${rowData.Id}`);

      const categoryData = response.data.data[0];
      Swal.close();

      setModalState({
        open: true,
        mode: "edit",
        initialData: categoryData,
      });
    } catch (error) {
      console.error("Error fetching category data:", error);
      Swal.close();
      await Swal.fire({
        title: "Error",
        text:
          error.response?.data?.message ||
          "Failed to load category data. Please try again.",
        icon: "error",
        confirmButtonText: "OK",
        confirmButtonColor: "#5750f1",
      });
    }
  };

  const handleCloseModal = () => {
    setModalState({
      open: false,
      mode: "add",
      initialData: null,
    });
  };

  const handleSubmit = async (formData, mode) => {
    try {
      setShowLoader(true);
      const url = mode === "add" ? "/category/addCategory" : "/category/EditCategory";
      const method = mode === "add" ? "post" : "put";
      
      const response = await apiClient.request({
        url,
        method,
        data: formData,
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (response.status === 200) {
        await Swal.fire({
          icon: "success",
          title: mode === "add" ? "Success" : "Updated",
          text: `Category ${mode === "add" ? "added" : "updated"} successfully`,
          timer: 2000,
          showConfirmButton: false,
        });
        handleCloseModal();
        fetchCategories();
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error?.response?.data?.error ||
          `Failed to ${mode === "add" ? "add" : "update"} category`,
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const handleDeleteCategory = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        setShowLoader(true);
        await apiClient.delete("/category/DeleteCategory", {
          data: { Id: rowData.Id },
        });        

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Category has been deleted.",
            timer: 2000,
            showConfirmButton: false,
          });
          fetchCategories();
        }
      } catch (error) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to delete category",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    }
  };

  const handleViewCategory = (rowData) => {
    const selectedDomainObj = domainList.find(domain => domain.Id === selectedDomain);
    
    if (!selectedDomainObj) {
      console.error("Selected domain not found");
      return;
    }
  
    let domainUrlName = selectedDomainObj.ShowUrlName || selectedDomainObj.Name.toLowerCase();
    
    if (!domainUrlName.startsWith('http://') && !domainUrlName.startsWith('https://')) {
      domainUrlName = `https://${domainUrlName}`;
    }
  
    const fullUrl = `${domainUrlName}/${rowData.ShowUrlName.toLowerCase()}`;
    
    window.open(fullUrl, "_blank");
  };

  const handleDomainChange = (item) => {
    setSelectedDomain(item?.Id ? item?.Id : "");
    setPage(0);
  };

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return (
    <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
      <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="p-6">
          <h1 className="text-2xl text-white">Category Management by Domain</h1>
        </div>
      </div>
      <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
        <div className="mb-5 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            {selectedDomain && (
              <>
                <div className="mt-4 w-full sm:w-64">
                  <Button
                    type="button"
                    label="Add Category"
                    variant="primary"
                    shape="rounded"
                    icon={<FaPlus size={14} />}
                    className="ml-auto flex w-full items-center justify-center gap-2 sm:w-64"
                    onClick={handleOpenAddModal}
                  />
                </div>
                <div className="w-full md:w-64">
                  <InputGroup
                    placeholder="Search..."
                    label="Search"
                    value={searchTerm}
                    handleChange={(e) => {
                      setSearchTerm(e.target.value);
                      setPage(0);
                    }}
                    type="text"
                    className="w-full"
                  />
                </div>
              </>
            )}
          </div>
          {domainListLoader ? (
            <div className="flex justify-center py-8 sm:w-64">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <div className="w-full sm:w-64">
              <SearchableDropdown
                label="Select Domain"
                options={domainList}
                placeholder="Select a Domain"
                value={selectedDomain}
                onChange={handleDomainChange}
                displayKey="Name"
                idKey="Id"
              />
            </div>
          )}
        </div>
        {selectedDomain && (
          <div className="mb-8">
            <CustomDataTable
              isLoading={showLoader}
              columns={columns}
              rows={categories}
              page={page}
              rowsPerPage={rowsPerPage}
              onPageChange={setPage}
              onRowsPerPageChange={setRowsPerPage}
              totalCount={totalCount}
              order={order}
              orderBy={orderBy}
              onRequestSort={(event, property) => {
                const isAsc = orderBy === property && order === "asc";
                setOrder(isAsc ? "desc" : "asc");
                setOrderBy(property);
              }}
              onView={handleViewCategory}
              onEdit={handleOpenEditModal}
              onDelete={handleDeleteCategory}
            />
          </div>
        )}
      </div>
      <CategoryModal
        open={modalState.open}
        onClose={handleCloseModal}
        onSubmit={handleSubmit}
        showLoader={showLoader}
        selectedDomain={selectedDomain}
        mode={modalState.mode}
        initialData={modalState.initialData}
      />
    </div>
  );
};

export default Category;