"use client"
import React, { useEffect, useState, useMemo } from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import { CloseIcon } from "@/assets/icons";
import InputGroup from "@/components/FormElements/InputGroup";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import Checkbox from "@/components/FormElements/checkbox";
import dynamic from "next/dynamic";
import Swal from "sweetalert2";

const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });



const shortJoditConfig = {
  height: 300,
  minHeight: 150,
  maxHeight: 900,
  tabIndex: 0,
  uploader: { insertImageAsBase64URI: true },
  iframe: true,
  iframeStyle: `
   body.jodit-wysiwyg {
    padding: 12px !important;
    margin: 0 !important;
    caret-color: auto !important;
  }
  .jodit-placeholder {
    left: 12px !important;
    right: auto !important;
  }
    body { font-family: Arial, sans-serif; line-height: 1.5; padding: 10px; }
    h1 { font-size: 2em; font-weight: bold; margin: 1em 0; }
    h2 { font-size: 1.75em; margin: .9em 0; }
    blockquote { border-left: 4px solid #ccc; padding-left: 1em; color: #555; }
    pre { background: #f4f4f4; padding: 1em; font-family: monospace; }

    /* Table border styles */
    table {
      border-collapse: collapse;
      width: 100%;
    }
    table, th, td {
      border: 1px solid #ccc;
    }
    th, td {
      padding: 8px;
    }
  `,
  toolbarAdaptive: false,
  buttons: [
    "bold",
    "italic",
    "underline",
    "strikethrough",
    "|",
    "ul",
    "ol",
    "|",
    "outdent",
    "indent",
    "|",
    "font",
    "fontsize",
    "brush",
    "paragraph",
    "|",
    "table",
    "link",
    "|",
    "align",
    "undo",
    "redo",
    "|",
    "hr",
    "eraser",
    "copyformat",
    "|",
    "source",
  ],
  style: {
    overflowY: "auto",
  },
};

const ArticleModal = ({
  open,
  onClose,
  mode,
  formData,
  setFormData,
  description,
  setDescription,
  shortDescription,
  setShortDescription,
  formdataImage,
  setFormdataImage,
  base64Image,
  setBase64Image,
  published,
  setPublished,
  showArticle,
  setShowArticle,
  showInDomain,
  setShowInDomain,
  showAds,
  setShowAds,
  selectedDomain,
  setSelectedDomain,
  selectedSubdomain,
  setSelectedSubdomain,
  selectedCampaignIds,
  setSelectedCampaignIds,
  domains,
  subdomains,
  categories,
  assignChannels,
  styleIds,
  campaigns,
  editId,
  editSlugMode,
  setEditSlugMode,
  handleFormSubmit,
  handleChange,
  handleChannelSelect,
  handleCampaignSelect,
  validateField,
  autoslug,
  showLoader,
  showSecondRelatedSearch,
  setShowSecondRelatedSearch,
}) => {

  const joditConfig = useMemo(() => ({
    height: 600,
    minHeight: 200,
    maxHeight: 900,
    tabIndex: 0,
    uploader: {
      url: '/api/article/ImageUpload',
      insertImageAsBase64URI: false,
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      },
      prepareData: (formData) => {
        const file = formData.get('files[0]');
        formData.append('Image', file);
        formData.append("domainId", selectedDomain)
        return formData;
      },
      isSuccess: (resp) => {
        let response = typeof resp === 'string' ? JSON.parse(resp) : resp;
        return response?.success === true;
      },
      getMessage: (resp) => {
        let response = typeof resp === 'string' ? JSON.parse(resp) : resp;
        return response?.message || 'Upload completed';
      },
      process: (resp) => {
        let response = typeof resp === 'string' ? JSON.parse(resp) : resp;
        if (!response?.imagePath) throw new Error("No imagePath in server response");
        
        const imagePath = response.imagePath.startsWith('/')
          ? response.imagePath
          : `/${response.imagePath}`;
          
        return {
          files: [imagePath],
          path: imagePath,
          error: null,
          msg: response.message
        };
      },
      defaultHandlerSuccess(data) {
        if (data.files?.length) {
          for (const file of data.files) {
            this.s.insertImage(file);
          }
        }
      },
      error(e) {
        console.error('Upload error:', e);
        Swal.fire({
          icon: 'error',
          title: 'Upload Failed',
          html: `Failed to upload image<br><small>${e.message || e}</small>`,
          showConfirmButton: true
        });
      }
    },
    iframe: true,
    iframeStyle: `
     body.jodit-wysiwyg {
      padding: 12px !important;
      margin: 0 !important;
      caret-color: auto !important;
    }
    .jodit-placeholder {
      left: 12px !important;
      right: auto !important;
    }
      body { font-family: Arial, sans-serif; line-height: 1.5; padding: 10px; }
      h1 { font-size: 2em; font-weight: bold; margin: 1em 0; }
      h2 { font-size: 1.75em; margin: .9em 0; }
      blockquote { border-left: 4px solid #ccc; padding-left: 1em; color: #555; }
      pre { background: #f4f4f4; padding: 1em; font-family: monospace; }
  
      /* Table border styles */
      table {
        border-collapse: collapse;
        width: 100%;
      }
      table, th, td {
        border: 1px solid #ccc;
      }
      th, td {
        padding: 8px;
      }
    `,
    toolbarAdaptive: false,
    buttons: [
      "bold",
      "italic",
      "underline",
      "strikethrough",
      "|",
      "ul",
      "ol",
      "|",
      "outdent",
      "indent",
      "|",
      "font",
      "fontsize",
      "brush",
      "paragraph",
      "|",
      "image",
      "table",
      "link",
      "|",
      "align",
      "undo",
      "redo",
      "|",
      "hr",
      "eraser",
      "copyformat",
      "|",
      "source",
      {
        name: "insertrelatedsearches2",
        tooltip: "Insert Related Search 2 Div",
        exec: (editor) => {
          if (editor.value.includes('id="relatedsearches2"')) {
            // const style = document.createElement('style');
            // const div = document.getElementById("relatedsearches2")
            // style.innerHTML = `
            //   #relatedsearches2 {
            //     background-color: #fff9c4 !important;
            //     padding: 10px !important;
            //     margin: 10px 0 !important;
            //   }
            // `;
            // editor.editorDocument.head.appendChild(style);
          
            
            // editor.events.on('beforeInit', () => {
            //   if(div.parentNode){
            //     div.parentNode.add(style)
            //   }
            // });
            Swal.fire({
              title: 'Warning',
              text: 'Related Search 2 div already exists!',
              icon: 'warning',
              confirmButtonText: 'OK'
            });
            return;
          }
          const div = editor.createInside.element('div');
          div.id = 'relatedsearches2';
          
          // const style = document.createElement('style');
          // style.innerHTML = `
          //   #relatedsearches2 {
          //     background-color: #fff9c4 !important;
          //     padding: 10px !important;
          //     margin: 10px 0 !important;
          //   }
          // `;
          // editor.editorDocument.head.appendChild(style);
          
          editor.s.insertNode(div);
          editor.selection.select(div);
         
          
        },
      }
    ],
    style: {
      overflowY: "auto",
    },  
    events: {
      afterInit: (editor) => {
        const style = editor.editorDocument.createElement('style');
        style.innerHTML = `
          #relatedsearches2 {
            background-color: #fff9c4 !important;
            padding: 10px !important;
            margin: 10px 0 !important;
          }
        `;
        editor.editorDocument.head.appendChild(style);
        editor.events.on('beforeDestruct', () => {
          if (style.parentNode) {
            style.parentNode.removeChild(style);
          }
        });
      }
    }
  }), [selectedDomain]);

  const handleSecondRelatedSearchToggle = () => {
    setShowSecondRelatedSearch(!showSecondRelatedSearch);
    setFormData((prev) => ({
      ...prev,
      adrelatedsearches2: { val: "", err: null },
    }));
  };
  const [currentUrl, setCurrentUrl] = useState('');

  useEffect(() => {
    setCurrentUrl(window.location.href);
  }, []);
  const handleFormSubmitWithValidation = (e) => {
    e.preventDefault();
    if (showSecondRelatedSearch && formData.adrelatedsearches2?.val) {
      if (!description.includes('id="relatedsearches2"')) {
        Swal.fire({
          icon: "error",
          title: "Validation Error",
          text: "Related Search 2 div is missing in the description. Please add it using the editor button.",
        });
        return;
      }
    }
    handleFormSubmit(e);
  };

  const renderSlugField = () => (
    <div className="mb-4">
      <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
        URL
      </label>
      <div className="flex flex-col gap-2 lg:flex-row lg:items-start">
        <div className="flex flex-1 flex-wrap items-center gap-2">
          <span className="rounded-md border border-stroke bg-gray-100 px-4 py-3 text-dark dark:border-dark-3 dark:bg-dark-2 dark:text-white whitespace-nowrap">
            {selectedDomain
              ? `https://${
                  selectedSubdomain
                    ? `${subdomains.find((s) => s.Id === selectedSubdomain)?.Url || ""}.`
                    : ""
                }${domains.find((d) => d.Id === selectedDomain)?.ShowUrlName || ""}/`
              : `${currentUrl || ""}/`}
          </span>
          <input
            type="text"
            name="url"
            value={formData.url.val.split("/").pop()}
            onChange={(e) => {
              const slug = e.target.value
                .replace(/[^\p{L}\p{N}\s-]/gu, '')
                .trim()
                .replace(/\s+/g, '-');
  
              handleChange({
                target: {
                  name: "url",
                  value: slug
                }
              });
            }}
            className="min-w-[200px] flex-1 rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
            readOnly={mode === "edit" && !editSlugMode}
          />
        </div>
        {mode === "edit" && (
          <div className="lg:ml-4 mt-2 lg:mt-0">
            <Checkbox
              label="Edit Slug"
              name="editSlug"
              checked={editSlugMode}
              onChange={() => setEditSlugMode(!editSlugMode)}
              withIcon="check"
              withBg
              radius="default"
            />
          </div>
        )}
      </div>
      {formData.url.err && (
        <p className="mt-1 text-sm text-red-500">{formData.url.err}</p>
      )}
    </div>
  );
  
  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason === "backdropClick") return;
        onClose();
      }}
      disableEnforceFocus
      fullWidth 
      maxWidth="xl"
      slotProps={{
        paper: { sx: { maxHeight: "90vh" } },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {mode === "edit" ? "Edit Article" : mode === "clone" ? "Clone Article" : "Add Article"}
        </span>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ color: "white" }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form onSubmit={handleFormSubmitWithValidation} className="flex flex-1 flex-col">
         {/* First Row: Domain, Subdomain, Category */}
         <div className="mb-4 flex flex-col gap-4 lg:flex-row">
            <div className="z-50 w-full lg:w-1/3">
              <SearchableDropdown
                label="Domain"
                options={domains}
                placeholder="Select Domain..."
                value={selectedDomain}
                onChange={(domain) => {
                  setSelectedDomain(domain?.Id || "");
                  setSelectedSubdomain("");
                  setSelectedCampaignIds("")
                  setFormData((prev) => ({
                    ...prev,
                    domain: {
                      val: domain?.Id || "",
                      err: validateField("domain", domain?.Id || "", showAds),
                    },
                    subdomain: { val: "", err: "" },
                    category: { val: "", err: "" },
                  }));
                }}
                error={formData.domain.err}
                displayKey="Name"
                idKey="Id"
                required
              />
            </div>
            <div className="z-40 w-full lg:w-1/3">
              <SearchableDropdown
                label="Subdomain"
                options={subdomains}
                placeholder="Select Subdomain..."
                value={selectedSubdomain}
                onChange={(subdomain) => {
                  setSelectedSubdomain(subdomain?.Id || "");
                  setFormData((prev) => ({
                    ...prev,
                    subdomain: {
                      val: subdomain?.Id || "",
                      err: showAds ? validateField("subdomain", subdomain?.Id || "", showAds) : "",
                    },
                  }));
                }}
                error={formData.subdomain.err}
                displayKey="Name"
                idKey="Id"
                disabled={!selectedDomain || !subdomains.length}
                required={showAds}
              />
            </div>
            <div className="w-full lg:w-1/3">
              <SearchableDropdown
                label="Category"
                options={categories}
                placeholder="Select Category..."
                value={formData.category.val}
                onChange={(category) => {
                  setFormData((prev) => ({
                    ...prev,
                    category: {
                      val: category?.Id || "",
                      err: validateField("category", category?.Id || "", showAds),
                    },
                  }));
                }}
                error={formData.category.err}
                displayKey="Name"
                idKey="Id"
                required
              />
            </div>
          </div>

          {/* Second Row: Title and Short Name */}
          <div className="mb-4 flex flex-col gap-4 lg:flex-row">
            <div className="w-full lg:w-1/2">
              <InputGroup
                label="Article Title"
                name="title"
                type="text"
                placeholder="Enter Article Title"
                value={formData.title.val}
                handleChange={handleChange}
                required
                className="w-full"
                active={!!formData.title.err}
              />
              {formData.title.err && (
                <p className="mt-1 text-sm text-red-500">{formData.title.err}</p>
              )}
            </div>
            <div className="w-full lg:w-1/2">
              <InputGroup
                label="Short Name"
                name="shortName"
                value={formData.shortName?.val || ""}
                handleChange={handleChange}
                placeholder="Enter Short Name"
              />
              {formData.shortName?.err && (
                <p className="mt-1 text-sm text-red-500">{formData.shortName.err}</p>
              )}
            </div>
          </div>

          {/* Third Row: URL Slug */}
          {renderSlugField()}
          <div className="mb-4">
            <TextAreaGroup
              label="Short Description"
              name="shortDescription"
              value={shortDescription}
              handleChange={(e) => {
                setShortDescription(e.target.value);
                setFormData((prev) => ({
                  ...prev,
                  shortDescription: {
                    val: e.target.value,
                    err: validateField("shortDescription", e.target.value, showAds),
                  },
                }));
              }}
              placeholder="Enter Short Description"
              rows={4}
              required
            />
            {formData.shortDescription.err && (
              <p className="mt-1 text-sm text-red-500">{formData.shortDescription.err}</p>
            )}
          </div>
          <div className="mb-4">
            <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
              Description <span className="text-red-500">*</span>
            </label>
            <JoditEditor
              value={description}
              config={joditConfig}
              onBlur={(newContent) => {
                setDescription(newContent);
                setFormData(prev => ({
                  ...prev,
                  description: {
                    val: newContent,
                    err: validateField("description", newContent, showAds),
                  },
                }));
              }}
              onChange={(newContent) => {
                // setDescription(newContent);
              }}
            />
            {formData.description.err && (
              <p className="mt-1 text-sm text-red-500">{formData.description.err}</p>
            )}
          </div>
          <div className="mb-4 flex flex-col gap-4 sm:flex-row">
            <div className="w-full sm:w-1/3">
              <TextAreaGroup
                label="Meta Title"
                name="metatitle"
                value={formData.metatitle.val}
                handleChange={handleChange}
                placeholder="Enter Meta Title"
                rows={2}
              />
            </div>
            <div className="w-full sm:w-1/3">
              <TextAreaGroup
                label="Meta Description"
                name="metadescription"
                value={formData.metadescription.val}
                handleChange={handleChange}
                placeholder="Enter Meta Description"
                rows={2}
              />
            </div>
            <div className="w-full sm:w-1/3">
              <TextAreaGroup
                label="Meta Keys"
                name="metakeys"
                value={formData.metakeys.val}
                handleChange={handleChange}
                placeholder="Enter Meta Keys"
                rows={2}
              />
            </div>
          </div>
          <div className="mb-4">
            <TextAreaGroup
              label="Hashtag"
              name="hashtag"
              value={formData.hashtag.val}
              handleChange={(e) =>
                handleChange({
                  target: {
                    name: "hashtag",
                    value: e.target.value.replace(/[ ,]/g, "#"),
                  },
                })
              }
              placeholder="Enter Hashtags"
              rows={2}
            />
          </div>
          <div className="mb-4">
            <div className="flex items-center gap-2">
              <Checkbox
                label="Show Ads"
                name="showAds"
                checked={showAds}
                onChange={() => {
                  setShowAds(!showAds);
                  if (!showAds) {
                    setFormData((prev) => ({
                      ...prev,
                      // customChannal: { val: "", err: "" },
                      // styleIdLm: { val: "", err: "" },
                      // styleIdDm: { val: "", err: "" },
                      // campaigns: { val: [], err: "" },
                      adrelatedsearches: { val: "7", err: "" },
                      adrelatedsearches2: { val: "", err: "" },
                      subdomain: { val: prev.subdomain.val, err: validateField("subdomain", prev.subdomain.val, true) },
                    }));
                    setSelectedCampaignIds([]);
                    setShowSecondRelatedSearch(false);
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      subdomain: { val: prev.subdomain.val, err: "" },
                    }));
                  }
                }}
                withIcon="check"
                withBg
                radius="default"
              />
            </div>
          </div>
          {showAds && (
            <>
              <div className="mb-4 flex flex-col gap-4 lg:flex-row">
                <div className="flex gap-4 w-full">
                  <div className="z-50 w-full">
                    <SearchableDropdown
                      label="Light Mode Style ID"
                      options={styleIds}
                      placeholder="Select Light Mode Style ID..."
                      value={formData.styleIdLm.val}
                      onChange={(style) => {
                        setFormData((prev) => ({
                          ...prev,
                          styleIdLm: {
                            val: style?.Id || "",
                            err: validateField("styleIdLm", style?.Id || "", showAds),
                          },
                        }));
                      }}
                      error={formData.styleIdLm.err}
                      displayKey="Name"
                      idKey="Id"
                    />
                  </div>
                  {/* <div className="z-50 w-1/2">
                    <SearchableDropdown
                      label="Dark Mode Style ID"
                      options={styleIds}
                      placeholder="Select Dark Mode Style ID..."
                      value={formData.styleIdDm.val}
                      onChange={(style) => {
                        setFormData((prev) => ({
                          ...prev,
                          styleIdDm: {
                            val: style?.Id || "",
                            err: validateField("styleIdDm", style?.Id || "", showAds),
                          },
                        }));
                      }}
                      error={formData.styleIdDm.err}
                      displayKey="Name"
                      idKey="Id"
                    />
                  </div> */}
                </div>
                <div className="z-40 w-full">
                  <SearchableDropdown
                    label="Channel Settings"
                    options={assignChannels}
                    placeholder="Select Channel..."
                    value={formData.customChannal.val}
                    onChange={handleChannelSelect}
                    error={formData.customChannal.err}
                    displayKey="DisplayName"
                    idKey="Id"
                    required
                  />
                </div>
              </div>
              <div className="z-30 mb-4">
                <MultiSelectDropdown
                  label="Campaigns"
                  options={campaigns}
                  value={selectedCampaignIds}
                  onChange={handleCampaignSelect}
                  placeholder="Search Campaigns..."
                  displayKey="Name"
                  idKey="SNo"
                  showSelectAll={true}
                  error={formData.campaigns.err}
                />
              </div>
              <div className="mb-4">
                <InputGroup
                  label="Ad Related Searches"
                  name="adrelatedsearches"
                  value={formData.adrelatedsearches.val}
                  handleChange={handleChange}
                  placeholder="Enter number of ads"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center gap-2 mb-2">
                  <Checkbox
                    label="Add 2nd Related Search"
                    name="addSecondRelatedSearch"
                    checked={showSecondRelatedSearch}
                    onChange={handleSecondRelatedSearchToggle}
                    withIcon="check"
                    withBg
                    radius="default"
                  />
                </label>
                {showSecondRelatedSearch && (
                  <InputGroup
                    label="2nd Ad Related Searches"
                    name="adrelatedsearches2"
                    value={formData.adrelatedsearches2?.val || ""}
                    handleChange={handleChange}
                    placeholder="Enter number of 2nd related ads"
                    required={showSecondRelatedSearch}
                  />
                )}
                {showSecondRelatedSearch && formData.adrelatedsearches2?.err && (
                  <p className="mt-1 text-sm text-red-500">{formData.adrelatedsearches2.err}</p>
                )}
              </div>
            </>
          )}
          <div className="mb-4">
            <InputGroup
              label="Read Time (minutes)"
              name="readTime"
              value={formData.readTime.val}
              handleChange={handleChange}
              placeholder="Enter read time in minutes"
              required
              min="0"
            />
            {formData.readTime.err && (
              <p className="mt-1 text-sm text-red-500">{formData.readTime.err}</p>
            )}
          </div>
          <div className="mb-4">
            <TextAreaGroup
              label="Remark"
              name="remark"
              value={formData.remark.val}
              handleChange={handleChange}
              placeholder="Enter Remark"
              rows={3}
            />
          </div>
          <div className="mb-4">
            <InputGroup
              type="file"
              name="Image"
              handleChange={handleChange}
              fileStyleVariant="style1"
              accept=".jpg,.jpeg,.png,.webp,.gif" 
              label="Image"
              placeholder="Image"
            />
            {(base64Image || (typeof formdataImage === "string" && formdataImage)) && (
              <div className="mt-4">
                <img
                  src={base64Image || formdataImage}
                  alt="Preview"
                  className="h-32 w-32 rounded object-cover"
                  width={128}
                  height={128}
                  onError={(e) => {
                    if (formdataImage?.includes("_small")) {
                      setFormdataImage(formdataImage.replace("_small", ""));
                    }
                  }}
                />
              </div>
            )}
          </div>
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <label className="flex items-center gap-2">
              <Checkbox
                label="Published"
                name="published"
                checked={published}
                onChange={() => setPublished(!published)}
                withIcon="check"
                withBg
                radius="default"
              />
            </label>
            <label className="flex items-center gap-2">
              <Checkbox
                label="Show Article"
                name="showArticle"
                checked={showArticle}
                onChange={() => setShowArticle(!showArticle)}
                withIcon="check"
                withBg
                radius="default"
              />
            </label>
            <label className="flex items-center gap-2">
              <Checkbox
                label="Show Article in Main Domain"
                name="showInDomain"
                checked={showInDomain}
                onChange={() => setShowInDomain(!showInDomain)}
                withIcon="check"
                withBg
                radius="default"
              />
            </label>
          </div>
        </form>
      </DialogContent>

      <DialogActions sx={{ py: 2, px: 3 }}>
        <Button
          type="button"
          label="Cancel"
          onClick={onClose}
          variant="dark"
          shape="rounded"
        />
        <Button
          type="submit"
          label={mode === "edit" ? "Update Article" : mode === "clone" ? "Clone Article" : "Add Article"}
          variant="primary"
          shape="rounded"
          disabled={showLoader}
          onClick={handleFormSubmitWithValidation}
        />
      </DialogActions>
    </Dialog>
  );
};

export default ArticleModal;