import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function POST(req: NextRequest) {
    try {
        // type AuthenticatedUser = {
        //     Id: string;
        // };

        // const user = await verifyToken(req) as AuthenticatedUser;
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const { StyleId, Name, AssignUsers = [] } = await req.json(); // Default to empty array if not provided

        if (!StyleId || !Name) {
            return NextResponse.json(
                { error: "StyleId and Name are required" },
                { status: 400 }
            );
        }

        // Validate AssignUsers is an array (now with default empty array)
        if (!Array.isArray(AssignUsers)) {
            return NextResponse.json(
                { error: "AssignUsers must be an array" },
                { status: 400 }
            );
        }

        // Check if StyleId already exists
        const existingStyle = await prisma.styleIds.findFirst({
            where: {
                StyleId,
                IsDeleted: false
            }
        });

        if (existingStyle) {
            return NextResponse.json(
                { error: "StyleId already exists" },
                { status: 409 }
            );
        }

        // Only validate users if array is not empty
        if (AssignUsers.length > 0) {
            const userIds = AssignUsers;
            const existingUsers = await prisma.adminUser.findMany({
                where: {
                    Id: {
                        in: userIds
                    },
                    IsDeleted: false
                },
                select: {
                    Id: true
                }
            });

            if (existingUsers.length !== userIds.length) {
                const missingUsers = userIds.filter(id =>
                    !existingUsers.some(user => user.Id === id)
                );
                return NextResponse.json(
                    {
                        error: "One or more user IDs are invalid",
                        details: {
                            invalidUserIds: missingUsers
                        }
                    },
                    { status: 400 }
                );
            }
        }

        const result = await prisma.$transaction(async (prismaTransaction) => {
            // Create the new style
            const newStyleId = await prismaTransaction.styleIds.create({
                data: {
                    StyleId,
                    Name,
                    CreatedBy: userId
                },
                select: {
                    Id: true,
                    StyleId: true,
                    Name: true
                }
            });

            // Create mappings only if users were provided
            const createdMappings = [];
            if (AssignUsers.length > 0) {
                for (const userId of AssignUsers) {
                    const mapping = await prismaTransaction.styleIdUserMappings.create({
                        data: {
                            StyleId: newStyleId.Id,
                            UserId: userId
                        }
                    });
                    createdMappings.push(mapping);
                }
            }

            return {
                styleId: newStyleId,
                mappings: createdMappings
            };
        });

        return NextResponse.json(
            {
                success: true,
                message: AssignUsers.length > 0
                    ? "StyleId and user mappings created successfully"
                    : "StyleId created successfully",
                data: {
                    styleId: result.styleId,
                    mappingsCount: result.mappings.length
                }
            },
            { status: 201 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : undefined
            },
            { status: 500 }
        );
    }
}