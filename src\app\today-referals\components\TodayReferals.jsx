"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import { Button } from "@/components/ui-elements/button";
import Swal from "sweetalert2";
import apiClient from "@/utils/axiosInstance";
import { FaFilter } from "react-icons/fa";
import { CircularProgress } from "@mui/material";

const TodayReferals = () => {
  const [showLoader, setShowLoader] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("Impression");
  const [data, setData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [domains, setDomains] = useState([]);
  const [subdomains, setSubdomains] = useState([]);
  const [selectedDomainId, setSelectedDomainId] = useState("");
  const [selectedSubdomainId, setSelectedSubdomainId] = useState("");
  const [selectedBreakPoints, setSelectedBreakPoints] = useState([]);
  const [appliedBreakPoints, setAppliedBreakPoints] = useState([]);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  const dataTableRef = useRef();

  const breakPointOptions = [
    { value: "Domain", label: "Domain" },
    { value: "Url", label: "Url" },
  ];

  const getColumns = useCallback(() => {
  const allColumns = [
    {
      id: "Domain",
      label: "Domain",
      sortable: false,
    },
    {
      id: "ReferalDomain",
      label: "Referral Domain",
      sortable: false,
      renderCell: (row) => {
        const fullUrl = `https://${row.ReferalDomain}`;
        return (
          <a
            href={fullUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 underline transition-colors hover:text-blue-800"
            onClick={(e) => e.stopPropagation()}
          >
            {row.ReferalDomain}
          </a>
        );
      },
    },
    {
      id: "Impression",
      label: "Impression",
      sortable: false,
      numeric: true,
    },
    {
      id: "Url",
      label: "Url",
      sortable: false,
      renderCell: (row) => {
          // const fullUrl = `https://${row.Domain}${row.Url}`;
        const fullUrl = row.Domain ? `https://${row.Domain}${row.Url}` : `${row.Url}`;
        return (
          <a
            href={fullUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 underline transition-colors hover:text-blue-800"
            onClick={(e) => e.stopPropagation()}
          >
            {row.Url}
          </a>
        );
      },
    },
  ];

    return allColumns.filter((column) =>
      column.id === "ReferalDomain" || 
      column.id === "Impression" || 
      appliedBreakPoints.includes(column.id)
    );
  }, [appliedBreakPoints]);

  const fetchDomains = useCallback(async () => {
    try {
      const response = await apiClient.get("/Domain/GetDropDown");
      if (response?.status === 200) {
        setDomains([{ Id: "", Name: "All Domains" }, ...response?.data?.data]);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Error fetching domains");
      setDomains([]);
    }
  }, []);

  const fetchSubdomains = useCallback(async (domainId) => {
    try {
      const url = domainId
        ? `/SubDomain/GetDropDown?DomainId=${domainId}`
        : `/SubDomain/GetDropDown?`;
      const response = await apiClient.get(url);
      if (response?.status === 200) {
        setSubdomains([
          { Id: "", Name: "All Subdomains" },
          ...response?.data?.data,
        ]);
      } else {
        setSubdomains([]);
      }
    } catch (error) {
      console.error(error?.response?.data?.error || "Error fetching subdomains");
      setSubdomains([]);
    }
  }, []);

  // Fixed API call logic
  const fetchReferrals = useCallback(async () => {
    try {
      setShowLoader(true);

      let breakPointsToSend = "";

      // Filter out any "SelectAll" values and get only actual breakpoint values
      const actualBreakPoints = selectedBreakPoints.filter(
        bp => bp !== "SelectAll" && breakPointOptions.some(opt => opt.value === bp)
      );

      if (actualBreakPoints.length > 0) {
        breakPointsToSend = actualBreakPoints.join(",");
      }
      // If no breakpoints selected, don't send the parameter at all

      const params = new URLSearchParams({
        orderBy: orderBy,
        orderDir: order,
      });

      if (breakPointsToSend) {
        params.append("breakpoint", breakPointsToSend);
      }

      if (selectedDomainId) {
        params.append("domainId", selectedDomainId);
      }

      if (selectedSubdomainId) {
        params.append("subDomainId", selectedSubdomainId);
      }

      const response = await apiClient.get(`/TodayReferals?${params}`);
      const responseData = response.data.data || [];

      if (responseData.length === 0) {
        await Swal.fire({
          icon: "info",
          title: "No Data Found",
          text: "No referral data available for the selected filters",
          timer: 3000,
          showConfirmButton: false,
        });
        setData([]);
        setTotalCount(0);
      } else {
        setData(responseData);
        setTotalCount(responseData.length);
      }
    } catch (error) {
      console.error("Error fetching referral data:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load today's referrals",
        timer: 3000,
        showConfirmButton: false,
      });
      setData([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [order, orderBy, selectedDomainId, selectedSubdomainId, selectedBreakPoints]);

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
    setPage(0);
  };

  const handleBreakPoints = (selectedValues) => {
    // If all options are selected, add "SelectAll" marker
    const allSelected = selectedValues.length === breakPointOptions.length;
    
    if (allSelected) {
      setSelectedBreakPoints(["SelectAll", ...breakPointOptions.map(opt => opt.value)]);
    } else {
      // Remove "SelectAll" if it exists and not all are selected
      const filteredValues = selectedValues.filter(value => value !== "SelectAll");
      setSelectedBreakPoints(filteredValues);
    }
    setPage(0);
  };

  const handleApply = () => {
    setPage(0);
    
    // Update applied breakpoints - filter out "SelectAll" and keep only actual values
    const actualBreakPoints = selectedBreakPoints.filter(
      bp => bp !== "SelectAll" && breakPointOptions.some(opt => opt.value === bp)
    );
    setAppliedBreakPoints(actualBreakPoints);
    
    fetchReferrals();
  };

  // Clear the "1 item selected" issue by ensuring proper display values
  const getDisplayValue = () => {
    const actualBreakPoints = selectedBreakPoints.filter(
      bp => bp !== "SelectAll" && breakPointOptions.some(opt => opt.value === bp)
    );
    
    if (actualBreakPoints.length === 0) {
      return [];
    }
    
    // If "SelectAll" is present, return all options for display
    if (selectedBreakPoints.includes("SelectAll")) {
      return breakPointOptions.map(opt => opt.value);
    }
    
    return actualBreakPoints;
  };

  useEffect(() => {
    fetchDomains();
  }, [fetchDomains]);

  useEffect(() => {
    if (selectedDomainId) {
      fetchSubdomains(selectedDomainId);
    } else {
      setSubdomains([{ Id: "", Name: "All Subdomains" }]);
      setSelectedSubdomainId("");
    }
  }, [selectedDomainId, fetchSubdomains]);

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">Today's Referrals</h1>
          </div>
        </div>

        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6">
          <div className="mb-3 flex flex-col gap-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 2xl:grid-cols-4 items-end">
              <div className="w-full z-50">
                <MultiSelectDropdown
                  label="Breakpoints"
                  placeholder="Select Breakpoints"
                  options={breakPointOptions}
                  value={getDisplayValue()} // Use the filtered display value
                  onChange={handleBreakPoints}
                  displayKey="label"
                  idKey="value"
                  className="w-full"
                />
              </div>
              <div className="w-full z-40">
                <SearchableDropdown
                  label="Domain"
                  placeholder="Select Domain"
                  options={domains}
                  value={selectedDomainId}
                  onChange={(item) => {
                    setSelectedDomainId(item?.Id || "");
                    setSelectedSubdomainId("");
                    setPage(0);
                  }}
                  className="w-full"
                  displayKey="Name"
                  displayKey2="ShowUrlName"
                  idKey="Id"
                />
              </div>
              <div className="w-full z-30">
                <SearchableDropdown
                  label="Subdomain"
                  placeholder={
                    selectedDomainId
                      ? "Select Subdomain"
                      : "Select a domain first"
                  }
                  options={subdomains}
                  value={selectedSubdomainId}
                  onChange={(item) => {
                    setSelectedSubdomainId(item?.Id || "");
                    setPage(0);
                  }}
                  displayKey="Name"
                  idKey="Id"
                  disabled={!selectedDomainId}
                  className="w-full"
                />
              </div>
              <div className="flex items-center gap-2 z-20">
                <Button
                  label="Apply"
                  variant="primary"
                  shape="rounded"
                  className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-6 py-2 font-medium text-white hover:bg-blue-700 disabled:opacity-50 w-full"
                  onClick={handleApply}
                  disabled={showLoader}
                />
              </div>
            </div>
          </div>
          {showLoader && data?.length === 0 && (
            <div className="mt-6 flex items-center justify-center">
              <CircularProgress size={40} />
            </div>
          )}
        {data?.length > 0 && (
          <CustomDataTable
            isLoading={showLoader}
            ref={dataTableRef}
            columns={getColumns()}
            rows={data}
            page={page}
            rowsPerPage={rowsPerPage}
            totalCount={totalCount}
            order={order}
            notShowAction={true}
            orderBy={orderBy}
            onRequestSort={handleRequestSort}
            showPagination={false}
          />
          )}
        </div>
      </div>
    </>
  );
};

export default TodayReferals;