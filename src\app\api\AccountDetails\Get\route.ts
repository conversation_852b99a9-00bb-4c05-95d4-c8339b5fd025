import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

const bigIntReplacer = (key: string, value: any) => {
    return typeof value === 'bigint' ? value.toString() : value;
};

export async function GET(req: NextRequest) {
    try {
       
        // const user = await verifyToken(req);
        // if (!userId) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get('page') || '1');
        const length = parseInt(searchParams.get('length') || '10');
        const search = searchParams.get('q')?.toLowerCase();
        const orderBy = searchParams.get('orderBy') || 'DescriptiveName';
        const orderDir = (searchParams.get('orderDir') || 'asc').toLowerCase() as 'asc' | 'desc';

        let skip: number | undefined;
        let limit: number | undefined;

        if (length === -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start === 1 ? 0 : start - 1) * length;
            limit = length;
            skip = skipCount > 0 ? skipCount : 0;
        }

        // Build where condition for search
        const whereCondition = search
            ? {
                OR: [
                    {
                        DescriptiveName: {
                            contains: search,
                            mode: 'insensitive' as const
                        }
                    },
                    ...(isNaN(Number(search))
                        ? []
                        : [{
                            AccountId: {
                                equals: BigInt(search)
                            }
                        }]),
                ],
            }
            : undefined;

        // Handle orderBy for both string and BigInt fields
        const orderByCondition = orderBy === 'AccountId'
            ? { AccountId: orderDir }
            : orderBy === 'DescriptiveName'
                ? { DescriptiveName: orderDir }
                : undefined;

        const users = await prisma.ads_AccountDetails.findMany({
            where: whereCondition,
            skip,
            take: limit,
            orderBy: orderByCondition,
            select: {
                AccountId: true,
                DescriptiveName: true,
            },
        });

        // Count query for filtered records
        const recordsFiltered = await prisma.ads_AccountDetails.count({
            where: whereCondition,
        });

        // Convert BigInt to string in the response
        const responseData = {
            success: true,
            data: users.map(user => ({
                ...user,
                AccountId: user.AccountId.toString()
            })),
            pagination: {
                draw: parseInt(searchParams.get('draw') || '1'),
                recordsFiltered,
                currentPageCount: users.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            },
        };

        return new NextResponse(JSON.stringify(responseData, bigIntReplacer), {
            headers: {
                'Content-Type': 'application/json'
            }
        });

    } catch (error) {
        console.error('Error fetching users:', error);
        return NextResponse.json(
            { error: 'Failed to fetch users' },
            { status: 500 }
        );
    }
}