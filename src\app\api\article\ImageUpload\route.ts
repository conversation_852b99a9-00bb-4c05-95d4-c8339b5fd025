import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

export async function POST(req: NextRequest) {
    // type AuthenticatedUser = {
    //     Id: string;
    // };

    // const user = await verifyToken(req) as AuthenticatedUser;
    // if (!user) {
    //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const formData = await req.formData();
    const file = formData.get('Image') as File | null;
    const domainId = formData.get('domainId') as string;

    if (!domainId) {
        throw new Error(" DomainId is required.");
    }

    if (!file) {
        return NextResponse.json({
            error: 'No file provided'
        }, { status: 400 });
    }

    let imagePath: string | null = null;
    let uniqueId: string | null = null;

    try {
        const domain = await prisma.domain.findUnique({
            where: { Id: domainId },
            select: { Prefix: true }
        });

        if (!domain) {
            return NextResponse.json({
                error: 'Domain not found'
            }, { status: 404 });
        }

        const domainPrefix = domain.Prefix?.toLowerCase();
        const buffer = Buffer.from(await file.arrayBuffer());
        const mimeType = file.type;

        if (!mimeType.startsWith('image/')) {
            return NextResponse.json({
                error: 'Invalid file type. Only images are allowed.'
            }, { status: 400 });
        }

        const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
        uniqueId = uuidv4();

        const uploadPath = domainPrefix 
            ? path.resolve(UPLOAD_DIR, domainPrefix)
            : UPLOAD_DIR;

        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }

        const originalBuffer = await sharp(buffer).toBuffer();

        const baseFileName = `${uniqueId}.${originalExtension}`;
        const filePath = path.resolve(uploadPath, baseFileName);
        await fs.promises.writeFile(filePath, originalBuffer);

        if (domainPrefix) {
            imagePath = `/uploads/${domainPrefix}/${baseFileName}`;
        } else {
            imagePath = `/uploads/${baseFileName}`;
        }

        return NextResponse.json({
            success: true,
            message: 'Image uploaded successfully',
            imagePath: imagePath,
        }, { status: 201 });

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
        console.error("Image processing error:", errorMessage);

        if (uniqueId) {
            try {
                const domain = await prisma.domain.findUnique({
                    where: { Id: domainId },
                    select: { Prefix: true }
                });
                
                const domainPrefix = domain?.Prefix;
                const cleanupPath = domainPrefix 
                    ? path.resolve(UPLOAD_DIR, domainPrefix)
                    : UPLOAD_DIR;

                const fileToDelete = `${uniqueId}.jpg`;
                const filePath = path.resolve(cleanupPath, fileToDelete);
                await fs.promises.unlink(filePath).catch(() => { }); 
            } catch (cleanupError) {
                console.error("File cleanup error:", cleanupError);
            }
        }

        return NextResponse.json(
            { error: "Failed to process image", details: errorMessage },
            { status: 500 }
        );
    }
}