"use client";

import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui-elements/button';

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'green' | 'danger' | 'dark';
    icon?: React.ReactNode;
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  className = '',
  size = 'md',
}) => {
  const sizeClasses = {
    sm: {
      container: 'py-8',
      icon: 'text-4xl mb-3',
      title: 'text-lg',
      description: 'text-sm',
    },
    md: {
      container: 'py-12',
      icon: 'text-6xl mb-4',
      title: 'text-xl',
      description: 'text-base',
    },
    lg: {
      container: 'py-16',
      icon: 'text-8xl mb-6',
      title: 'text-2xl',
      description: 'text-lg',
    },
  };

  return (
    <div className={cn(
      'flex flex-col items-center justify-center text-center',
      sizeClasses[size].container,
      className
    )}>
      {icon && (
        <div className={cn(
          'text-gray-400 dark:text-gray-600',
          sizeClasses[size].icon
        )}>
          {icon}
        </div>
      )}
      
      <h3 className={cn(
        'font-semibold text-gray-900 dark:text-white mb-2',
        sizeClasses[size].title
      )}>
        {title}
      </h3>
      
      {description && (
        <p className={cn(
          'text-gray-600 dark:text-gray-400 max-w-md mx-auto',
          sizeClasses[size].description,
          action ? 'mb-6' : ''
        )}>
          {description}
        </p>
      )}
      
      {action && (
        <Button
          label={action.label}
          variant={action.variant || 'primary'}
          shape="rounded"
          icon={action.icon}
          onClick={action.onClick}
        />
      )}
    </div>
  );
};

export default EmptyState;
