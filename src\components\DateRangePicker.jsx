// "use client";
// import React, { useRef, useEffect, useState } from "react";
// import { Calendar } from "@/components/Layouts/sidebar/icons";
// import { Button } from "@/components/ui-elements/button";
// import { formatDate } from "@/utils/functions";
// import flatpickr from "flatpickr";
// import "flatpickr/dist/flatpickr.min.css";

// const DateRangePicker = ({ 
//   startDate, 
//   endDate, 
//   onDateChange, 
//   onQuickDateSelect,
//   label = "Date Range",
//   placeholder = "Select date range"
// }) => {
//   const datePickerRef = useRef(null);
//   const [showDateModal, setShowDateModal] = useState(false);

//   // Initialize flatpickr for date range picker in modal
//   useEffect(() => {
//     if (showDateModal && datePickerRef.current) {
//       flatpickr(datePickerRef.current, {
//         mode: "range",
//         dateFormat: "d-m-Y",
//         defaultDate: [startDate, endDate],
//         inline: true,
//         onChange: (selectedDates) => {
//           if (selectedDates.length === 2) {
//             onDateChange(selectedDates);
//           }
//         }
//       });
//     }
//   }, [showDateModal, startDate, endDate, onDateChange]);

//   const handleQuickDate = (range) => {
//     onQuickDateSelect(range);
//     setShowDateModal(false);
//   };

//   return (
//     <>
//       <div>
//         <label className="mb-3 block text-sm font-medium text-dark dark:text-white">
//           {label}
//         </label>
//         <div
//           className="flex items-center justify-between w-full rounded-lg border border-stroke bg-transparent px-4 py-3 cursor-pointer hover:border-primary transition-colors dark:border-dark-3 dark:bg-dark-2 dark:hover:border-primary"
//           onClick={() => setShowDateModal(true)}
//         >
//           <span className="text-dark dark:text-white text-sm">
//             {formatDate(startDate)} - {formatDate(endDate)}
//           </span>
//           <Calendar className="size-5 text-gray-400 dark:text-gray-500" />
//         </div>
//       </div>

//       {/* Date Range Modal */}
//       {showDateModal && (
//         <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
//           <div className="bg-white dark:bg-gray-dark rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
//             <div className="flex items-center justify-between p-4 border-b border-stroke dark:border-dark-3">
//               <h3 className="text-lg font-semibold text-dark dark:text-white">
//                 Select Date Range
//               </h3>
//               <button
//                 onClick={() => setShowDateModal(false)}
//                 className="p-1 hover:bg-gray-100 dark:hover:bg-dark-2 rounded"
//               >
//                 <span className="text-xl text-gray-500">×</span>
//               </button>
//             </div>

//             <div className="p-4">
//               {/* Calendar Container */}
//               <div className="mb-4">
//                 <input
//                   ref={datePickerRef}
//                   className="w-full rounded-lg border border-stroke bg-transparent px-4 py-3 outline-none transition focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
//                   placeholder={placeholder}
//                   readOnly
//                 />
//               </div>

//               {/* Quick Date Selection Buttons */}
//               <div className="grid grid-cols-2 gap-2 mb-4">
//                 <button
//                   type="button"
//                   onClick={() => handleQuickDate("today")}
//                   className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary/20"
//                 >
//                   Today
//                 </button>
//                 <button
//                   type="button"
//                   onClick={() => handleQuickDate("yesterday")}
//                   className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary/20"
//                 >
//                   Yesterday
//                 </button>
//                 <button
//                   type="button"
//                   onClick={() => handleQuickDate("last7days")}
//                   className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary/20"
//                 >
//                   Last 7 Days
//                 </button>
//                 <button
//                   type="button"
//                   onClick={() => handleQuickDate("last30days")}
//                   className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary/20"
//                 >
//                   Last 30 Days
//                 </button>
//                 <button
//                   type="button"
//                   onClick={() => handleQuickDate("thismonth")}
//                   className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary/20"
//                 >
//                   This Month
//                 </button>
//                 <button
//                   type="button"
//                   onClick={() => handleQuickDate("lastmonth")}
//                   className="px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary/20"
//                 >
//                   Last Month
//                 </button>
//               </div>

//               {/* Modal Actions */}
//               <div className="flex gap-2 pt-4 border-t border-stroke dark:border-dark-3">
//                 <Button
//                   label="Cancel"
//                   variant="dark"
//                   onClick={() => setShowDateModal(false)}
//                   className="flex-1"
//                 />
//                 <Button
//                   label="Apply"
//                   variant="primary"
//                   onClick={() => setShowDateModal(false)}
//                   className="flex-1"
//                 />
//               </div>
//             </div>
//           </div>
//         </div>
//       )}
//     </>
//   );
// };

// export default DateRangePicker;
"use client";
import React, { useRef, useEffect } from "react";
import { Calendar } from "@/components/Layouts/sidebar/icons";
import { formatDate } from "@/utils/functions";
import flatpickr from "flatpickr";
import "flatpickr/dist/flatpickr.min.css";

const DateRangePicker = ({ 
  startDate, 
  endDate, 
  onDateChange,
  onQuickDateSelect,
  label = "Date Range",
  placeholder = "Select date range",
  showQuickSelectButtons = false // Prop to toggle quick select buttons
}) => {
  const datePickerRef = useRef(null);
  const calendarContainerRef = useRef(null);
  const formatDisplayDate = (dateStr) => {
    return dateStr.split(" to ").map(date => {
      const [day, month] = date.split("-");
      return `${day}-${month}`;
    }).join(" to ");
  };
  useEffect(() => {
    if (datePickerRef.current) {
      const flatpickrInstance = flatpickr(datePickerRef.current, {
        mode: "range",
        dateFormat: "d-m",
        defaultDate: [startDate, endDate],
        maxDate: new Date(), // This restricts dates to today and earlier
        onChange: (selectedDates, dateStr) => {
          if (selectedDates.length === 2) {
            onDateChange(selectedDates);
            datePickerRef.current.value = formatDisplayDate(dateStr);
          }
        },
        onReady: (_, __, instance) => {
          // Store the calendar container for cleanup
          calendarContainerRef.current = instance.calendarContainer;

          // Only add buttons if showQuickSelectButtons is true
          if (showQuickSelectButtons) {
            // Create a container for the buttons
            const buttonContainer = document.createElement("div");
            buttonContainer.className = "grid grid-cols-2 gap-2 mt-4";

            // Define the quick date options
            const quickDates = [
              { label: "Today", value: "today" },
              { label: "Yesterday", value: "yesterday" },
              { label: "Last 7 Days", value: "last7days" },
              { label: "Last 30 Days", value: "last30days" },
              { label: "This Month", value: "thismonth" },
              { label: "Last Month", value: "lastmonth" },
            ];

            // Create buttons dynamically
            quickDates.forEach(({ label, value }) => {
              const button = document.createElement("button");
              button.type = "button";
              button.className = "px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary/20";
              button.textContent = label;
              button.addEventListener("click", () => {
                onQuickDateSelect(value);
                instance.close(); // Close the calendar after selection
              });
              buttonContainer.appendChild(button);
            });

            // Append the button container to the calendar
            instance.calendarContainer.appendChild(buttonContainer);
          }
        },
      });

      return () => {
        flatpickrInstance.destroy();
      };
    }
  }, [startDate, endDate, onDateChange, onQuickDateSelect, showQuickSelectButtons]);

  return (
    <div className="w-full mt-5">
      <div className="relative">
        <input
          ref={datePickerRef}
          className="w-full rounded-lg border border-stroke bg-transparent px-4 py-3 pr-10 outline-none transition focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary text-dark dark:text-white"
          placeholder={placeholder}
          readOnly
        />
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <Calendar className="size-5 text-gray-400 dark:text-gray-500" />
        </div>
      </div>
    </div>
  );
};

export default DateRangePicker;