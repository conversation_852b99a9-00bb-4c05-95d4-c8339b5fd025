import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

interface AdminUserWithCounts {
    Id: string;
    Name: string | null;
    Email: string | null;
    Number: string | null;
    User_Type: string | null;
    Status: boolean | null;
    subDomainCount: number;
    styleIdCount: number;
}

export async function GET(req: NextRequest) {
    try {
     
        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = (searchParams.get("q") || '').trim(); // Trim whitespace from search
        const orderBy = (searchParams.get("orderBy") || "CreatedAt").toLowerCase(); // Convert to lowercase
        const orderDir = searchParams.get("orderDir") || "asc";

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        let where: any = {
            IsDeleted: false,
        };

        // Enhanced search with trimmed input and case-insensitive matching
        if (search) {
            const searchTerm = search.toLowerCase(); // Convert search to lowercase
            where.OR = [
                { Name: { contains: searchTerm, mode: 'insensitive' } },
                { Email: { contains: searchTerm, mode: 'insensitive' } },
                { Number: { contains: searchTerm } }
            ];
        }

        // First get the count of filtered records
        const recordsTotal = await prisma.adminUser.count({
            where: { IsDeleted: false }
        });

        const recordsFiltered = await prisma.adminUser.count({ where });

        const allUsers = await prisma.adminUser.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                Email: true,
                Number: true,
                User_Type: true,
                Block: true,
                Status: true,
                CreatedAt: true,
            }
        });

        let usersWithCounts: AdminUserWithCounts[] = await Promise.all(
            allUsers.map(async (user) => {
                const subDomainCount = await prisma.subDomainUserMappings.count({
                    where: {
                        UserId: user.Id,
                        SubDomain: {
                            IsDeleted: false,
                            Domain_SubDomain_DomainToDomain: {
                                IsDeleted: false,
                            }
                        }
                    }
                });

                const styleIdCount = await prisma.styleIdUserMappings.count({
                    where: {
                        UserId: user.Id,
                        StyleIds: {
                            IsDeleted: false
                        }
                    }
                });

                return {
                    ...user,
                    subDomainCount,
                    styleIdCount
                };
            })
        );

        const allowedFields = ['id', 'name', 'email', 'number', 'user_type', 'status', 'createdat', 'subdomaincount', 'styleidcount'];
        const normalizedOrderBy = orderBy.toLowerCase();

        if (allowedFields.includes(normalizedOrderBy)) {
            usersWithCounts.sort((a, b) => {
                const actualField =
                    normalizedOrderBy === 'createdat' ? 'CreatedAt' :
                        normalizedOrderBy === 'user_type' ? 'User_Type' :
                            normalizedOrderBy === 'subdomaincount' ? 'subDomainCount' :
                                normalizedOrderBy === 'styleidcount' ? 'styleIdCount' :
                                    normalizedOrderBy.charAt(0).toUpperCase() + normalizedOrderBy.slice(1);

                let aValue = a[actualField as keyof AdminUserWithCounts];
                let bValue = b[actualField as keyof AdminUserWithCounts];

                if (aValue === null || aValue === undefined) return orderDir === 'asc' ? 1 : -1;
                if (bValue === null || bValue === undefined) return orderDir === 'asc' ? -1 : 1;

                if (actualField === 'subDomainCount' || actualField === 'styleIdCount') {
                    return orderDir === 'asc'
                        ? (aValue as number) - (bValue as number)
                        : (bValue as number) - (aValue as number);
                }

                else if (actualField === 'Status') {
                    const aBool = aValue as boolean;
                    const bBool = bValue as boolean;
                    return orderDir === 'asc'
                        ? (aBool === bBool ? 0 : aBool ? 1 : -1)
                        : (aBool === bBool ? 0 : aBool ? -1 : 1);
                }
                else if (actualField === 'CreatedAt') {
                    const dateA = new Date(aValue as string).getTime();
                    const dateB = new Date(bValue as string).getTime();
                    return orderDir === 'asc' ? dateA - dateB : dateB - dateA;
                }
                else {
                    const strA = String(aValue).toLowerCase();
                    const strB = String(bValue).toLowerCase();
                    return orderDir === 'asc'
                        ? strA.localeCompare(strB)
                        : strB.localeCompare(strA);
                }
            });
        }

        // Apply pagination after sorting
        const paginatedUsers = length === -1
            ? usersWithCounts
            : usersWithCounts.slice(skip || 0, (skip || 0) + (limit || usersWithCounts.length));

        return NextResponse.json({
            success: true,
            data: paginatedUsers,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                currentPageCount: paginatedUsers.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "User Not Found" },
            { status: 500 }
        );
    }
}