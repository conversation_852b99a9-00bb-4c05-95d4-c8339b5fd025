import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(req: NextRequest) {
    try {
        // type AuthenticatedUser = {
        //     Id: string;
        //     User_Type: string;
        // };

        // const user = await verifyToken(req) as AuthenticatedUser;
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id');
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');
        
        const { searchParams } = new URL(req.url);
        const imPersonateLogin = searchParams.get('imPersonateLogin') === 'true';
        const emailId = searchParams.get('email');

        if (
            imPersonateLogin &&
            emailId &&
            (role.toLowerCase() === 'super admin' || role.toLowerCase() === 'admin')
        ) {
            const targetUser = await prisma.adminUser.findUnique({
                where: { Email: emailId },
                select: {
                    Id: true,
                    Name: true,
                    Email: true,
                    User_Type: true,
                    ProfilePic: true
                }
            });

            if (!targetUser) {
                return NextResponse.json({ error: 'Target user not found' }, { status: 404 });
            }

            const accessToken = jwt.sign(
                {
                    Id: targetUser.Id,
                    Name: targetUser.Name,
                    Email: targetUser.Email,
                    User_Type: targetUser.User_Type,
                    ProfilePic: targetUser.ProfilePic
                },
                JWT_SECRET,
                { expiresIn: '7d' }
            );

            const refreshToken = jwt.sign(
                {
                    Id: targetUser.Id,
                    Name: targetUser.Name,
                    Email: targetUser.Email,
                    User_Type: targetUser.User_Type,
                    ProfilePic: targetUser.ProfilePic
                },
                JWT_SECRET,
                { expiresIn: '7d' }
            );

            const cookieStore = await cookies();
            cookieStore.set('accessToken', accessToken, { httpOnly: true });
            cookieStore.set('refreshToken', refreshToken, { httpOnly: true })

            return NextResponse.json({
                success: true,
                data: accessToken,
                message: `Successfully impersonated ${targetUser.Email}`,
            });
        }

        return NextResponse.json({
            success: true,
            message: 'No impersonation performed'
        });

    } catch (error) {
        console.error('Error in impersonation:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}
