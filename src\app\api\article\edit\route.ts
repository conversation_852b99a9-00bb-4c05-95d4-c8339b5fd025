// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
// import sharp from 'sharp';
// import { v4 as uuidv4 } from 'uuid';
// import fs from 'fs';
// import path from 'path';
// import { Prisma } from '@prisma/client';
// import axios from 'axios';

// const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

// async function checkExistingCombinations(customChannel: string | null, styleIdLm: string | null, styleIdDm: string | null, excludeId?: string) {
//     const existingCombinations = [];

//     // Check CustomChannel + StyleIdLm combination
//     if (customChannel && styleIdLm) {
//         const existingLm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdLm: styleIdLm,
//                 ...(excludeId && { Id: { not: excludeId } }),
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdLm: true,
//                 StyleIdDm: true
//             }
//         });

//         if (existingLm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdLm',
//                 combination: {
//                     customChannel,
//                     styleId: styleIdLm
//                 },
//                 existingArticles: existingLm.map(article => ({
//                     id: article.Id,
//                     title: article.Title,
//                     customChannelId: article.CustomChannal,
//                     styleIdLm: article.StyleIdLm,
//                     styleIdDm: article.StyleIdDm
//                 }))
//             });
//         }

//         const crossLmAsDm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdDm: styleIdLm,
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdLm: true,
//                 StyleIdDm: true
//             }
//         });

//         if (crossLmAsDm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdDm (from styleIdLm)',
//                 combination: { customChannel, styleId: styleIdLm },
//                 existingArticles: crossLmAsDm.map(a => ({
//                     id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
//                 }))
//             });
//         }
//     }

//     // Check CustomChannel + StyleIdDm combination
//     if (customChannel && styleIdDm) {
//         const existingDm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdDm: styleIdDm,
//                 ...(excludeId && { Id: { not: excludeId } }),
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdDm: true,
//                 StyleIdLm: true
//             }
//         });

//         if (existingDm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdDm',
//                 combination: {
//                     customChannel,
//                     styleId: styleIdDm
//                 },
//                 existingArticles: existingDm.map(article => ({
//                     id: article.Id,
//                     title: article.Title,
//                     customChannelId: article.CustomChannal,
//                     styleIdDm: article.StyleIdDm,
//                     styleIdLm: article.StyleIdLm
//                 }))
//             });
//         }

//         const crossDmAsLm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdLm: styleIdDm,
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdLm: true,
//                 StyleIdDm: true
//             }
//         });

//         if (crossDmAsLm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdLm (from styleIdDm)',
//                 combination: { customChannel, styleId: styleIdDm },
//                 existingArticles: crossDmAsLm.map(a => ({
//                     id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
//                 }))
//             });
//         }
//     }

//     return existingCombinations;
// }

// export async function PUT(req: NextRequest) {
//     type AuthenticatedUser = {
//         Id: string;
//     };
//     const user = await verifyToken(req) as AuthenticatedUser;
//     if (!user) {
//         return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//     }

//     try {
//         const formData = await req.formData();

//         const formFields = {
//             Id: formData.get('Id'),
//             Title: formData.get('Title'),
//             Category: formData.get('Category'),
//             Url: formData.get('Url'),
//             Description: formData.get('Description'),
//             ShortName: formData.get('ShortName'),
//             Published: formData.get('Published'),
//             ShowArticle: formData.get('ShowArticle'),
//             ShowsAds: formData.get('ShowsAds'),
//             MetaTitle: formData.get('MetaTitle'),
//             MetaDescription: formData.get('MetaDescription'),
//             MetaKeys: formData.get('MetaKeys'),
//             CustomChannal: formData.get('CustomChannal'),
//             StyleIdLm: formData.get('StyleIdLm'),
//             StyleIdDm: formData.get('StyleIdDm'),
//             AdRelatedSearches: formData.get('AdRelatedSearches'),
//             Remark: formData.get('Remark'),
//             ShortDescription: formData.get('ShortDescription'),
//             Domain: formData.get('Domain'),
//             SubDomain: formData.get('SubDomain'),
//             CampaignIds: formData.get('CampaignIds'),
//             file: formData.get('file'),
//             ReadTime: formData.get('ReadTime'),
//             CheckRelatedSearches2: formData.get('CheckRelatedSearches2'),
//             AdReletadSearches2: formData.get('AdReletadSearches2')
//         };

//         if (!formFields.Id?.toString().trim()) {
//             return NextResponse.json(
//                 { error: "Article ID is required" },
//                 { status: 400 }
//             );
//         }

//         const existingArticle = await prisma.articleDetails.findUnique({
//             where: { Id: formFields.Id.toString() }
//         });

//         if (!existingArticle) {
//             return NextResponse.json(
//                 { error: "Article not found" },
//                 { status: 404 }
//             );
//         }

//         const textFields = {
//             Id: formFields.Id.toString(),
//             Title: formFields.Title !== null ? formFields.Title.toString() : undefined,
//             Category: formFields.Category !== null ? formFields.Category.toString() : undefined,
//             Url: formFields.Url !== null ? (formFields.Url.toString().trim() || null) : undefined,
//             Description: formFields.Description !== null ? (formFields.Description.toString().trim() || null) : undefined,
//             ShortName: formFields.ShortName !== null ? (formFields.ShortName.toString().trim() || null) : undefined,
//             MetaTitle: formFields.MetaTitle !== null ? (formFields.MetaTitle.toString().trim() || null) : undefined,
//             MetaDescription: formFields.MetaDescription !== null ? (formFields.MetaDescription.toString().trim() || null) : undefined,
//             MetaKeys: formFields.MetaKeys !== null ? (formFields.MetaKeys.toString().trim() || null) : undefined,
//             CustomChannal: formFields.CustomChannal !== null ? (formFields.CustomChannal.toString().trim() || null) : undefined,
//             StyleIdLm: formFields.StyleIdLm !== null ? (formFields.StyleIdLm.toString().trim() || null) : undefined,
//             StyleIdDm: formFields.StyleIdDm !== null ? (formFields.StyleIdDm.toString().trim() || null) : undefined,
//             AdRelatedSearches: formFields.AdRelatedSearches !== null ? (formFields.AdRelatedSearches.toString().trim() || null) : undefined,
//             Remark: formFields.Remark !== null ? (formFields.Remark.toString().trim() || null) : undefined,
//             ShortDescription: formFields.ShortDescription !== null ? (formFields.ShortDescription.toString().trim() || null) : undefined,
//             Domain: formFields.Domain !== null ? (formFields.Domain.toString().trim() || null) : undefined,
//             SubDomain: formFields.SubDomain !== null ? (formFields.SubDomain.toString().trim() || null) : undefined,
//             AdReletadSearches2: formFields.AdReletadSearches2 !== null ? (formFields.AdReletadSearches2.toString().trim() || null) : undefined,
//             Published: formFields.Published !== null ? formFields.Published === 'true' : undefined,
//             ShowArticle: formFields.ShowArticle !== null ? formFields.ShowArticle === 'true' : undefined,
//             ShowsAds: formFields.ShowsAds !== null ? formFields.ShowsAds === 'true' : undefined,
//             CheckRelatedSearches2: formFields.CheckRelatedSearches2 !== null ? formFields.CheckRelatedSearches2 === 'true' : undefined,
//             ReadTime: formFields.ReadTime !== null ?
//                 (formFields.ReadTime.toString().trim() === '' ? null :
//                     isNaN(parseInt(formFields.ReadTime.toString())) ? undefined : parseInt(formFields.ReadTime.toString())) : undefined,
//             CampaignIds: formFields.CampaignIds !== null ?
//                 (formFields.CampaignIds.toString().trim() === '' ? [] :
//                     JSON.parse(formFields.CampaignIds.toString() || '[]') as number[]) : undefined,
//         };

//         if (textFields.Title !== undefined && textFields.Title.trim() !== '' && textFields.Title !== existingArticle.Title) {
//             const duplicateTitle = await prisma.articleDetails.findFirst({
//                 where: {
//                     Title: textFields.Title,
//                     Domain: textFields.Domain || existingArticle.Domain,
//                     // SubDomain: textFields.SubDomain || existingArticle.SubDomain,
//                     Id: { not: textFields.Id }
//                 }
//             });

//             if (duplicateTitle) {
//                 return NextResponse.json(
//                     { error: "Article title already exists" },
//                     { status: 409 }
//                 );
//             }
//         }


//         if (textFields.ShortName !== null && textFields.ShortName !== undefined) {

//             const duplicateShortName = await prisma.articleDetails.findFirst({
//                 where: {
//                     ShortName: textFields.ShortName,
//                     Id: { not: textFields.Id }
//                 }
//             });

//             if (duplicateShortName) {
//                 return NextResponse.json(
//                     { error: "Article ShortName already exists for another Article" },
//                     { status: 409 }
//                 );
//             }
//         }

//         const existingCombinations = await checkExistingCombinations(
//             textFields.CustomChannal ?? null,
//             textFields.StyleIdLm ?? null,
//             textFields.StyleIdDm ?? null,
//             textFields.Id
//         );


//         if (existingCombinations.length > 0) {
//             const messages: string[] = [];

//             for (const combo of existingCombinations) {
//                 const titles = combo.existingArticles
//                     .map((article) => article.title)
//                     .filter((title, index, self) => title && self.indexOf(title) === index);

//                 const titleList = titles.join(', ');

//                 if (combo.type === 'CustomChannel + StyleIdLm') {
//                     messages.push(`Combination of Custom Channel and StyleIdLm already exists for: ${titleList}`);
//                 } else if (combo.type === 'CustomChannel + StyleIdDm') {
//                     messages.push(`Combination of Custom Channel and StyleIdDm already exists for: ${titleList}`);
//                 } else {
//                     messages.push(`Combination already exists for: ${titleList}`);
//                 }
//             }

//             return NextResponse.json(
//                 {
//                     error: "Selected ChannalId and StyleId already exists",
//                     message: messages.join(' | ')
//                 },
//                 { status: 409 }
//             );
//         }

//         const file = formFields.file as File | null;
//         let imagePath: string | undefined = undefined;

//         if (file) {
//             try {
//                 const buffer = Buffer.from(await file.arrayBuffer());
//                 const mimeType = file.type;
//                 const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
//                 const uniqueId = uuidv4();

//                 if (!fs.existsSync(UPLOAD_DIR)) {
//                     fs.mkdirSync(UPLOAD_DIR, { recursive: true });
//                 }

//                 const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
//                     sharp(buffer).toBuffer(),
//                     sharp(buffer)
//                         .resize(300, null, { fit: 'inside', withoutEnlargement: true })
//                         .toFormat(originalExtension, { quality: 80 })
//                         .toBuffer(),
//                     sharp(buffer)
//                         .resize(720, null, { fit: 'inside', withoutEnlargement: true })
//                         .toFormat(originalExtension, { quality: 80 })
//                         .toBuffer()
//                 ]);

//                 const baseFileName = `${uniqueId}.${originalExtension}`;
//                 const versions = [
//                     { suffix: '', buffer: originalBuffer },
//                     { suffix: '_small', buffer: smallBuffer },
//                     { suffix: '_medium', buffer: mediumBuffer }
//                 ];

//                 await Promise.all(versions.map(({ suffix, buffer }) => {
//                     const fileName = `${uniqueId}${suffix}.${originalExtension}`;
//                     const filePath = path.resolve(UPLOAD_DIR, fileName);
//                     return fs.promises.writeFile(filePath, buffer);
//                 }));

//                 if (existingArticle.Image) {
//                     try {
//                         const oldFileName = path.basename(existingArticle.Image);
//                         const oldFilePaths = [
//                             path.resolve(UPLOAD_DIR, oldFileName),
//                             path.resolve(UPLOAD_DIR, oldFileName.replace('.', '_small.')),
//                             path.resolve(UPLOAD_DIR, oldFileName.replace('.', '_medium.'))
//                         ];

//                         await Promise.all(oldFilePaths.map(filePath =>
//                             fs.promises.unlink(filePath).catch(() => { })
//                         ));
//                     } catch (error) {
//                         console.error("Error deleting old image files:", error);
//                     }
//                 }

//                 imagePath = `${baseFileName}`;
//             } catch (error: unknown) {
//                 const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
//                 console.error("Image processing error:", errorMessage);
//                 return NextResponse.json(
//                     { error: "Failed to process image", details: errorMessage },
//                     { status: 500 }
//                 );
//             }
//         }

//         const ShowUrlName = (textFields.Title !== undefined && textFields.Title.trim() !== '')
//             ? textFields.Title.replace(/[^a-zA-Z0-9]+/g, "-").toLowerCase()
//             : undefined;

//         const updateData: any = {
//             UpdatedAt: new Date(),
//         };

//         if (textFields.Title !== undefined) updateData.Title = textFields.Title;
//         if (textFields.Category !== undefined) updateData.Category = textFields.Category;
//         if (textFields.Url !== undefined) updateData.Url = textFields.Url;
//         if (textFields.Description !== undefined) updateData.Description = textFields.Description;
//         if (textFields.Published !== undefined) updateData.Published = textFields.Published;
//         if (textFields.ShowArticle !== undefined) updateData.ShowArticle = textFields.ShowArticle;
//         if (textFields.ShowsAds !== undefined) updateData.ShowsAds = textFields.ShowsAds;
//         if (textFields.MetaTitle !== undefined) updateData.MetaTitle = textFields.MetaTitle;
//         if (textFields.MetaDescription !== undefined) updateData.MetaDescription = textFields.MetaDescription;
//         if (textFields.MetaKeys !== undefined) updateData.MetaKeys = textFields.MetaKeys;
//         if (textFields.CustomChannal !== undefined) updateData.CustomChannal = textFields.CustomChannal;
//         if (textFields.StyleIdLm !== undefined) updateData.StyleIdLm = textFields.StyleIdLm;
//         if (textFields.StyleIdDm !== undefined) updateData.StyleIdDm = textFields.StyleIdDm;
//         if (textFields.AdRelatedSearches !== undefined) updateData.AdRelatedSearches = textFields.AdRelatedSearches;
//         if (textFields.Remark !== undefined) updateData.Remark = textFields.Remark;
//         if (textFields.ShortDescription !== undefined) updateData.ShortDescription = textFields.ShortDescription;
//         if (textFields.Domain !== undefined) updateData.Domain = textFields.Domain;
//         if (textFields.SubDomain !== undefined) updateData.SubDomain = textFields.SubDomain;
//         if (textFields.ReadTime !== undefined) updateData.ReadTime = textFields.ReadTime;
//         if (textFields.AdReletadSearches2 !== undefined) updateData.AdReletadSearches2 = textFields.AdReletadSearches2;
//         if (textFields.CheckRelatedSearches2 !== undefined) updateData.CheckRelatedSearches2 = textFields.CheckRelatedSearches2;
//         if (imagePath !== undefined) updateData.Image = imagePath;
//         if (ShowUrlName !== undefined) updateData.ShowUrlName = ShowUrlName;
//         if (textFields.ShortName !== undefined) updateData.ShortName = textFields.ShortName;

//         const updatedArticle = await prisma.articleDetails.update({
//             where: { Id: textFields.Id },
//             data: updateData
//         });

//         if (textFields.CampaignIds !== undefined) {
//             await prisma.articleCampaignMappings.deleteMany({
//                 where: { ArticleId: textFields.Id }
//             });

//             const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
//                 ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
//                 : [];

//             if (normalizedCampaignIds.length > 0) {
//                 await prisma.articleCampaignMappings.createMany({
//                     data: normalizedCampaignIds.map(campaignId => ({
//                         ArticleId: textFields.Id,
//                         CampaignId: campaignId,
//                         CreatedAt: new Date(),
//                     })),
//                     skipDuplicates: true
//                 });
//             }
//         }

//         const articleWithRelations = await prisma.articleDetails.findUnique({
//             where: { Id: textFields.Id },
//             include: {
//                 ArticleCampaignMappings: {
//                     select: {
//                         CampaignId: true
//                     }
//                 }
//             }
//         });

//         const campaignMappingsCount = await prisma.articleCampaignMappings.count({
//             where: { ArticleId: textFields.Id }
//         });

//         // Updated cache invalidation section
//         let cacheInvalidationStatus = null;

//         try {


//             if (updatedArticle.Domain && updatedArticle.Url) {
//                 const domain = await prisma.domain.findUnique({
//                     where: { Id: updatedArticle.Domain }
//                 });

//                 let cacheUrl: string;

//                 if (updatedArticle.SubDomain) {
//                     const subdomain = await prisma.subDomain.findUnique({
//                         where: { Id: updatedArticle.SubDomain }
//                     });

//                     if (domain && subdomain && subdomain.Url) {
//                         const urlSlug = updatedArticle.Url;
//                         cacheUrl = `https://${subdomain.Url}.${domain.ShowUrlName}/cache/invalidate/${urlSlug}`;
//                     } else if (domain) {
//                         const urlSlug = updatedArticle.Url;
//                         cacheUrl = `https://${domain.ShowUrlName}/cache/invalidate/${urlSlug}`;
//                     } else {
//                         throw new Error("Domain not found");
//                     }
//                 } else if (domain) {
//                     const urlSlug = updatedArticle.Url;
//                     cacheUrl = `https://${domain.ShowUrlName}/cache/invalidate/${urlSlug}`;
//                 } else {
//                     throw new Error("Domain not found");
//                 }

//                 const cacheResponse = await axios.get(cacheUrl);
//                 cacheInvalidationStatus = {
//                     success: true,
//                     url: cacheUrl,
//                     status: cacheResponse.status,
//                     statusText: cacheResponse.statusText
//                 };
//             } else {
//                 cacheInvalidationStatus = {
//                     success: false,
//                     reason: "Missing Domain or Url",
//                     url: null
//                 };
//             }
//         } catch (cacheError) {
//             cacheInvalidationStatus = {
//                 success: false,
//                 reason: cacheError instanceof Error ? cacheError.message : 'Unknown cache error',
//                 url: null
//             };
//             // console.error("Cache invalidation failed:", cacheError);
//         }

//         return NextResponse.json({
//             success: true,
//             message: "Article updated successfully",
//         }, { status: 200 });

//     } catch (error: unknown) {
//         const errorMessage = error instanceof Error ? error.message : 'Unknown error';
//         console.error("Error in article update:", errorMessage);
//         return NextResponse.json(
//             { error: "Failed to update article", details: errorMessage },
//             { status: 500 }
//         );
//     }
// }



import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import axios from 'axios';
import Busboy from 'busboy';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

async function checkExistingCombinations(customChannel: string | null, styleIdLm: string | null, styleIdDm: string | null, excludeId?: string) {
    const existingCombinations = [];

    // Check CustomChannel + StyleIdLm combination
    if (customChannel && styleIdLm) {
        const existingLm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdLm: styleIdLm,
                ...(excludeId && { Id: { not: excludeId } }),
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdLm: true,
                StyleIdDm: true
            }
        });

        if (existingLm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdLm',
                combination: {
                    customChannel,
                    styleId: styleIdLm
                },
                existingArticles: existingLm.map(article => ({
                    id: article.Id,
                    title: article.Title,
                    customChannelId: article.CustomChannal,
                    styleIdLm: article.StyleIdLm,
                    styleIdDm: article.StyleIdDm
                }))
            });
        }

        const crossLmAsDm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdDm: styleIdLm,
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdLm: true,
                StyleIdDm: true
            }
        });

        if (crossLmAsDm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdDm (from styleIdLm)',
                combination: { customChannel, styleId: styleIdLm },
                existingArticles: crossLmAsDm.map(a => ({
                    id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
                }))
            });
        }
    }

    // Check CustomChannel + StyleIdDm combination
    if (customChannel && styleIdDm) {
        const existingDm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdDm: styleIdDm,
                ...(excludeId && { Id: { not: excludeId } }),
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdDm: true,
                StyleIdLm: true
            }
        });

        if (existingDm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdDm',
                combination: {
                    customChannel,
                    styleId: styleIdDm
                },
                existingArticles: existingDm.map(article => ({
                    id: article.Id,
                    title: article.Title,
                    customChannelId: article.CustomChannal,
                    styleIdDm: article.StyleIdDm,
                    styleIdLm: article.StyleIdLm
                }))
            });
        }

        const crossDmAsLm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdLm: styleIdDm,
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdLm: true,
                StyleIdDm: true
            }
        });

        if (crossDmAsLm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdLm (from styleIdDm)',
                combination: { customChannel, styleId: styleIdDm },
                existingArticles: crossDmAsLm.map(a => ({
                    id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
                }))
            });
        }
    }

    return existingCombinations;
}

export async function PUT(req: NextRequest) {
    // type AuthenticatedUser = {
    //     Id: string;
    // };
    // const user = await verifyToken(req) as AuthenticatedUser;
    // if (!user) {
    //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }
    const userId = req.headers.get('x-user-id') as string;
    try {
        const contentType = req.headers.get('content-type');
        if (!contentType || !contentType.includes('multipart/form-data')) {
            return NextResponse.json({ error: 'Invalid content type' }, { status: 400 });
        }

        const formData: any = {};
        const fileBuffers: { field: string; buffer: Buffer; filename: string; mimeType: string }[] = [];

        const busboy = Busboy({ headers: { 'content-type': contentType } });

        // Process each field in the form data
        busboy.on('field', (fieldname, val) => {
            formData[fieldname] = val;
        });

        // Process files in the form data
        busboy.on('file', (fieldname, file, info) => {
            const chunks: Buffer[] = [];
            const { filename, mimeType } = info;

            file.on('data', (data) => {
                chunks.push(data);
            });

            file.on('end', () => {
                if (chunks.length > 0) {
                    fileBuffers.push({
                        field: fieldname,
                        buffer: Buffer.concat(chunks),
                        filename: filename || '',
                        mimeType
                    });
                }
            });
        });

        // Wait for the busboy to finish processing
        const busboyFinished = new Promise((resolve, reject) => {
            busboy.on('finish', resolve);
            busboy.on('error', reject);
        });

        // Pipe the request body to busboy
        const reqBody = await req.blob();
        const reqBuffer = Buffer.from(await reqBody.arrayBuffer());
        busboy.end(reqBuffer);

        await busboyFinished;

        if (!formData.Id?.toString().trim()) {
            return NextResponse.json(
                { error: "Article ID is required" },
                { status: 400 }
            );
        }

        const existingArticle = await prisma.articleDetails.findUnique({
            where: { Id: formData.Id.toString() }
        });

        if (!existingArticle) {
            return NextResponse.json(
                { error: "Article not found" },
                { status: 404 }
            );
        }

        const textFields = {
            Id: formData.Id.toString(),
            Title: formData.Title !== undefined ? formData.Title.toString() : undefined,
            Category: formData.Category !== undefined ? formData.Category.toString() : undefined,
            Url: formData.Url !== undefined ? (formData.Url.toString().trim() || null) : undefined,
            Description: formData.Description !== undefined ? (formData.Description.toString().trim() || null) : undefined,
            ShortName: formData.ShortName !== undefined ? (formData.ShortName.toString().trim() || null) : undefined,
            MetaTitle: formData.MetaTitle !== undefined ? (formData.MetaTitle.toString().trim() || null) : undefined,
            MetaDescription: formData.MetaDescription !== undefined ? (formData.MetaDescription.toString().trim() || null) : undefined,
            MetaKeys: formData.MetaKeys !== undefined ? (formData.MetaKeys.toString().trim() || null) : undefined,
            CustomChannal: formData.CustomChannal !== undefined ? (formData.CustomChannal.toString().trim() || null) : undefined,
            StyleIdLm: formData.StyleIdLm !== undefined ? (formData.StyleIdLm.toString().trim() || null) : undefined,
            StyleIdDm: formData.StyleIdDm !== undefined ? (formData.StyleIdDm.toString().trim() || null) : undefined,
            AdRelatedSearches: formData.AdRelatedSearches !== undefined ? (formData.AdRelatedSearches.toString().trim() || null) : undefined,
            Remark: formData.Remark !== undefined ? (formData.Remark.toString().trim() || null) : undefined,
            ShortDescription: formData.ShortDescription !== undefined ? (formData.ShortDescription.toString().trim() || null) : undefined,
            Domain: formData.Domain !== undefined ? (formData.Domain.toString().trim() || null) : undefined,
            SubDomain: formData.SubDomain !== undefined ? (formData.SubDomain.toString().trim() || null) : undefined,
            AdReletadSearches2: formData.AdReletadSearches2 !== undefined ? (formData.AdReletadSearches2.toString().trim() || null) : undefined,
            Published: formData.Published !== undefined ? formData.Published === 'true' : undefined,
            ShowArticle: formData.ShowArticle !== undefined ? formData.ShowArticle === 'true' : undefined,
            ShowsAds: formData.ShowsAds !== undefined ? formData.ShowsAds === 'true' : undefined,
            CheckRelatedSearches2: formData.CheckRelatedSearches2 !== undefined ? formData.CheckRelatedSearches2 === 'true' : undefined,
            ShowInDomain: formData.ShowInDomain !== undefined ? formData.ShowInDomain === 'true' : undefined,
            ReadTime: formData.ReadTime !== undefined ?
                (formData.ReadTime.toString().trim() === '' ? null :
                    isNaN(parseInt(formData.ReadTime.toString())) ? undefined : parseInt(formData.ReadTime.toString())) : undefined,
            CampaignIds: formData.CampaignIds !== undefined ?
                (formData.CampaignIds.toString().trim() === '' ? [] :
                    JSON.parse(formData.CampaignIds.toString() || '[]') as number[]) : undefined,
        };

        // Add logic to clear ad-related fields when disabling ads
        // Check if ShowsAds is being explicitly set to false
        if (textFields.ShowsAds !== undefined && textFields.ShowsAds === false) {
            // Clear the ad-related fields
            textFields.CustomChannal = null;
            textFields.StyleIdLm = null;
            textFields.StyleIdDm = null;

            console.log("Clearing ad-related fields because ShowsAds is being set to false");
        }
        // If ShowsAds is not being changed but is currently false, ensure ad fields are null
        else if (textFields.ShowsAds === undefined && existingArticle.ShowsAds === false) {
            // Ensure ad-related fields are null if ads are disabled
            textFields.CustomChannal = null;
            textFields.StyleIdLm = null;
            textFields.StyleIdDm = null;

            console.log("Ensuring ad-related fields are null because ads are disabled");
        }

        if (textFields.Title !== undefined && textFields.Title.trim() !== '' && textFields.Title !== existingArticle.Title) {
            const duplicateTitle = await prisma.articleDetails.findFirst({
                where: {
                    Title: textFields.Title,
                    Domain: textFields.Domain || existingArticle.Domain,
                    Id: { not: textFields.Id }
                }
            });

            if (duplicateTitle) {
                return NextResponse.json(
                    { error: "Article title already exists" },
                    { status: 409 }
                );
            }
        }

        if (textFields.ShortName !== null && textFields.ShortName !== undefined) {
            const duplicateShortName = await prisma.articleDetails.findFirst({
                where: {
                    ShortName: textFields.ShortName,
                    Id: { not: textFields.Id }
                }
            });

            if (duplicateShortName) {
                return NextResponse.json(
                    { error: "Article ShortName already exists for another Article" },
                    { status: 409 }
                );
            }
        }

        // IMPORTANT: Skip the combination check if we're clearing these fields due to disabling ads
        let skipCombinationCheck = false;
        if (textFields.ShowsAds !== undefined && textFields.ShowsAds === false) {
            skipCombinationCheck = true;
            console.log("Skipping combination check because ads are being disabled");
        }

        if (!skipCombinationCheck) {
            const existingCombinations = await checkExistingCombinations(
                textFields.CustomChannal ?? null,
                textFields.StyleIdLm ?? null,
                textFields.StyleIdDm ?? null,
                textFields.Id
            );

            if (existingCombinations.length > 0) {
                const messages: string[] = [];

                for (const combo of existingCombinations) {
                    const titles = combo.existingArticles
                        .map((article) => article.title)
                        .filter((title, index, self) => title && self.indexOf(title) === index);

                    const titleList = titles.join(', ');

                    if (combo.type === 'CustomChannel + StyleIdLm') {
                        messages.push(`Combination of Custom Channel and StyleIdLm already exists for: ${titleList}`);
                    } else if (combo.type === 'CustomChannel + StyleIdDm') {
                        messages.push(`Combination of Custom Channel and StyleIdDm already exists for: ${titleList}`);
                    } else {
                        messages.push(`Combination already exists for: ${titleList}`);
                    }
                }

                return NextResponse.json(
                    {
                        error: "Selected ChannalId and StyleId already exists",
                        message: messages.join(' | ')
                    },
                    { status: 409 }
                );
            }
        }

        let imagePath: string | undefined = undefined;
        const fileData = fileBuffers.find(f => f.field === 'file');

        if (fileData) {
            try {
                const buffer = fileData.buffer;
                const mimeType = fileData.mimeType;
                const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
                const uniqueId = uuidv4();

                const domain = await prisma.domain.findUnique({
                    where: { Id: textFields.Domain },
                    select: { Prefix: true }
                });
                const domainPrefix = domain?.Prefix?.toLowerCase();

                const uploadPath = domainPrefix
                    ? path.resolve(UPLOAD_DIR, domainPrefix)
                    : UPLOAD_DIR;

                if (!fs.existsSync(uploadPath)) {
                    fs.mkdirSync(uploadPath, { recursive: true });
                }

                const originalBuffer = await sharp(buffer).toBuffer();

                const baseFileName = `${uniqueId}.${originalExtension}`;

                await fs.promises.writeFile(
                    path.resolve(uploadPath, baseFileName),
                    originalBuffer
                );

                if (existingArticle.Image) {
                    try {
                        const oldFileName = path.basename(existingArticle.Image);
                        // Extract prefix from old image path if it exists
                        const oldImagePath = existingArticle.Image;
                        const oldPrefix = oldImagePath.includes('/')
                            ? oldImagePath.split('/')[0]
                            : null;

                        const oldUploadPath = oldPrefix
                            ? path.resolve(UPLOAD_DIR, oldPrefix)
                            : UPLOAD_DIR;

                        const oldFilePath = path.resolve(oldUploadPath, oldFileName);
                        await fs.promises.unlink(oldFilePath).catch(() => { });
                    } catch (error) {
                        console.error("Error deleting old image file:", error);
                    }
                }

                if (domainPrefix) {
                    imagePath = `${domainPrefix}/${baseFileName}`;
                } else {
                    imagePath = `${baseFileName}`;
                }
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
                console.error("Image processing error:", errorMessage);
                return NextResponse.json(
                    { error: "Failed to process image", details: errorMessage },
                    { status: 500 }
                );
            }
        }
        const ShowUrlName = (textFields.Title !== undefined && textFields.Title.trim() !== '')
            ? textFields.Title.replace(/[^a-zA-Z0-9]+/g, "-").toLowerCase()
            : undefined;

        const updateData: any = {
            UpdatedAt: new Date(),
            Updated_By: userId
        };

        if (textFields.Title !== undefined) updateData.Title = textFields.Title;
        if (textFields.Category !== undefined) updateData.Category = textFields.Category;
        if (textFields.Url !== undefined) updateData.Url = textFields.Url;
        if (textFields.Description !== undefined) updateData.Description = textFields.Description;
        if (textFields.Published !== undefined) updateData.Published = textFields.Published;
        if (textFields.ShowArticle !== undefined) updateData.ShowArticle = textFields.ShowArticle;
        if (textFields.ShowsAds !== undefined) updateData.ShowsAds = textFields.ShowsAds;
        if (textFields.MetaTitle !== undefined) updateData.MetaTitle = textFields.MetaTitle;
        if (textFields.MetaDescription !== undefined) updateData.MetaDescription = textFields.MetaDescription;
        if (textFields.MetaKeys !== undefined) updateData.MetaKeys = textFields.MetaKeys;
        if (textFields.CustomChannal !== undefined) updateData.CustomChannal = textFields.CustomChannal;
        if (textFields.StyleIdLm !== undefined) updateData.StyleIdLm = textFields.StyleIdLm;
        if (textFields.StyleIdDm !== undefined) updateData.StyleIdDm = textFields.StyleIdDm;
        if (textFields.AdRelatedSearches !== undefined) updateData.AdRelatedSearches = textFields.AdRelatedSearches;
        if (textFields.Remark !== undefined) updateData.Remark = textFields.Remark;
        if (textFields.ShortDescription !== undefined) updateData.ShortDescription = textFields.ShortDescription;
        if (textFields.Domain !== undefined) updateData.Domain = textFields.Domain;
        if (textFields.SubDomain !== undefined) updateData.SubDomain = textFields.SubDomain;
        if (textFields.ReadTime !== undefined) updateData.ReadTime = textFields.ReadTime;
        if (textFields.AdReletadSearches2 !== undefined) updateData.AdReletadSearches2 = textFields.AdReletadSearches2;
        if (textFields.CheckRelatedSearches2 !== undefined) updateData.CheckRelatedSearches2 = textFields.CheckRelatedSearches2;
        if (textFields.ShowInDomain !== undefined) updateData.ShowInDomain = textFields.ShowInDomain;
        if (imagePath !== undefined) updateData.Image = imagePath;
        if (ShowUrlName !== undefined) updateData.ShowUrlName = ShowUrlName;
        if (textFields.ShortName !== undefined) updateData.ShortName = textFields.ShortName;

        const updatedArticle = await prisma.articleDetails.update({
            where: { Id: textFields.Id },
            data: updateData
        });

        if (textFields.CampaignIds !== undefined) {
            await prisma.articleCampaignMappings.deleteMany({
                where: { ArticleId: textFields.Id }
            });

            const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
                ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
                : [];

            if (normalizedCampaignIds.length > 0) {
                await prisma.articleCampaignMappings.createMany({
                    data: normalizedCampaignIds.map(campaignId => ({
                        ArticleId: textFields.Id,
                        CampaignId: campaignId,
                        CreatedAt: new Date(),
                    })),
                    skipDuplicates: true
                });
            }
        }

        const articleWithRelations = await prisma.articleDetails.findUnique({
            where: { Id: textFields.Id },
            include: {
                ArticleCampaignMappings: {
                    select: {
                        CampaignId: true
                    }
                }
            }
        });

        const campaignMappingsCount = await prisma.articleCampaignMappings.count({
            where: { ArticleId: textFields.Id }
        });

        // Cache invalidation
        let cacheInvalidationStatus = null;

        try {
            if (updatedArticle.Domain && updatedArticle.Url) {
                const domain = await prisma.domain.findUnique({
                    where: { Id: updatedArticle.Domain }
                });

                let cacheUrl: string;

                if (updatedArticle.SubDomain) {
                    const subdomain = await prisma.subDomain.findUnique({
                        where: { Id: updatedArticle.SubDomain }
                    });

                    if (domain && subdomain && subdomain.Url) {
                        const urlSlug = updatedArticle.Url;
                        cacheUrl = `https://${subdomain.Url}.${domain.ShowUrlName}/cache/invalidate/${urlSlug}`;
                    } else if (domain) {
                        const urlSlug = updatedArticle.Url;
                        cacheUrl = `https://${domain.ShowUrlName}/cache/invalidate/${urlSlug}`;
                    } else {
                        throw new Error("Domain not found");
                    }
                } else if (domain) {
                    const urlSlug = updatedArticle.Url;
                    cacheUrl = `https://${domain.ShowUrlName}/cache/invalidate/${urlSlug}`;
                } else {
                    throw new Error("Domain not found");
                }

                const cacheResponse = await axios.get(cacheUrl);
                cacheInvalidationStatus = {
                    success: true,
                    url: cacheUrl,
                    status: cacheResponse.status,
                    statusText: cacheResponse.statusText
                };
            } else {
                cacheInvalidationStatus = {
                    success: false,
                    reason: "Missing Domain or Url",
                    url: null
                };
            }
        } catch (cacheError) {
            cacheInvalidationStatus = {
                success: false,
                reason: cacheError instanceof Error ? cacheError.message : 'Unknown cache error',
                url: null
            };
        }

        return NextResponse.json({
            success: true,
            message: "Article updated successfully",
            data: {
                article: updatedArticle,
                campaignMappingsCount,
                cacheInvalidationStatus
            }
        }, { status: 200 });

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error("Error in article update:", errorMessage);
        return NextResponse.json(
            { error: "Failed to update article", details: errorMessage },
            { status: 500 }
        );
    }
}