// Common types used across the application

export interface BaseEntity {
  Id: string | number;
  CreatedAt?: string;
  UpdatedAt?: string;
}

export interface DropdownOption {
  Id: string | number;
  Name: string;
  [key: string]: any;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  totalCount?: number;
}

export interface PaginationParams {
  page: number;
  pageSize: number;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string;
  sortable?: boolean;
}

export interface FormField {
  value: any;
  error?: string;
  touched?: boolean;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any, formData?: any) => string | null;
  message?: string;
}

// User related types
export interface User extends BaseEntity {
  Name: string;
  Email: string;
  Number: string;
  User_Type: UserType;
  DisplayName?: string;
  ProfilePic?: string;
  AccessExpiration?: string;
  Status: boolean;
  AboutMe?: string;
}

export type UserType = 'Super Admin' | 'Admin' | 'User' | 'Editor';

// Domain related types
export interface Domain extends BaseEntity {
  Name: string;
  ShowUrlName: string;
  Prefix: string;
  ChannelId: string;
  CookieMinutes?: number;
  StyleIdDm?: string;
  StyleIdLm?: string;
  HeadTagScript?: string;
  HeadTagScriptLandingPage?: string;
  HeadTagScriptSearchPage?: string;
  GId?: string;
  AWId?: string;
  SendTo?: string;
  AdsProviderId?: string;
  TrackingKey?: string;
  WideLogo?: File | null;
  SquareLogo?: File | null;
  ContactEmail?: string;
  Address?: string;
  AdsTxtContent?: string;
}

// Subdomain related types
export interface SubDomain extends BaseEntity {
  Name: string;
  Url: string;
  Domain: string;
  AssignUsers: any[];
  AccountId: any[];
  HeadTagScript?: string;
  HeadTagScriptLandingPage?: string;
  HeadTagScriptSearchPage?: string;
  GId?: string;
  AWId?: string;
  SendTo?: string;
  AdsProviderId?: string;
  TrackingKey?: string;
  UserMappings?: any[];
}

// Style ID related types
export interface StyleId extends BaseEntity {
  StyleId: string;
  Name: string;
  AssignUser?: User | null;
}

// Article related types
export interface Article extends BaseEntity {
  title: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  image?: string;
  published: boolean;
  showArticle: boolean;
  showInDomain: boolean;
  showAds: boolean;
  domain: string;
  subdomain: string;
  category: string;
  channels?: string[];
  campaigns?: string[];
  styleIds?: string[];
}

// Category related types
export interface Category extends BaseEntity {
  Name: string;
  Description?: string;
  Status: boolean;
}

// Campaign related types
export interface Campaign extends BaseEntity {
  Name: string;
  Description?: string;
  Status: boolean;
}

// Channel related types
export interface Channel extends BaseEntity {
  Name: string;
  Description?: string;
  Status: boolean;
}

// Ads Provider related types
export interface AdsProvider extends BaseEntity {
  Name: string;
  Description?: string;
  Status: boolean;
}

// Account Details related types
export interface AccountDetail extends BaseEntity {
  Name: string;
  AccountId: string;
  Status: boolean;
}
