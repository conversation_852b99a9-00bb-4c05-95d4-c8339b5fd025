// UI constants and enums

// Button variants
export const BUTTON_VARIANTS = {
  PRIMARY: 'primary',
  GREEN: 'green',
  DANGER: 'danger',
  DARK: 'dark',
  OUTLINE_PRIMARY: 'outlinePrimary',
  OUTLINE_GREEN: 'outlineGreen',
  OUTLINE_DARK: 'outlineDark',
} as const;

// Button shapes
export const BUTTON_SHAPES = {
  DEFAULT: 'default',
  ROUNDED: 'rounded',
  FULL: 'full',
} as const;

// Button sizes
export const BUTTON_SIZES = {
  DEFAULT: 'default',
  SMALL: 'small',
} as const;

// Modal sizes
export const MODAL_SIZES = {
  XS: 'xs',
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
  XL: 'xl',
} as const;

// Table sort orders
export const SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc',
} as const;

// User types
export const USER_TYPES = {
  SUPER_ADMIN: 'Super Admin',
  ADMIN: 'Admin',
  USER: 'User',
  EDITOR: 'Editor',
} as const;

// Status options
export const STATUS_OPTIONS = {
  ACTIVE: true,
  INACTIVE: false,
} as const;

// Common pagination options
export const PAGINATION_OPTIONS = {
  ROWS_PER_PAGE: [5, 10, 25, 50, 100],
  DEFAULT_ROWS_PER_PAGE: 10,
  DEFAULT_PAGE: 0,
} as const;

// Form validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_URL: 'Please enter a valid URL',
  MIN_LENGTH: (min: number) => `Must be at least ${min} characters`,
  MAX_LENGTH: (max: number) => `Must be no more than ${max} characters`,
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  INVALID_PASSWORD: 'Password must contain at least 8 characters with uppercase, lowercase, and number',
} as const;

// Loading states
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

// Theme modes
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// File upload constants
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
} as const;

// Date formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  INPUT: 'yyyy-MM-dd',
  DATETIME: 'MMM dd, yyyy HH:mm',
  TIME: 'HH:mm',
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// Z-index layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const;

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Common CSS classes
export const CSS_CLASSES = {
  VISUALLY_HIDDEN: 'sr-only',
  TRUNCATE: 'truncate',
  TRANSITION: 'transition-all duration-300',
  FOCUS_RING: 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
} as const;
