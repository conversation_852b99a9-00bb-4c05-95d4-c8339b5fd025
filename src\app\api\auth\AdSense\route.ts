import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const code = searchParams.get('code');

        if (!code) {
            return NextResponse.json({ error: 'Authorization code is required' }, { status: 400 });
        }

        const adminSettings = await prisma.adminUserSetting.findFirst({
            select: {
                RevenueClientId: true,
                RevenueClientSecret: true,
                RevenueRedirectUrl: true
            }
        });

        if (!adminSettings?.RevenueClientId || !adminSettings?.RevenueClientSecret) {
            return NextResponse.json({ error: 'OAuth credentials not configured' }, { status: 400 });
        }

        const tokenEndpoint = 'https://oauth2.googleapis.com/token';
        const requestBody = new URLSearchParams();
        requestBody.append('code', code);
        requestBody.append('client_id', adminSettings.RevenueClientId);
        requestBody.append('client_secret', adminSettings.RevenueClientSecret);
        requestBody.append('redirect_uri', adminSettings.RevenueRedirectUrl as string);
        requestBody.append('grant_type', 'authorization_code');

        const response = await fetch(tokenEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: requestBody.toString(),
        });

        const responseData = await response.json();

        if (!response.ok) {
            console.error('Google OAuth Error:', {
                status: response.status,
                error: responseData,
            });
            return NextResponse.json(
                { error: 'OAuth token exchange failed', details: responseData },
                { status: response.status }
            );
        }

        // Update the refresh token in the database
        if (responseData.refresh_token) {
            await prisma.adminUserSetting.updateMany({
                data: {
                    RevenueRefreshToken: responseData.refresh_token,
                    RevenueAccessToken: responseData.access_token
                }
            });
        } else {
            console.warn('No refresh token in response - using existing token');
        }

        return NextResponse.json(responseData);

    } catch (error) {
        console.error("OAuth Error:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}