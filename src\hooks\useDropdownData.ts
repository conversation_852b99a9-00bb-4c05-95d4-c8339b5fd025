import { useState, useEffect, useCallback } from 'react';
import { useApiGet } from './useApi';

interface DropdownOption {
  Id: string | number;
  Name: string;
  [key: string]: any;
}

interface UseDropdownDataOptions {
  autoFetch?: boolean;
  dependencies?: any[];
  transform?: (data: any) => DropdownOption[];
}

interface UseDropdownDataReturn {
  options: DropdownOption[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  reset: () => void;
}

export function useDropdownData(
  url: string,
  options: UseDropdownDataOptions = {}
): UseDropdownDataReturn {
  const { autoFetch = true, dependencies = [], transform } = options;
  
  const [options_data, setOptionsData] = useState<DropdownOption[]>([]);
  
  const { data, loading, error, get, reset } = useApiGet({
    showErrorAlert: false,
  });

  const fetchData = useCallback(async () => {
    if (!url) return;
    
    const response = await get(url);
    if (response?.success && response?.data) {
      const transformedData = transform ? transform(response.data) : response.data;
      setOptionsData(Array.isArray(transformedData) ? transformedData : []);
    } else {
      setOptionsData([]);
    }
  }, [url, get, transform]);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const resetData = useCallback(() => {
    setOptionsData([]);
    reset();
  }, [reset]);

  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [autoFetch, fetchData, ...dependencies]);

  return {
    options: options_data,
    loading,
    error,
    refetch,
    reset: resetData,
  };
}

// Specialized hooks for common dropdown endpoints
export function useDomainDropdown(dependencies: any[] = []) {
  return useDropdownData('/Domain/GetDropDown', {
    dependencies,
  });
}

export function useSubdomainDropdown(domainId?: string | number, dependencies: any[] = []) {
  const url = domainId ? `/SubDomain/GetDropDown?Domain=${domainId}` : '/SubDomain/GetDropDown';
  return useDropdownData(url, {
    dependencies: [domainId, ...dependencies],
    autoFetch: !!domainId || !domainId, // Fetch regardless for now
  });
}

export function useCategoryDropdown(dependencies: any[] = []) {
  return useDropdownData('/category/GetDropDown', {
    dependencies,
  });
}

export function useUserDropdown(dependencies: any[] = []) {
  return useDropdownData('/adminuser/GetDropDown', {
    dependencies,
  });
}

export function useAccountDetailsDropdown(dependencies: any[] = []) {
  return useDropdownData('/AccountDetails/GetDropDown', {
    dependencies,
  });
}

export function useAdsProviderDropdown(dependencies: any[] = []) {
  return useDropdownData('/AdsProvider/GetDropDown', {
    dependencies,
  });
}

export function useChannelDropdown(dependencies: any[] = []) {
  return useDropdownData('/Channals/GetDropDown', {
    dependencies,
  });
}

export function useStyleIdDropdown(dependencies: any[] = []) {
  return useDropdownData('/StyleIds/GetDropDown', {
    dependencies,
  });
}

export function useCampaignDropdown(dependencies: any[] = []) {
  return useDropdownData('/Campaigns/GetDropDown', {
    dependencies,
  });
}
