-- View: public.vw_ads_campaigndetails

-- DROP VIEW public.vw_ads_campaigndetails;

CREATE OR REPLACE VIEW public.vw_ads_campaigndetails
 AS
 SELECT camp."AccountId",
    acc."ClientCustomer",
    acc."DescriptiveName",
    camp."Status",
    camp."PaymentMode",
    camp."Name",
    campb."CampaignBudgetId",
    campb."Status" AS "BudgetStatus",
    campd."SNo",
    campd."CampaignId",
    campd."ActiveViewCPM",
    campd."ActiveViewCTR",
    campd."ActiveViewImpressions",
    campd."ActiveViewMeasurability",
    campd."ActiveViewMeasurableCostMicros",
    campd."ActiveViewMeasurableImpressions",
    campd."ActiveViewViewAbility",
    campd."AllConversionsValue",
    campd."AllConversionsValueByConversionDate",
    campd."AllConversions",
    campd."AllConversionsByConversionDate",
    campd."AverageCost",
    campd."AverageCPC",
    campd."AverageCPM",
    campd."Clicks",
    campd."ConversionsFromInteractionsRate",
    campd."ConversionsValue",
    campd."ConversionsValueByConversionDate",
    campd."Conversions",
    campd."ConversionsByConversionDate",
    campd."CostMicros",
    campd."CostPerAllConversions",
    campd."CostPerConversion",
    campd."CostPerCurrentModelAttributedConversion",
    campd."CTR",
    campd."CurrentModelAttributedConversions",
    campd."CurrentModelAttributedConversionsFromInteractionsRate",
    campd."CurrentModelAttributedConversionsFromInteractionsValuePerIntera",
    campd."CurrentModelAttributedConversionsValue",
    campd."CurrentModelAttributedConversionsValuePerCost",
    campd."Engagements",
    campd."Impressions",
    campd."InteractionRate",
    campd."Interactions",
    campd."InvalidClickRate",
    campd."InvalidClicks",
    campd."GeneralInvalidClickRate",
    campd."GeneralInvalidClicks",
    campd."AverageTargetCpaMicros",
    campd."ValuePerAllConversions",
    campd."ValuePerAllConversionsByConversionDate",
    campd."ValuePerConversion",
    campd."ValuePerConversionsByConversionDate",
    campd."ValuePerCurrentModelAttributedConversion",
    campd."SegmentDate"
   FROM "Ads_CampaignDetails" campd
     JOIN "Ads_Campaigns" camp ON camp."CampaignId" = campd."CampaignId"
     LEFT JOIN "Ads_AccountDetails" acc ON camp."AccountId" = acc."AccountId"
     LEFT JOIN "Ads_CampaignsBudget" campb ON camp."CampaignId" = campb."CampaignId" AND campd."SegmentDate" = campb."SegmentDate";
