import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { writeFile, access, constants } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { mkdirSync, existsSync } from 'fs';
import sharp from 'sharp';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || '/var/www/images';
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MIN_FILE_SIZE = 1024; // 1KB minimum file size

type SharpImageFormat = keyof sharp.FormatEnum;

export async function POST(req: NextRequest) {
    try {

        // type AuthenticatedUser = {
        //     Id: string;
        // };
        // const user = await verifyToken(req) as AuthenticatedUser;
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');
        const formData = await req.formData();

        const Name = formData.get('Name')?.toString();
        const ShowUrlName = formData.get('ShowUrlName')?.toString();

        if (!Name || !ShowUrlName) {
            return NextResponse.json(
                { error: 'Name and ShowUrlName are required' },
                { status: 400 }
            );
        }

        const Prefix = formData.get('Prefix')?.toString() || null;
        const ChannelId = formData.get('ChannelId')?.toString() || null;
        const CookieMinutes = formData.get('CookieMinutes') ? Number(formData.get('CookieMinutes')) : null;
        const StyleIdDm = formData.get('StyleIdDm') ? BigInt(formData.get('StyleIdDm') as string) : null;
        const StyleIdLm = formData.get('StyleIdLm') ? BigInt(formData.get('StyleIdLm') as string) : null;
        const HeadTagScript = formData.get('HeadTagScript')?.toString() || null;
        const HeadTagScriptLandingPage = formData.get('HeadTagScriptLandingPage')?.toString() || null;
        const HeadTagScriptSearchPage = formData.get('HeadTagScriptSearchPage')?.toString() || null;
        const GId = formData.get('GId')?.toString() || null;
        const AWId = formData.get('AWId')?.toString() || null;
        const SendTo = formData.get('SendTo')?.toString() || null;
        const ContactEmail = formData.get('ContactEmail')?.toString() || null;
        const Address = formData.get('Address')?.toString() || null;
        const AdsTxtContent = formData.get('AdsTxtContent')?.toString() || null;
        const AdsProviderId = formData.get('AdsProviderId')?.toString() || null;
        const AdsProviderDescription = formData.get('AdsProviderDescription')?.toString() || null;
        const TrackingKey = formData.get('TrackingKey')?.toString() || null;


        const existing = await prisma.domain.findFirst({
            where: {
                OR: [
                    { Name, IsDeleted: false },
                    { ShowUrlName, IsDeleted: false }
                ]
            }
        });

        if (existing) {
            const conflictField = existing.Name === Name ? 'Name' : 'ShowUrlName';
            return NextResponse.json(
                { error: `Domain with this ${conflictField} already exists` },
                { status: 409 }
            );
        }

        if (existing) {
            return NextResponse.json(
                { error: 'Domain with this name already exists' },
                { status: 409 }
            );
        }

        if (!existsSync(UPLOAD_DIR)) {
            mkdirSync(UPLOAD_DIR, { recursive: true });
        }
        try {
            await access(UPLOAD_DIR, constants.W_OK);
        } catch (err) {
            console.error('Upload directory is not writable:', UPLOAD_DIR);
            return NextResponse.json(
                { error: 'Server configuration error: upload directory not writable' },
                { status: 500 }
            );
        }

        function isSharpImageFormat(format: string): format is SharpImageFormat {
            const sharpFormats: SharpImageFormat[] = ['jpeg', 'png', 'webp', 'gif', 'tiff', 'raw'];
            return sharpFormats.includes(format as SharpImageFormat);
        }

        const processImage = async (file: File | null, prefix: string): Promise<string | null> => {
            if (!file || file.size === 0) {
                return null;
            }

            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                let mimeType = file.type;
                let originalExtension: SharpImageFormat = 'jpeg'; // default fallback

                if (mimeType === 'application/octet-stream') {
                    try {
                        const metadata = await sharp(buffer).metadata();
                        if (metadata.format && isSharpImageFormat(metadata.format)) {
                            mimeType = `image/${metadata.format}`;
                            originalExtension = metadata.format;
                        }
                    } catch (sharpError) {
                        return null;
                    }
                } else {
                    const ext = mimeType.split('/')[1];
                    if (ext && isSharpImageFormat(ext)) {
                        originalExtension = ext;
                    }
                }

                if (!ALLOWED_IMAGE_TYPES.includes(mimeType)) {
                    return null;
                }

                if (file.size < MIN_FILE_SIZE) {
                    return null;
                }

                const uniqueId = uuidv4();
                const baseFileName = `${prefix?.toLowerCase()}_${uniqueId}.${originalExtension}`;

                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                await Promise.all([
                    writeFile(path.join(UPLOAD_DIR, baseFileName), originalBuffer),
                    writeFile(path.join(UPLOAD_DIR, `${prefix?.toLowerCase()}_${uniqueId}_small.${originalExtension}`), smallBuffer),
                    writeFile(path.join(UPLOAD_DIR, `${prefix?.toLowerCase()}_${uniqueId}_medium.${originalExtension}`), mediumBuffer)
                ]);

                return baseFileName;
            } catch (error) {
                console.error(`Error processing ${prefix?.toLowerCase()} image:`, error);
                return null;
            }
        };

        const [wideLogoPath, squareLogoPath] = await Promise.all([
            processImage(formData.get('wideLogo') as File | null, 'wide'),
            processImage(formData.get('squareLogo') as File | null, 'square')
        ]);

        if (AdsProviderId) {
            try {
                await prisma.adsProvider.update({
                    where: { Id: AdsProviderId },
                    data: {
                        Description: AdsProviderDescription,
                        TrackingKey: TrackingKey
                    }
                });
            } catch (error) {
                console.error('Error updating AdsProvider description:', error);
                return NextResponse.json(
                    { error: 'Failed to update AdsProvider description' },
                    { status: 500 }
                );
            }
        }

        const newDomain = await prisma.domain.create({
            data: {
                Name,
                Prefix,
                ShowUrlName,
                CreatedBy: userId,
                ChannelId,
                CookieMinutes,
                StyleIdDm,
                StyleIdLm,
                HeadTagScript,
                HeadTagScriptLandingPage,
                HeadTagScriptSearchPage,
                GId,
                AWId,
                SendTo,
                ContactEmail,
                Address,
                AdsTxtContent,
                WideLogo: wideLogoPath,
                SquareLogo: squareLogoPath,
                CreatedAt: new Date(),
                AdsProviderId,
                TrackingKey
            }
        });

        return NextResponse.json({
            success: true,
            message: 'Domain created successfully',
            data: {
                ...newDomain,
                wideLogoUrl: wideLogoPath ? `/images/${wideLogoPath}` : null,
                squareLogoUrl: squareLogoPath ? `/images/${squareLogoPath}` : null
            }
        }, { status: 201 });

    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}