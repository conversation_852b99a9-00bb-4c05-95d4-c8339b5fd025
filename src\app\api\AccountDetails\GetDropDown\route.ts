import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
import { prisma } from '../../../../lib/prisma';


export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        let where: any = {
            Manager: false,
        };

        const users = await prisma.ads_AccountDetails.findMany({
            where,
            select: {
                AccountId: true,
                ClientCustomer: true,
                DescriptiveName: true
            },
            orderBy: {
                DescriptiveName: 'asc',
            }
        });

        const sanitizedUsers = users.map(user => ({
            ...user,
            AccountId: user.AccountId.toString(),
            ClientCustomer: user.ClientCustomer?.toString(),
        }));

        return NextResponse.json({
            success: true,
            data: sanitizedUsers,
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Failed to fetch users" },
            { status: 500 }
        );
    }
}
