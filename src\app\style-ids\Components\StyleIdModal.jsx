"use client";

import React, { useState } from "react";
import {
  Dialog,
  <PERSON>alogActions,
  <PERSON>alogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { CloseIcon } from "@/assets/icons";

const StyleIdModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isEdit,
  users,
  isLoading,
}) => {
  const [formData, setFormData] = useState({
    StyleId: "",
    Name: "",
    AssignUser: null,
  });

  const [errors, setErrors] = useState({});

  React.useEffect(() => {
    if (isEdit && initialData) {
      setFormData({
        StyleId: initialData.StyleId || "",
        Name: initialData.Name || "",
        AssignUser: initialData.AssignUser || null,
      });
    } else {
      setFormData({
        StyleId: "",
        Name: "",
        AssignUser: null,
      });
    }
  }, [initialData, isEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.StyleId) newErrors.StyleId = "Style ID is required";
    else if (isNaN(formData.StyleId)) newErrors.StyleId = "Style ID must be a number";
    if (!formData.Name.trim()) newErrors.Name = "Name is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={(event, reason) => {
        if (reason === "backdropClick") return;
        onClose();
      }}
      fullWidth
      maxWidth="sm"
      slotProps={{
        paper: {
          sx: {
            height: "60vh",
            maxHeight: "90vh",
          },
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {isEdit ? "Edit Style ID" : "Add Style ID"}
        </span>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form>
          <InputGroup
            label="Style ID (Numeric)"
            type="number"
            name="StyleId"
            value={formData.StyleId}
            handleChange={handleChange}
            placeholder="Enter Style ID"
            required
            error={errors.StyleId}
          />
          <InputGroup
            label="Name"
            type="text"
            name="Name"
            value={formData.Name}
            handleChange={handleChange}
            placeholder="Enter Name"
            required
            error={errors.Name}
          />
          <SearchableDropdown
            label="Assign Users"
            options={users}
            value={formData.AssignUser ? formData.AssignUser.Id : ""}
            onChange={(selectedUser) =>
              setFormData({ ...formData, AssignUser: selectedUser })
            }
            displayKey="Name"
            idKey="Id"
            displayKey2="Email"
            placeholder="Select users..."
          />
        </form>
      </DialogContent>

      <DialogActions sx={{ py: 2, px: 3 }}>
        <Button
          type="submit"
          label={
            isLoading
              ? "Loading..."
              : isEdit
                ? "Update Style ID"
                : "Add Style ID"
          }
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
        />
      </DialogActions>
    </Dialog>
  );
};

export default StyleIdModal;