"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { <PERSON>a<PERSON><PERSON>, FaFilter } from "react-icons/fa";
import { convertToBase64, fetchImage } from "@/utils/functions";
import Swal from "sweetalert2";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import ArticleModal from "../Components/ArticleModal";
import InputGroup from "@/components/FormElements/InputGroup";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import { decodeJWT } from "@/utils/functions";
import { CloseIcon } from "@/assets/icons";
import Checkbox from "@/components/FormElements/checkbox";
import useDebounce from "@/hooks/useDebounce";
import apiClient from "@/utils/axiosInstance";

const reverseFieldMapping = {
  title: "Title",
  url: "url",
  description: "description",
  shortName: "ShortName",
  shortDescription: "shortDescription",
  metatitle: "metatitle",
  metadescription: "metadescription",
  metakeys: "metakeys",
  hashtag: "hashtag",
  customChannal: "customChannal",
  styleIdLm: "StyleIdLm",
  styleIdDm: "StyleIdDm",
  adrelatedsearches: "adrelatedsearches",
  remark: "remark",
  category: "category",
  userName: "user_id_settings",
  domain: "domain",
  subdomain: "subdomain",
  published: "Published",
  showArticle: "ShowArticle",
  showInDomain : "ShowInDomain",
  showAds: "ShowsAds",
  image: "image",
  categoryName: "CategoryName",
  customChannalName: "CustomChannal",
  channelId: "CustomChannalId",
  campaignCount: "CampaignCount",
  status: "Published",
  userName: "UserName",
  domainName: "DomainName",
  subDomainName: "SubDomainName",
};

const ArticlePage = () => {
  const [categories, setCategories] = useState([]);
  const [modalShow, setModalShow] = useState(false);
  const [articles, setArticles] = useState([]);
  const [editId, setEditId] = useState(null);
  const [showLoader, setShowLoader] = useState(true);
  const [published, setPublished] = useState(true);
  const [showArticle, setShowArticle] = useState(false);
  const [showInDomain, setShowInDomain] = useState(false);
  const [showAds, setShowAds] = useState(false);
  const [formdataImage, setFormdataImage] = useState("");
  const [assignChannels, setAssignChannels] = useState([]);
  const [base64Image, setBase64Image] = useState("");
  const [users, setUsers] = useState([]);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [domains, setDomains] = useState([]);
  const [subdomains, setSubdomains] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [selectedCampaignIds, setSelectedCampaignIds] = useState([]);
  const [styleIds, setStyleIds] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const dropdownRef = useRef(null);
  const [description, setDescription] = useState("");
  const [shortDescription, setShortDescription] = useState("");
  const [orderBy, setOrderBy] = useState("");
  const [order, setOrder] = useState("asc");
  const [userData, setUserData] = useState(null);
  const [campaignSearchTerm, setCampaignSearchTerm] = useState("");
  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const [selectedArticleForCampaigns, setSelectedArticleForCampaigns] = useState(null);
  const [articleCampaigns, setArticleCampaigns] = useState([]);
  const [campaignModalLoading, setCampaignModalLoading] = useState(false);
  const [showAddCampaignForm, setShowAddCampaignForm] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [showAddCampaignFormClick, setShowAddCampaignFormClick] = useState(false);
  const [showAddCampaignModal, setShowAddCampaignModal] = useState(false);
  const [campaignFormData, setCampaignFormData] = useState({
    articleId : "",
    budgetName: "",
    budgetAmountMicros: "",
    campaignName: "",
    customerId: "",
    campaignId: "",
  });
  const [customerOptions, setCustomerOptions] = useState([]);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [showSecondRelatedSearch, setShowSecondRelatedSearch] = useState(false);
  const [editSlugMode, setEditSlugMode] = useState(false);
  const [styleIdLm , setStyleIdLm] =  useState("");
  const [styleIdDm , setStyleIdDm] =  useState("");
  const [campaignPage, setCampaignPage] = useState(0);
  const [campaignRowsPerPage, setCampaignRowsPerPage] = useState(10);
  const [campaignOrder, setCampaignOrder] = useState("asc");
  const [campaignOrderBy, setCampaignOrderBy] = useState("CreatedAt");
  const [campaignTotalCount, setCampaignTotalCount] = useState(0);

  // Consolidated filter states
  const [filters, setFilters] = useState({
    searchTerm: "",
    selectedUser: "",
    selectedCategory: "",
    selectedDomain: "",
    selectedSubdomain: "",
    selectedShowAds: "",
    selectedStatus: "",
  });

  // Modal-specific domain and subdomain states
  const [modalSelectedDomain, setModalSelectedDomain] = useState("");
  const [modalSelectedSubdomain, setModalSelectedSubdomain] = useState("");

  // Debounce search term
  const debouncedSearchTerm = useDebounce(filters.searchTerm, 500);

  const columns = [
    { id: "title", label: "Title" },
    ...(isSuperAdmin ? [{ id: "userName", label: "User Name" }] : []),
    { id: "categoryName", label: "Category" },
    { id: "customChannalName", label: "Custom Channel" },
    { id: "campaignCount", label: "Campaigns" },
    { id: "showAds", label: "Show Ads" },
    { id: "status", label: "Status / Ads" },
    { id: "domainName", label: "Domain Name" },
  ];

  const mapResponseToFrontend = (article) => {
    
    return {
      id: article.Id || article.id,
      title: article.Title || article.title || "No Title",
      url: article.Url || article.url || "",
      description: article.Description || article.description || "",
      shortDescription: article.ShortDescription || article.shortDescription || "",
      metatitle: article.MetaTitle || article.metatitle || "",
      metadescription: article.MetaDescription || article.metadescription || "",
      metakeys: article.MetaKeys || article.metakeys || "",
      hashtag: article.Hashtag || article.hashtag || "",
      customChannal: article.CustomChannalId && (article.ChannelName || article.customChannalName)
        ? {
            Id: article.CustomChannalId,
            displayName: article.ChannelName || article.customChannalName,
            reportingDimensionId: `ga:${article.CustomChannalId}`,
          }
        : null,
      shortName: article.ShortName || "",
      styleIdLm: article.StyleIdLm || "",
      styleIdDm: article.StyleIdDm || "",
      adrelatedsearches: article.AdRelatedSearches || article.adrelatedsearches || "7",
      adrelatedsearches2: article.AdReletadSearches2 || "",
      checkRelatedSearches2: !!article.CheckRelatedSearches2,
      remark: article.Remark || article.remark || "",
      published: !!article.Published || !!article.published,
      showArticle: !!article.ShowArticle || !!article.showArticle,
      showInDomain : article.ShowInDomain,
      showAds: !!article.ShowsAds || !!article.showsAds,
      image: article.Image || article.image || "",
      userId: article.User_Id_Settings || article.userId || "",
      userName: article.UserName || article.userName || "Unknown",
      category: article.CategoryName
        ? {
            Id: article.CategoryId || article.category?.Id,
            Name: article.CategoryName,
          }
        : null,
      domain: article.DomainName
        ? {
            Id: article.DomainId || article.domain?.Id,
            name: article.DomainName,
            showUrlName: article.DomainUrl,
          }
        : null,
      subdomain: article.SubDomainId || null,
      campaigns: article.Campaigns?.map((c) => ({
        SNo: c.SNo,
        Name: c.Name,
        Description: c.Description || "",
      })) || [],
      categoryName: article.CategoryName || "No category",
      showAds: article.ShowsAds || article.showsAds ? "True" : "False",
      customChannalName: article.CustomChannal || "No channel",
      channelId: article.CustomChannalId || "No ID",
      status: article.Published || article.published ? "Published" : "Draft",
      campaignCount: article.CampaignCount || 0,
      domainName: article.Domain,
    };
  };

  const defaultValuesForm = {
    title: { val: "", err: "" },
    category: { val: "", err: "" },
    url: { val: "", err: "" },
    description: { val: "", err: "" },
    shortDescription: { val: "", err: "" },
    metatitle: { val: "", err: "" },
    metadescription: { val: "", err: "" },
    metakeys: { val: "", err: "" },
    hashtag: { val: "", err: "" },
    shortName: { val: "", err: "" },
    customChannal: { val: "", err: "" },
    styleIdLm: { val: "", err: "" },
    styleIdDm: { val: "", err: "" },
    adrelatedsearches: { val: "7", err: "" },
    adrelatedsearches2: { val: "", err: "" },
    remark: { val: "", err: "" },
    campaigns: { val: [], err: "" },
    domain: { val: "", err: "" },
    subdomain: { val: "", err: "" },
    readTime: { val: "", err: "" },
  };
  const optionalFields = [
    "remark",
    "metatitle",
    "metadescription",
    "metakeys",
    "hashtag",
    "styleIdLm",
    "styleIdDm",
    "shortName",
    "adrelatedsearches2",
  ];
  const showAlert = (config) => {
    if (isMounted) {
      Swal.fire(config);
    }
  };

  const [formData, setFormData] = useState(defaultValuesForm);

  const validateField = (name, value, showAds) => {
    if (optionalFields.includes(name)) {
      if (name === "adrelatedsearches2" && showSecondRelatedSearch) {
        return value ? "" : "2nd Ad Related Searches field is required when enabled.";
      }
      return "";
    }
    const adRelatedFields = [
      "subdomain",
      "customChannal",
      "campaigns",
      "styleIdLm",
      "styleIdDm",
      "adrelatedsearches",
      "adrelatedsearches2",
    ];
    if (adRelatedFields.includes(name) && !showAds) {
      return "";
    }

    if (name === "readTime") {
      if (!value) return "Read Time field is required.";
      if (isNaN(value)) return "Read Time must be a number.";
      if (Number(value) <= 0) return "Read Time must be greater than 0.";
      return "";
    }
    
    return value ? "" : `${name.replace(/([A-Z])/g, " $1").trim()} field is required.`;
  };
  const fetchArticles = useCallback(async () => {
    try {
      setShowLoader(true);
      let url = `/article/get?page=${page + 1}&length=${rowsPerPage}`;
      if (filters.searchTerm) url += `&q=${encodeURIComponent(filters.searchTerm)}`;
      if (filters.selectedUser) url += `&userId=${encodeURIComponent(filters.selectedUser)}`;
      // if (filters.selectedCategory) url += `&CategoryId=${encodeURIComponent(filters.selectedCategory)}`;
      if (filters.selectedDomain) url += `&domainId=${encodeURIComponent(filters.selectedDomain)}`;
      if (filters.selectedSubdomain && filters.selectedDomain) url += `&subDomainId=${encodeURIComponent(filters.selectedSubdomain)}`;
      if (filters.selectedShowAds !== "") url += `&showsAds=${encodeURIComponent(filters.selectedShowAds)}`;
      if (filters.selectedStatus !== "") url += `&published=${encodeURIComponent(filters.selectedStatus)}`;
      if (orderBy) {
        const backendField = reverseFieldMapping[orderBy] || orderBy;
        url += `&orderBy=${encodeURIComponent(backendField)}&orderDir=${order}`;
      }

      const response = await apiClient.get(url);
      const articlesData = response.data.data || [];
      const totalItems = response.data.pagination?.recordsFiltered || 0;

      const mappedArticles = articlesData.map(mapResponseToFrontend);
      setArticles(mappedArticles);
      setTotalCount(totalItems);
    } catch (error) {
      console.error("Error fetching articles:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch articles",
        timer: 3000,
        showConfirmButton: false,
      });
      setArticles([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [page, rowsPerPage, filters, orderBy, order]);

  const fetchDomains = async () => {
    try {
      const response = await apiClient.get("/Domain/GetDropDown")
      setDomains(response.data.data || []);
    } catch (error) {
      console.error("Error fetching domains:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load domains",
        timer: 3000,
        showConfirmButton: false,
      });
      setDomains([]);
    }
  };

  const fetchSubdomains = async (domainId, isForFilter = false) => {
    if (!domainId) {
      setSubdomains([]);
      if (isForFilter) {
        setFilters((prev) => ({ ...prev, selectedSubdomain: "" }));
      } else {
        setModalSelectedSubdomain("");
      }
      return;
    }
    try {
      const response = await apiClient.get(`/SubDomain/GetDropDown?DomainId=${domainId}`);
      setSubdomains(response.data.data || []);
    } catch (error) {
      console.error(`Error fetching subdomains${isForFilter ? " for filter" : ""}:`, error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load subdomains",
        timer: 3000,
        showConfirmButton: false,
      });
      setSubdomains([]);
      if (isForFilter) {
        setFilters((prev) => ({ ...prev, selectedSubdomain: "" }));
      } else {
        setModalSelectedSubdomain("");
      }
    }
  };
  const fetchCategories = useCallback(async (params = {}) => {
    try {
      // setShowLoader(true);
      const queryParams = new URLSearchParams();
      if (params.search) queryParams.append('q', params.search);
      if (params.domainId) queryParams.append('domainId', params.domainId);

      const response = await apiClient.get(`/category/GetDropdown?${queryParams.toString()}`);
      if (response.status === 200) {
        setCategories(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch categories",
        timer: 3000,
        showConfirmButton: false,
      });
      setCategories([]);
    } finally {
      // setShowLoader(false);
    }
  }, []);

  const fetchCampaigns = useCallback(async (subdomainId) => {
    if (!subdomainId) {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
      return;
    }

    try {
      const response = await apiClient.get(`/Campaigns/GetDropdown?subdomainId=${subdomainId}`);
      setCampaigns(response.data.data || []);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch campaigns",
        timer: 3000,
        showConfirmButton: false,
      });
      setCampaigns([]);
    }
  }, []);

  const fetchStyleIds = useCallback(async () => {
    try {
      const response = await apiClient.get("/StyleIds/getdropdown");
      setStyleIds(response.data.data || []);
    } catch (error) {
      console.error("Error fetching style IDs:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch style IDs",
        timer: 3000,
        showConfirmButton: false,
      });
      setStyleIds([]);
    }
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await apiClient.get("/adminuser/GetDropdown");
      setUsers(response.data.data || []);
    } catch (error) {
      console.error("Error fetching users:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch users",
        timer: 3000,
        showConfirmButton: false,
      });
      setUsers([]);
    }
  };

  const fetchChannels = useCallback(async (styleIdLm, styleIdDm, articleId) => {
    try {
      // Only fetch channels if showAds is true and at least one style ID is provided
      if (!showAds && !(styleIdLm || styleIdDm)) {
        console.warn("Skipping fetchChannels — showAds is false or no style IDs provided");
        setAssignChannels([]);
        return;
      }
  
      const params = new URLSearchParams();
      if (styleIdLm) params.append('styleIdLm', styleIdLm);
      if (styleIdDm) params.append('styleIdDm', styleIdDm);
      if (articleId) params.append('articleId', articleId);
  
      const response = await apiClient.get(`/Channals/GetChannalsByStyleId?${params.toString()}`);
      
      setAssignChannels(response.data.dataWithCustomChannelId || []);
    } catch (error) {
      console.error("Error fetching channels:", error.response || error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch channels",
        timer: 3000,
        showConfirmButton: false,
      });
      setAssignChannels([]);
    }
  }, [styleIdDm, styleIdLm]);
  
  const fetchCustomerOptions = useCallback(async (subDomainId = null) => {
    try {
      let url = "/AccountDetails/GetDropDown";
      if (subDomainId) {
        url = `/AccountDetails/GetAccountBySubDomain?subDomainId=${subDomainId}`;
      }

      const response = await apiClient.get(url);
      setCustomerOptions(response.data.data || []);
    } catch (error) {
      console.error("Error fetching customer options:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch customer options",
        timer: 3000,
        showConfirmButton: false,
      });
      setCustomerOptions([]);
    }
  }, []);

// Consolidated function to fetch campaigns with pagination and sorting
const fetchCampaignsData = useCallback(
  async (rowData, page, rowsPerPage, orderBy, orderDir, searchTerm = "") => {
    const articleId = rowData?.id || rowData?.Id;
    const subdomainId = rowData?.subdomain || rowData?.SubDomainId;

    if (!articleId) {
      showAlert({
        icon: "error",
        title: "Error",
        text: "Invalid article ID. Please ensure the article has a valid ID.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    if (!subdomainId) {
      showAlert({
        icon: "error",
        title: "Error",
        text: "No subdomain associated with this article.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    try {
      setCampaignModalLoading(true);
      setSelectedArticleForCampaigns(rowData);

      // Construct the API URL with pagination, sorting, and search parameters
      let url = `/Campaigns/GetData?articleId=${articleId}&page=${
        page + 1
      }&length=${rowsPerPage}&orderBy=${orderBy}&orderDir=${orderDir}`;
      if (searchTerm) {
        url += `&q=${encodeURIComponent(searchTerm)}`;
      }

      // Fetch campaigns and dropdown options concurrently
      const [campaignsResponse, customerOptionsResponse] = await Promise.all([
        apiClient.get(url),
        apiClient.get(`/Campaigns/GetDropdown?subdomainId=${subdomainId}`),
        fetchCustomerOptions(subdomainId),
      ]);

      setArticleCampaigns(campaignsResponse.data.data || []);
      setCampaignTotalCount(campaignsResponse.data.pagination?.recordsFiltered || 0);
      setCampaigns(customerOptionsResponse.data.data || []);
    } catch (error) {
      console.error("Error fetching campaigns data:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch campaigns data",
        timer: 3000,
        showConfirmButton: false,
      });
      setArticleCampaigns([]);
      setCampaignTotalCount(0);
    } finally {
      setCampaignModalLoading(false);
    }
  },
  [fetchCustomerOptions]
);



const handleViewCampaignsClick = useCallback(
  async (rowData) => {
    setShowCampaignModal(true);
    setCampaignPage(0); // Reset to first page
    setCampaignOrder("asc");
    setCampaignOrderBy("CreatedAt");
    setCampaignSearchTerm("");
    setCampaignTotalCount(0);
    await fetchCampaignsData(
      rowData,
      0, // Initial page
      campaignRowsPerPage,
      "CreatedAt",
      "asc",
      "" // Initial search term
    );
  },
  [fetchCampaignsData, campaignRowsPerPage]
);
const handleAddCampaign = useCallback(
  async (e) => {
    e.preventDefault();

    // Simple validation: check required fields
    const { articleId, budgetName, budgetAmountMicros, campaignName, customerId } = campaignFormData;
    if (!budgetName || !budgetAmountMicros || !campaignName || !customerId) {
      showAlert({
        icon: "warning",
        title: "Incomplete Data",
        text: "Please fill in all required fields before submitting.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    try {
      setCampaignModalLoading(true);

      const updatedCampaignFormData = {
        ...campaignFormData,
        articleId: selectedArticleForCampaigns.id
      };

      await apiClient.post("/Campaigns/Add", updatedCampaignFormData);

      showAlert({
        icon: "success",
        title: "Success",
        text: "Campaign created successfully",
        timer: 2000,
        showConfirmButton: false,
      });

      setCampaignFormData({
        articleId: "",
        budgetName: "",
        budgetAmountMicros: "",
        campaignName: "",
        customerId: "",
        campaignId: "",
      });
      setShowAddCampaignForm(false);
      setShowAddCampaignFormClick(false);

      if (selectedArticleForCampaigns) {
        await handleViewCampaignsClick(selectedArticleForCampaigns);
      }
      setShowAddCampaignModal(false);
    } catch (error) {
      console.error("Error creating campaign:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to create campaign",
        timer: 3000,
        showConfirmButton: false,
      });
      setShowAddCampaignModal(false);
    } finally {
      setCampaignModalLoading(false);
    }
  },
  [campaignFormData, selectedArticleForCampaigns, handleViewCampaignsClick],
);

  const handleAssignCampaign = async (e) => {
    e.preventDefault();

    if (!campaignFormData.campaignId || !selectedArticleForCampaigns?.id) {
      showAlert({
        icon: "error",
        title: "Error",
        text: "Please select a campaign and ensure an article is selected.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    try {
      setCampaignModalLoading(true);

      const res = await apiClient.post(
        `/Campaigns/ArticleCampaignMappings?articleId=${selectedArticleForCampaigns.id}&campaignId=${Number(campaignFormData.campaignId)}`,
        {}
      );      

      showAlert({
        icon: res.data.message ? "warning" : "success",
        title: "Success",
        text: res.data.message || "Campaign assigned successfully",
        timer: 2000,
        showConfirmButton: false,
      });
  
      // Reset the campaignId in campaignFormData to clear the dropdown
      setCampaignFormData((prev) => ({
        ...prev,
        campaignId: "",
      }));
  
      await fetchArticles();
      await handleViewCampaignsClick(selectedArticleForCampaigns);
    } catch (error) {
      console.error("Error assigning campaign:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to assign campaign",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setCampaignModalLoading(false);
    }
  };

  const handleCampaignFormChange = useCallback((e) => {
    const { name, value } = e.target;
    setCampaignFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleCampaignPageChange = useCallback(
    async (newPage) => {
      setCampaignPage(newPage);
      if (selectedArticleForCampaigns) {
        await fetchCampaignsData(
          selectedArticleForCampaigns,
          newPage,
          campaignRowsPerPage,
          campaignOrderBy,
          campaignOrder,
          campaignSearchTerm
        );
      }
    },
    [
      selectedArticleForCampaigns,
      campaignRowsPerPage,
      campaignOrderBy,
      campaignOrder,
      campaignSearchTerm,
      fetchCampaignsData,
    ]
  );

  // Handle rows per page change
const handleCampaignRowsPerPageChange = useCallback(
  async (newRowsPerPage) => {
    setCampaignRowsPerPage(newRowsPerPage);
    setCampaignPage(0); // Reset to first page
    if (selectedArticleForCampaigns) {
      await fetchCampaignsData(
        selectedArticleForCampaigns,
        0,
        newRowsPerPage,
        campaignOrderBy,
        campaignOrder,
        campaignSearchTerm
      );
    }
  },
  [
    selectedArticleForCampaigns,
    campaignOrderBy,
    campaignOrder,
    campaignSearchTerm,
    fetchCampaignsData,
  ]
);


// Handle sorting
const handleCampaignRequestSort = useCallback(
  async (event, property) => {
    const isAsc = campaignOrderBy === property && campaignOrder === "asc";
    setCampaignOrder(isAsc ? "desc" : "asc");
    setCampaignOrderBy(property);
    setCampaignPage(0); // Reset to first page
    if (selectedArticleForCampaigns) {
      await fetchCampaignsData(
        selectedArticleForCampaigns,
        0,
        campaignRowsPerPage,
        property,
        isAsc ? "desc" : "asc",
        campaignSearchTerm
      );
    }
  },
  [
    selectedArticleForCampaigns,
    campaignRowsPerPage,
    campaignOrderBy,
    campaignOrder,
    campaignSearchTerm,
    fetchCampaignsData,
  ]
);

// Handle search change
const handleCampaignSearchChange = useCallback(
  async (value) => {
    setCampaignSearchTerm(value);
    setCampaignPage(0); // Reset to first page
    if (selectedArticleForCampaigns) {
      await fetchCampaignsData(
        selectedArticleForCampaigns,
        0,
        campaignRowsPerPage,
        campaignOrderBy,
        campaignOrder,
        value
      );
    }
  },
  [
    selectedArticleForCampaigns,
    campaignRowsPerPage,
    campaignOrderBy,
    campaignOrder,
    fetchCampaignsData,
  ]
);
  const handleRemoveCampaign = useCallback(
    async (campaignData) => {
      if (!campaignData?.Id) {
        showAlert({
          icon: "error",
          title: "Error",
          text: "Invalid campaign data",
          timer: 3000,
          showConfirmButton: false,
        });
        return;
      }

      try {
        const result = await Swal.fire({
          icon: "warning",
          title: "Confirm Removal",
          text: `Are you sure you want to remove campaign "${campaignData.Name}"?`,
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: "Yes, remove it!",
          cancelButtonText: "Cancel",
        });

        if (result.isConfirmed) {
          setCampaignModalLoading(true);
          await apiClient.delete("/Campaigns/Delete", {
            data: { Id: campaignData.Id },
          });

          showAlert({
            icon: "success",
            title: "Success",
            text: "Campaign removed successfully",
            timer: 2000,
            showConfirmButton: false,
          });
          await fetchArticles();
          if (selectedArticleForCampaigns) {
            await handleViewCampaignsClick(selectedArticleForCampaigns);
          }
        }
      } catch (error) {
        console.error("Error removing campaign:", error);
        showAlert({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to remove campaign",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setCampaignModalLoading(false);
      }
    },
    [selectedArticleForCampaigns, handleViewCampaignsClick, fetchArticles],
  );

 const handleFormSubmit = async (e) => {
    e.preventDefault();

    const updatedFormData = {
      ...formData,
      title: {
        val: formData.title.val,
        err: validateField("title", formData.title.val, showAds),
      },
      category: {
        val: formData.category.val,
        err: validateField("category", formData.category.val, showAds),
      },
      description: {
        val: description,
        err: validateField("description", description, showAds),
      },
      shortDescription: {
        val: shortDescription,
        err: validateField("shortDescription", shortDescription, showAds),
      },
      domain: {
        val: modalSelectedDomain,
        err: validateField("domain", modalSelectedDomain, showAds),
      },
      subdomain: {
        val: modalSelectedSubdomain,
        err: validateField("subdomain", modalSelectedSubdomain, showAds),
      },
      shortName: {
        val: formData.shortName.val,
        err: validateField("shortName", formData.shortName.val, showAds),
      },
      customChannal: {
        val: formData.customChannal.val,
        err: validateField("customChannal", formData.customChannal.val, showAds),
      },
      styleIdLm: {
        val: formData.styleIdLm.val,
        err: validateField("styleIdLm", formData.styleIdLm.val, showAds),
      },
      styleIdDm: {
        val: formData.styleIdDm.val,
        err: validateField("styleIdDm", formData.styleIdDm.val, showAds),
      },
      adrelatedsearches: {
        val: formData.adrelatedsearches.val,
        err: validateField("adrelatedsearches", formData.adrelatedsearches.val, showAds),
      },
      adrelatedsearches2: {
        val: formData.adrelatedsearches2?.val || "",
        err: validateField("adrelatedsearches2", formData.adrelatedsearches2?.val || "", showAds),
      },
      readTime: {
        val: formData.readTime.val,
        err: validateField("readTime", formData.readTime.val, showAds),
      },
    };

    const hasErrors = Object.values(updatedFormData).some((field) => field.err);
    if (hasErrors) {
      setFormData(updatedFormData);
      showAlert({
        icon: "error",
        title: "Validation Error",
        text: "Please fill all required fields",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    if (formData.adrelatedsearches2?.val && !showSecondRelatedSearch) {
      showAlert({
        icon: "error",
        title: "Validation Error",
        text: "CheckRelatedSearches2 must be true if AdRelatedSearches2 is provided",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    setShowLoader(true);
    try {
      const formDataPayload = new FormData();

      const urlSlug = formData.url.val.split("/").pop() || autoslug(formData.title.val);
  
      // Always send all fields (use empty string if blank)
      formDataPayload.append("Title", formData.title.val || "");
      formDataPayload.append("Category", formData.category.val || "");
      formDataPayload.append("Url", urlSlug);
      formDataPayload.append("MetaTitle", formData.metatitle.val || "");
      formDataPayload.append("MetaDescription", formData.metadescription.val || "");
      formDataPayload.append("MetaKeys", formData.metakeys.val || "");
      formDataPayload.append("Description", description || "");
      formDataPayload.append("ShortDescription", shortDescription || "");
      formDataPayload.append("CustomChannal", formData.customChannal.val || "");
      formDataPayload.append("ShortName", formData.shortName.val || "");
      formDataPayload.append("StyleIdLm", showAds ? (formData.styleIdLm.val || "") : "");
      formDataPayload.append("StyleIdDm", showAds ? (formData.styleIdDm.val || "") : "");
      formDataPayload.append("AdRelatedSearches", showAds ? (formData.adrelatedsearches.val || "") : "");
      formDataPayload.append("AdReletadSearches2", showAds ? (formData.adrelatedsearches2?.val || "") : "");
      formDataPayload.append("Remark", formData.remark?.val || "");
      formDataPayload.append("Hashtag", formData.hashtag?.val || "");
      formDataPayload.append("Published", String(published));
      formDataPayload.append("ShowArticle", String(showArticle));
      formDataPayload.append("ShowInDomain", String(showInDomain));
      formDataPayload.append("ShowsAds", String(showAds));
      formDataPayload.append("CheckRelatedSearches2", String(showSecondRelatedSearch));
      formDataPayload.append("ReadTime", formData.readTime.val || "");
      formDataPayload.append("Domain", modalSelectedDomain || "");
      formDataPayload.append("SubDomain", modalSelectedSubdomain || "");
  
      if (formdataImage && typeof formdataImage === "object") {
        formDataPayload.append("file", formdataImage);
      }

      if (editId) {
        formDataPayload.append("Id", editId);
      }

      const numericCampaignIds = Array.isArray(selectedCampaignIds) 
      ? selectedCampaignIds.map(id => Number(id)) 
      : [];
      formDataPayload.append("CampaignIds", JSON.stringify(numericCampaignIds));

      const endpoint = editId ? "/article/edit" : "/article/add";
      const method = editId ? "PUT" : "POST";

      const response = await apiClient.request({
        url: endpoint,
        method,
        data: formDataPayload,
        headers: { "Content-Type": "multipart/form-data" },
      });
  
      if ([200, 201].includes(response.status)) {
        showAlert({
          icon: "success",
          title: "Success",
          text: editId ? "Article updated successfully" : "Article created successfully",
          timer: 3000,
          showConfirmButton: false,
        });
        resetFormState();
        fetchArticles();
        setModalShow(false);
      }
    } catch (error) {
      console.error("Error saving article:", error.response?.data || error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to save article",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const resetFormState = () => {
    setEditId(null);
    setFormData(defaultValuesForm);
    setDescription("");
    setShortDescription("");
    setFormdataImage("");
    setBase64Image("");
    setPublished(true);
    setShowArticle(false);
    setShowInDomain(false);
    setShowAds(false);
    setModalSelectedDomain("");
    setModalSelectedSubdomain("");
    setSelectedCampaignIds([]);
    setEditSlugMode(false);
    // Do NOT reset filter states (filters object)
  };

  const handleFilterChange = (name, value) => {
    setFilters((prev) => {
      const newFilters = { ...prev, [name]: value };
      if (name === "selectedDomain" && !value) {
        // When domain is cleared, also clear subdomain
        newFilters.selectedSubdomain = "";
        setSubdomains([]);
      }
      return newFilters;
    });
    setPage(0);
  };

  const handleChange = (e) => {
    const { name, value, files } = e.target || e;
    if (name === "title") {
      const shouldUpdateSlug = !editId || editSlugMode;
      setFormData((prev) => ({
        ...prev,
        [name]: { val: value, err: validateField(name, value) },
        ...(shouldUpdateSlug && { url: { val: autoslug(value), err: "" } }),
      }));
      return;
    }
    if (name === "url") {
      setFormData((prev) => ({
        ...prev,
        url: { val: autoslug(value), err: "" },
      }));
      return;
    }
    if (files && files.length > 0) {
      setFormdataImage(files[0]);
      setBase64Image("");
      return;
    }
    setFormData((prev) => ({
      ...prev,
      [name]: { val: value, err: validateField(name, value) },
    }));
  };

  const handleCampaignSelect = (campaignIds) => {
    setSelectedCampaignIds(campaignIds);
    setFormData((prev) => ({
      ...prev,
      campaigns: { val: campaignIds, err: "" },
    }));
  };

  const handleChannelSelect = (channel) => {
    setFormData((prev) => ({
      ...prev,
      customChannal: {
        val: channel?.Id || "",
        err: validateField("customChannal", channel?.Id || ""),
      },
    }));
  };

  const handleEditArticle = useCallback(
    async (rowData) => {
      try {
        // setShowLoader(true);
        const loadPromises = [];

        // Fetch required data only if not already loaded
        // if (assignChannels.length === 0) {
        //   loadPromises.push(fetchChannels());
        // }
        if (domains.length === 0) {
          loadPromises.push(fetchDomains());
        }
        if (styleIds.length === 0) {
          loadPromises.push(fetchStyleIds());
        }
        await Promise.all(loadPromises);
        const response = await apiClient.get(
          `/article/GetById?id=${rowData.id}`
        );

        if (response.data.success && response.data.data?.length > 0) {
          const article = response.data.data[0];
          const showAds = !!article.ShowsAds;
   
          setModalShow(true);
          setEditId(article.Id);
          setEditSlugMode(false);
  
          setPublished(!!article.Published);
          setShowArticle(!!article.ShowArticle);
          setShowInDomain(!!article.ShowInDomain);
          setShowAds(!!article.ShowsAds);
          setShowSecondRelatedSearch(!!article.CheckRelatedSearches2);
  
          setDescription(article.Description || "");
          setShortDescription(article.ShortDescription || "");

          // Fetch channels only if showAds is true and style IDs are available
        if (showAds && (article.StyleIdLm || article.StyleIdDm)) {
          await fetchChannels(article.StyleIdLm, article.StyleIdDm, article.Id);
        } else {
          setAssignChannels([]);
        }
          const formDataToSet = {
            ...defaultValuesForm,
            title: { val: article.Title || "", err: "" },
            category: {
              val: article.CategoryId || "",
              err: validateField("category", article.CategoryId || "", showAds),
            },
            url: { val: article.Url || "", err: "" },
            shortName: { val: article.ShortName || "", err: "" },
            description: {
              val: article.Description || "",
              err: validateField("description", article.Description || "", showAds),
            },
            shortDescription: {
              val: article.ShortDescription || "",
              err: validateField("shortDescription", article.ShortDescription || "", showAds),
            },
            metatitle: { val: article.MetaTitle || "", err: "" },
            metadescription: {
              val: article.MetaDescription || "",
              err: validateField("metadescription", article.MetaDescription || "", showAds),
            },
            metakeys: {
              val: article.MetaKeys || "",
              err: validateField("metakeys", article.MetaKeys || "", showAds),
            },
            hashtag: { val: article.Hashtag || "", err: "" },
            customChannal: {
              val: article.ChannelId || "",
              err: showAds ? validateField("customChannal", article.ChannelId || "", showAds) : "",
            },
            styleIdLm: {
              val: article.StyleIdLm || "",
              err: showAds ? validateField("styleIdLm", article.StyleIdLm || "", showAds) : "",
            },
            styleIdDm: {
              val: article.StyleIdDm || "",
              err: showAds ? validateField("styleIdDm", article.StyleIdDm || "", showAds) : "",
            },
            adrelatedsearches: {
              val: article.AdRelatedSearches || "7",
              err: showAds ? validateField("adrelatedsearches", article.AdRelatedSearches || "7", showAds) : "",
            },
            adrelatedsearches2: {
              val: article.AdReletadSearches2 || "",
              err: showAds && article.CheckRelatedSearches2
                ? validateField("adrelatedsearches2", article.AdReletadSearches2 || "", showAds)
                : "",
            },
            remark: { val: article.Remark || "", err: "" },
            campaigns: {
              val: article.Campaigns?.map((c) => c.SNo.toString()) || [],
              err: showAds ? validateField("campaigns", article.Campaigns?.map((c) => c.SNo) || [], showAds) : "",
            },
            domain: {
              val: article.DomainId || "",
              err: validateField("domain", article.DomainId || "", showAds),
            },
            subdomain: {
              val: article.SubDomainId || "",
              err: showAds ? validateField("subdomain", article.SubDomainId || "", showAds) : "",
            },
            readTime: {
              val: article.ReadTime?.toString() || "",
              err: validateField("readTime", article.ReadTime?.toString() || "", showAds),
            },
          };
  
          setFormData(formDataToSet);
          setModalSelectedDomain(article.DomainId || "");
          if (article.DomainId) {
            await fetchSubdomains(article.DomainId);
            setModalSelectedSubdomain(article.SubDomainId || "");
            await fetchCategories({ domainId: article.DomainId });          
            if (article.SubDomainId) {
              await fetchCampaigns(article.SubDomainId);
              if (article.Campaigns && article.Campaigns.length > 0) {
                const campaignIds = article.Campaigns.map((c) => c.SNo.toString());
                setSelectedCampaignIds(campaignIds);
              } else {
                setSelectedCampaignIds([]);
              }
            } else {
              setCampaigns([]);
              setSelectedCampaignIds([]);
            }
          } else {
            setSubdomains([]);
            setModalSelectedSubdomain("");
            setCampaigns([]);
            setSelectedCampaignIds([]);
          }

          if (article.Image) {
            setFormdataImage(
              fetchImage(
                article.Image.includes("cloudinary") ? "cloud" : "small",
                article.Image,
              ),
            );
          } else {
            setFormdataImage("");
          }
          setBase64Image("");
        } else {
          throw new Error("No article data found");
        }
      } catch (error) {
        console.error("Error fetching article:", error);
        showAlert({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to load article data",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        // setShowLoader(false);
      }
    },
    [
      fetchCampaigns,
      fetchChannels,
      fetchStyleIds,
      fetchCategories,
      fetchDomains,
      assignChannels,
      domains,
      styleIds,
      validateField,
    ],
  );
  const handleCloneArticle = useCallback(
    async (rowData) => {
      try {
        const loadPromises = [];
        if (assignChannels.length === 0) {
          loadPromises.push(fetchChannels());
        }
        if (domains.length === 0) {
          loadPromises.push(fetchDomains());
        }
        if (styleIds.length === 0) {
          loadPromises.push(fetchStyleIds());
        }

        await Promise.all(loadPromises);

        const response = await apiClient.get(`/article/GetById?id=${rowData.id}`);

        if (response.data.success && response.data.data?.length > 0) {
          const article = response.data.data[0];
          const showAds = !!article.ShowsAds;

          setModalShow(true);
          setEditId(null);
          setEditSlugMode(false);

          setPublished(!!article.Published);
          setShowArticle(!!article.ShowArticle);
          setShowInDomain(!!article.ShowInDomain)
          setShowAds(!!article.ShowsAds);
          setShowSecondRelatedSearch(!!article.CheckRelatedSearches2);

          setDescription(article.Description || "");
          setShortDescription(article.ShortDescription || "");

          const formDataToSet = {
            ...defaultValuesForm,
            title: { val: `${article.Title || ""}`, err: "" },
            category: { val: article.CategoryId || "", err: "" },
            url: { val: `${article.Url || ""}`, err: "" },
            shortName: { val: article.ShortName || "", err: "" }, // Add shortName
            description: { val: article.Description || "", err: "" },
            shortDescription: { val: article.ShortDescription || "", err: "" },
            metatitle: { val: article.MetaTitle || "", err: "" },
            metadescription: { val: article.MetaDescription || "", err: "" },
            metakeys: { val: article.MetaKeys || "", err: "" },
            hashtag: { val: article.Hashtag || "", err: "" },
            customChannal: { val: article.customChannal, err: "" },
            styleIdLm: { val: article.styleIdLm, err: "" },
            styleIdDm: { val: article.styleIdDm, err: "" },
            adrelatedsearches: { val: article.AdRelatedSearches || "7", err: "" },
            adrelatedsearches2: { val: article.AdReletadSearches2 || "", err: "" },
            remark: { val: article.Remark || "", err: "" },
            campaigns: { val: [], err: "" },
            domain: { val: "", err: "" },
            subdomain: { val: "", err: "" },
            readTime: { val: article.ReadTime?.toString() || "", err: "" },
          };

          setFormData({
            ...formDataToSet,
          });

          setModalSelectedDomain("");
          setModalSelectedSubdomain("");
          setSelectedCampaignIds([]);
          setSubdomains([]);
          setCampaigns([]);

          if (article.Image) {
            setFormdataImage(
              fetchImage(
                article.Image.includes("cloudinary") ? "cloud" : "small",
                article.Image,
              ),
            );
          } else {
            setFormdataImage("");
          }
          setBase64Image("");
        }
      } catch (error) {
        console.error("Error cloning article:", error);
        showAlert({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to clone article data",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    },
    [
      fetchChannels,
      fetchDomains,
      fetchStyleIds,
      defaultValuesForm,
      assignChannels,
      domains,
      styleIds,
    ],
  );

  const handleDeleteArticle = useCallback(
    async (rowData) => {
      const result = await Swal.fire({
        icon: "warning",
        title: "Confirm Deletion",
        text: "Are you sure you want to delete this article?",
        showCancelButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        showCloseButton: true,
        confirmButtonText: "Yes, delete it!",
      });

      if (result.isConfirmed) {
        try {
          setShowLoader(true);
          await apiClient.delete("/article/delete", {
            data: { id: rowData.id },
          });
          showAlert({
            icon: "success",
            title: "Success",
            text: "Article deleted successfully",
            timer: 3000,
            showConfirmButton: false,
          });
          await fetchArticles();
        } catch (error) {
          console.error("Error deleting article:", error);
          showAlert({
            icon: "error",
            title: "Error",
            text: error?.response?.data?.error || "Failed to delete article",
            timer: 3000,
            showConfirmButton: false,
          });
        } finally {
          setShowLoader(false);
        }
      }
    },
    [fetchArticles],
  );

  const handleAddCampaignClick = useCallback(
    async (rowData) => {
      const articleId = rowData?.id || rowData?.Id;

      if (!articleId) {
        showAlert({
          icon: "error",
          title: "Error",
          text: "Invalid article ID. Please ensure the article has a valid ID.",
          timer: 3000,
          showConfirmButton: false,
        });
        return;
      }

      try {
        setCampaignModalLoading(true);
        setSelectedArticleForCampaigns(rowData);
        setShowAddCampaignModal(true);

        const [campaignsResponse] = await Promise.all([
          apiClient.get(`/Campaigns/GetData?articleId=${articleId}`),
          fetchCustomerOptions(rowData?.subdomain || rowData?.SubDomainId),
        ]);

        setArticleCampaigns(campaignsResponse.data.data || []);
      } catch (error) {
        console.error("Error fetching article campaigns:", error);
        showAlert({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch campaigns",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setCampaignModalLoading(false);
      }
    },
    [fetchCustomerOptions],
  );

  const handleViewArticle = useCallback((rowData) => {
    if (!rowData.published) {
      showAlert({
        icon: "warning",
        title: "Warning",
        text: "Article is not published yet",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }
    const link = makeLinkRedirect(rowData);
    window.open(link, "_blank");
  }, []);

  const makeLinkRedirect = (article) => {
    if (!article.published) return "/";
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE;

    if (article.domainName) {
      const slug = article.url.split("/").pop();
      return `https://${article.domainName}/${slug}`;
    }

    const channelId = article.customChannal?.reportingDimensionId?.split(":")[1] || "";
    return `${baseUrl}/${article.url.split("/").pop()}?channel=${channelId}&mode=light`;
  };

  const autoslug = (title) =>
    title
      ? title
          .replace(/[^\p{L}\p{N}\s-]/gu, '')
          .trim()
          .replace(/\s+/g, '-')
          .toLowerCase()
      : "";

  const imageConvert = async () => {
    if (formdataImage && typeof formdataImage === "object") {
      try {
        const base64 = await convertToBase64(formdataImage);
        setBase64Image(base64);
      } catch (error) {
        console.error("Failed to convert image:", error);
        setBase64Image("");
        showAlert({
          icon: "error",
          title: "Error",
          text: "Failed to process image. Please select a valid image file.",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const accessToken = localStorage.getItem("accessToken");
    if (accessToken) {
      try {
        const decoded = decodeJWT(accessToken);
        setUserData(decoded);
        setIsSuperAdmin(decoded.User_Type === "Super Admin" || decoded.User_Type === "Admin");
      } catch (error) {
        console.error("Error decoding JWT:", error);
        showAlert({
          icon: "error",
          title: "Authentication Error",
          text: "Invalid access token. Please log in again.",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    } else {
      showAlert({
        icon: "warning",
        title: "Session Expired",
        text: "Please log in to continue.",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, [isMounted]);

  useEffect(() => {
    if (isMounted) {
      fetchDomains();
      // fetchCategories();
    }
  }, [isMounted]);

  useEffect(() => {
    if (filters.selectedDomain) {
      fetchSubdomains(filters.selectedDomain, true);
      // fetchCategories({domainId : filters.selectedDomain})
    } else {
      // fetchCategories();
      setSubdomains([]);
      setFilters((prev) => ({ ...prev, selectedSubdomain: "" }));
    }
  }, [filters.selectedDomain]);

  useEffect(() => {
    if (isSuperAdmin) {
      fetchUsers();
    }
  }, [isSuperAdmin]);

  useEffect(() => {
    if (modalShow) {
      fetchChannels();
      fetchStyleIds();
      if (domains.length === 0) {
        fetchDomains();
      }
    }
  }, [modalShow, fetchChannels, fetchStyleIds, domains.length]);

  useEffect(() => {
    if (modalSelectedDomain) {
      fetchSubdomains(modalSelectedDomain);
      fetchCategories({ domainId: modalSelectedDomain });
    }
  }, [modalSelectedDomain, fetchCategories, editId]);

  useEffect(() => {
    if (modalSelectedSubdomain) {
      fetchCampaigns(modalSelectedSubdomain);
    }
  }, [modalSelectedSubdomain, fetchCampaigns, editId]);

  useEffect(() => {
    imageConvert();
  }, [formdataImage]);

  useEffect(() => {
    fetchArticles();
  }, [
    debouncedSearchTerm,
    filters,
    page,
    rowsPerPage,
    orderBy,
    order,
    fetchArticles,
    handleAddCampaign
  ]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        // Handle click outside if needed
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (modalShow  && (formData.styleIdLm.val || formData.styleIdDm.val)) {
      fetchChannels(formData.styleIdLm.val, formData.styleIdDm.val, editId);
    } else if (modalShow && !showAds) {
      setAssignChannels([]);
    }
  }, [modalShow, formData.styleIdLm.val, formData.styleIdDm.val, editId, fetchChannels]);

  const filteredCampaigns = campaignSearchTerm
    ? articleCampaigns.filter((campaign) =>
        campaign.Name.toLowerCase().includes(campaignSearchTerm.toLowerCase()),
      )
    : articleCampaigns;

  return (
    <>
      <div className="font-poppins relative min-h-screen w-full overflow-x-hidden bg-gray-50">
        <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white">
              Articles Management
            </h1>
          </div>
        </div>

        <div className="absolute left-1/2 top-[90px] w-[95%] -translate-x-1/2 rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
          <div className="mb-5 flex flex-col flex-wrap gap-4">
            <div className="flex items-center justify-between gap-4">
              <div className="flex flex-wrap items-center gap-4">
                <div className="w-full mt-4 sm:w-64">
                  <Button
                    type="button"
                    label="Add Article"
                    variant="primary"
                    shape="rounded"
                    className="flex w-full items-center justify-center gap-2 sm:w-64"
                    icon={<FaPlus size={14} />}
                    onClick={() => {
                      setModalShow(true);
                      resetFormState();
                    }}
                  />
                </div>
                <div className="w-full  sm:w-64">
                  <InputGroup
                    label="Search"
                    placeholder="Search..."
                    value={filters.searchTerm}
                    handleChange={(e) => handleFilterChange("searchTerm", e.target.value)}
                    type="text"
                    className="w-full"
                  />
                </div>
                <div className="w-full z-40 sm:w-64">
                  <SearchableDropdown
                    label="Domain"
                    options={[{ Id: "", Name: "All Domains" }, ...domains]}
                    placeholder="Select Domain..."
                    value={filters.selectedDomain}
                    onChange={(domain) => handleFilterChange("selectedDomain", domain?.Id || "")}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
                {isSuperAdmin && (
                  <div className="w-full z-70 sm:w-64">
                    <SearchableDropdown
                      options={[{ Id: "", Name: "All Users" }, ...users]}
                      label="Users"
                      placeholder="Select User..."
                      value={filters.selectedUser}
                      onChange={(user) => handleFilterChange("selectedUser", user?.Id || "")}
                      displayKey="Name"
                      idKey="Id"
                    />
                  </div>
                )}
              </div>
              <IconButton
                onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                className="hover:bg-primary-dark flex items-center justify-center rounded-md bg-primary p-2 text-white"
                aria-label={isFilterExpanded ? "Collapse Filters" : "Expand Filters"}
              >
                <FaFilter size={14} />
              </IconButton>
            </div>
            {isFilterExpanded && (
              <div className="flex z-1 flex-wrap items-center gap-4">
                {/* <div className="w-full z-40 sm:w-64">
                  <SearchableDropdown
                    options={[{ Id: "", Name: "All Categories" }, ...categories]}
                    label="Categories"
                    placeholder="Select Category..."
                    value={filters.selectedCategory}
                    onChange={(category) => handleFilterChange("selectedCategory", category?.Id || "")}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div> */}
                <div className="w-full z-30 sm:w-64">
                  <SearchableDropdown
                    label="Subdomain"
                    options={[{ Id: "", Name: "All Subdomains" }, ...subdomains]}
                    placeholder="Select Subdomain..."
                    value={filters.selectedSubdomain}
                    onChange={(subdomain) => handleFilterChange("selectedSubdomain", subdomain?.Id || "")}
                    displayKey="Name"
                    idKey="Id"
                    disabled={!filters.selectedDomain || !subdomains.length}
                  />
                </div>
                <div className="w-full z-20 sm:w-64">
                  <SearchableDropdown
                    label="Show Ads"
                    options={[
                      { Id: "-1", Name: "All" },
                      { Id: "0", Name: "True" },
                      { Id: "1", Name: "False" },
                    ]}
                    placeholder="Select Show Ads..."
                    value={filters.selectedShowAds}
                    onChange={(option) => handleFilterChange("selectedShowAds", option?.Id || "")}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
                <div className="w-full sm:w-64">
                  <SearchableDropdown
                    label="Status"
                    options={[
                      { Id: "-1", Name: "All" },
                      { Id: "0", Name: "Published" },
                      { Id: "1", Name: "Draft" },
                    ]}
                    placeholder="Select Status..."
                    value={filters.selectedStatus}
                    onChange={(option) => handleFilterChange("selectedStatus", option?.Id || "")}
                    displayKey="Name"
                    idKey="Id"
                  />
                </div>
              </div>
            )}
          </div>
          <CustomDataTable
            isLoading={showLoader}
            columns={columns}
            rows={articles}
            searchTerm={filters.searchTerm}
            onSearchChange={(value) => handleFilterChange("searchTerm", value)}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={setPage}
            onRequestSort={(_, property) => {
              const isAsc = orderBy === property && order === "asc";
              setOrder(isAsc ? "desc" : "asc");
              setOrderBy(property);
            }}
            onRowsPerPageChange={(value) => {
              setRowsPerPage(value);
              setPage(0);
            }}
            totalCount={totalCount}
            order={order}
            orderBy={orderBy}
            onView={handleViewArticle}
            onEdit={handleEditArticle}
            onClone={handleCloneArticle}
            onDelete={handleDeleteArticle}
            handleViewCampaignsClick={handleViewCampaignsClick}
            handleAddCampaignClick={handleAddCampaignClick}
          />
        </div>
      </div>

      <ArticleModal
        open={modalShow}
        onClose={() => {
          setModalShow(false);
          resetFormState();
        }}
        mode={editId ? "edit" : "add"}
        formData={formData}
        setFormData={setFormData}
        description={description}
        setDescription={setDescription}
        shortDescription={shortDescription}
        setShortDescription={setShortDescription}
        formdataImage={formdataImage}
        setFormdataImage={setFormdataImage}
        base64Image={base64Image}
        setBase64Image={setBase64Image}
        published={published}
        setPublished={setPublished}
        showArticle={showArticle}
        setShowArticle={setShowArticle}
        showInDomain={showInDomain}
        setShowInDomain={setShowInDomain}
        showAds={showAds}
        setShowAds={setShowAds}
        selectedDomain={modalSelectedDomain}
        setSelectedDomain={setModalSelectedDomain}
        selectedSubdomain={modalSelectedSubdomain}
        setSelectedSubdomain={setModalSelectedSubdomain}
        selectedCampaignIds={selectedCampaignIds}
        setSelectedCampaignIds={setSelectedCampaignIds}
        domains={domains}
        subdomains={subdomains}
        categories={categories}
        assignChannels={assignChannels}
        styleIds={styleIds}
        campaigns={campaigns}
        editId={editId}
        editSlugMode={editSlugMode}
        setEditSlugMode={setEditSlugMode}
        handleFormSubmit={handleFormSubmit}
        handleChange={handleChange}
        handleChannelSelect={handleChannelSelect}
        handleCampaignSelect={handleCampaignSelect}
        validateField={validateField}
        autoslug={autoslug}
        showLoader={showLoader}
        showSecondRelatedSearch={showSecondRelatedSearch}
        setShowSecondRelatedSearch={setShowSecondRelatedSearch}
      />

      <Dialog
        open={showCampaignModal}
        onClose={(event, reason) => {
          if (reason === "backdropClick") return;
          setShowCampaignModal(false);
          setSelectedArticleForCampaigns(null);
          setArticleCampaigns([]);
          setShowAddCampaignForm(false);
          setShowAddCampaignFormClick(false);
          setCampaignSearchTerm("");
          setCampaignFormData({
            articleId: "",
            budgetName: "",
            budgetAmountMicros: "",
            campaignName: "",
            customerId: "",
            campaignId: "",
          });
          setCampaignPage(0);
          setCampaignRowsPerPage(10);
          setCampaignOrder("asc");
          setCampaignOrderBy("CreatedAt");
          setCampaignTotalCount(0);
        }}
        fullWidth
        maxWidth="md"
        slotProps={{
          paper: {
            sx: {
              maxHeight: "90vh",
              zIndex: 1300,
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span className="text-xl font-medium">
            Campaigns for{" "}
            <span className="font-bold">
              "{selectedArticleForCampaigns?.title}"
            </span>
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setShowCampaignModal(false);
              setSelectedArticleForCampaigns(null);
              setArticleCampaigns([]);
              setShowAddCampaignForm(false);
              setShowAddCampaignFormClick(false);
              setCampaignSearchTerm("");
              setCampaignPage(0);
              setCampaignRowsPerPage(10);
              setCampaignOrder("asc");
              setCampaignOrderBy("CreatedAt");
              setCampaignTotalCount(0);
            }}
            sx={{ color: "white" }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          <>
            <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                <div className="min-w-72.5">
                  <SearchableDropdown
                    label="Select Campaign"
                    className="overflow-hidden"
                    options={campaigns}
                    placeholder="Select a campaign..."
                    value={campaignFormData.campaignId || ""}
                    onChange={(campaign) => {
                      setCampaignFormData((prev) => ({
                        ...prev,
                        campaignId: campaign?.SNo || "",
                      }));
                    }}
                    displayKey="Name"
                    idKey="SNo"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className="mt-4"
                  label={"Assign Campaign"}
                  variant="primary"
                  shape="rounded"
                  onClick={handleAssignCampaign}
                />
              </div>
            </div>

            {/* <CustomDataTable
              isLoading={campaignModalLoading}
              columns={[
                { id: "CampaignId", label: "Campaign ID" },
                { id: "Name", label: "Campaign Name" },
                { id: "CreatedAt", label: "Added Date" },
              ]}
              rows={filteredCampaigns}
              page={0}
              rowsPerPage={10}
              onPageChange={() => {}}
              onRowsPerPageChange={() => {}}
              totalCount={articleCampaigns.length}
              searchTerm={campaignSearchTerm}
              onSearchChange={setCampaignSearchTerm}
              order="asc"
              orderBy=""
              onRequestSort={() => {}}
              onDelete={handleRemoveCampaign}
              showDeleteAction={true}
            /> */}

            <CustomDataTable
              isLoading={campaignModalLoading}
              columns={[
                { id: "CampaignId", label: "Campaign ID" },
                { id: "Name", label: "Campaign Name" },
                { id: "CreatedAt", label: "Added Date" },
              ]}
              rows={articleCampaigns} // Use raw API data since search is handled server-side
              page={campaignPage}
              rowsPerPage={campaignRowsPerPage}
              onPageChange={handleCampaignPageChange}
              onRowsPerPageChange={handleCampaignRowsPerPageChange}
              totalCount={campaignTotalCount}
              searchTerm={campaignSearchTerm}
              onSearchChange={handleCampaignSearchChange}
              order={campaignOrder}
              orderBy={campaignOrderBy}
              onRequestSort={handleCampaignRequestSort}
              onDelete={handleRemoveCampaign}
              showDeleteAction={true}
            />
          </>
        </DialogContent>

        <DialogActions sx={{ py: 2, px: 3 }}>
          <Button
            type="button"
            label="Cancel"
            onClick={() => {
              setShowCampaignModal(false);
              setSelectedArticleForCampaigns(null);
              setArticleCampaigns([]);
              setCampaignSearchTerm("");
              setCampaignFormData({
                articleId: "",
                budgetName: "",
                budgetAmountMicros: "",
                campaignName: "",
                customerId: "",
                campaignId: "",
              });
              setCampaignPage(0);
              setCampaignRowsPerPage(10);
              setCampaignOrder("asc");
              setCampaignOrderBy("CreatedAt");
              setCampaignTotalCount(0);
            }}
            variant="dark"
            shape="rounded"
          />
        </DialogActions>
      </Dialog>

      <Dialog
        open={showAddCampaignModal}
        onClose={(event, reason) => {
          if (reason === "backdropClick") return;
          setShowAddCampaignModal(false);
          setSelectedArticleForCampaigns(null);
          setArticleCampaigns([]);
          setShowAddCampaignFormClick(false);
          setCampaignSearchTerm("");
        }}
        fullWidth
        maxWidth="sm"
        slotProps={{
          paper: {
            sx: {
              height: "67vh",
              maxHeight: "90vh",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span className="text-xl font-medium">
            Campaigns for{" "}
            <span className="font-bold">
              "{selectedArticleForCampaigns?.title}"
            </span>
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setShowAddCampaignModal(false);
              setSelectedArticleForCampaigns(null);
              setArticleCampaigns([]);
              setShowAddCampaignFormClick(false);
              setCampaignSearchTerm("");
            }}
            sx={{ color: "white" }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          <form onSubmit={handleAddCampaign}>
            <h3 className="mb-4 text-lg font-semibold">Add New Campaign</h3>
              <SearchableDropdown
                label="Customer ID"
                options={customerOptions}
                placeholder="Select Customer..."
                value={campaignFormData.customerId}
                onChange={(customer) => {
                  setCampaignFormData((prev) => ({
                    ...prev,
                    customerId: customer?.AccountId || "",
                  }));
                }}
                displayKey="DescriptiveName"
                idKey="AccountId"
                required
              />
              <InputGroup
                label="Budget Name"
                name="budgetName"
                type="text"
                value={campaignFormData.budgetName}
                handleChange={handleCampaignFormChange}
                placeholder="Enter budget name"
                required
              />
              <InputGroup
                label="Budget Amount (Micros)"
                name="budgetAmountMicros"
                value={campaignFormData.budgetAmountMicros}
                handleChange={handleCampaignFormChange}
                placeholder="Enter budget amount in micros"
                required
              />
              <InputGroup
                label="Campaign Name"
                name="campaignName"
                type="text"
                value={campaignFormData.campaignName}
                handleChange={handleCampaignFormChange}
                placeholder="Enter campaign name"
                required
              />
          </form>
        </DialogContent>

        <DialogActions sx={{ py: 2, px: 2 }}>
          <Button
            type="submit"
            label="Create Campaign"
            variant="primary"
            shape="rounded"
            disabled={campaignModalLoading}
            onClick={handleAddCampaign}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ArticlePage;