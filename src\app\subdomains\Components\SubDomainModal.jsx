"use client";

import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { CloseIcon } from "@/assets/icons";
import apiClient from "@/utils/axiosInstance";

const SubDomainModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isEdit,
  users,
  accountDetails,
  selectedDomain,
}) => {
  const [formData, setFormData] = useState({
    Name: "",
    Url: "",
    Domain: "",
    AssignUsers: [],
    AccountId: [],
    HeadTagScript: "",
    HeadTagScriptLandingPage: "",
    HeadTagScriptSearchPage: "",
    GId: "",
    AWId: "", // Commented out
    SendTo: "", // Commented out
    AdsProviderId: "", // New field for Ads Provider
    TrackingKey: "", // New field for Tracking Key
  });

  const [adsProviders, setAdsProviders] = useState({
    providersList: [],
    isLoading: false,
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (isEdit && initialData) {
      setFormData({
        Name: initialData.Name || "",
        Url: initialData.Url || "",
        Domain: selectedDomain?.Id || "",
        AssignUsers: initialData.UserMappings?.map((mapping) => mapping) || [],
        AccountId: initialData.AccountId?.map((mapping) => mapping) || [],
        HeadTagScript: initialData.HeadTagScript || "",
        HeadTagScriptLandingPage: initialData.HeadTagScriptLandingPage || "",
        HeadTagScriptSearchPage: initialData.HeadTagScriptSearchPage || "",
        GId: initialData.GId || "",
        AWId: initialData.AWId || "", // Commented out
        SendTo: initialData.SendTo || "", // Commented out
        AdsProviderId: initialData.AdsProviderId || "", // New field
        TrackingKey: initialData.TrackingKey || "", // New field
        Id: initialData.Id || "",
      });
    } else {
      setFormData({
        Name: "",
        Url: "",
        Domain: "",
        AssignUsers: [],
        AccountId: [],
        HeadTagScript: "",
        HeadTagScriptLandingPage: "",
        HeadTagScriptSearchPage: "",
        GId: "",
        AWId: "", // Commented out
        SendTo: "", // Commented out
        AdsProviderId: "", // New field
        TrackingKey: "", // New field
      });
    }
    setErrors({}); // Clear errors when data changes
  }, [initialData, isEdit, selectedDomain]);

  // Fetch ads providers when modal opens
  useEffect(() => {
    const fetchAdsProviders = async () => {
      if (isOpen) {
        try {
          setAdsProviders((prev) => ({ ...prev, isLoading: true }));
          const response = await apiClient.get("/AdsProvider/GetDropDown");
          
          if (response.data.success) {
            setAdsProviders({
              providersList: response.data.data,
              isLoading: false,
            });
          } else {
            console.error("Failed to fetch ads providers:", response.data.error);
            setAdsProviders((prev) => ({ ...prev, isLoading: false }));
          }
        } catch (error) {
          console.error("Error fetching ads providers:", error);
          setAdsProviders((prev) => ({ ...prev, isLoading: false }));
        }
      }
    };

    fetchAdsProviders();
  }, [isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleAdsProviderSelect = async (item) => {
    if (item?.Id) {
      setFormData((prev) => ({ 
        ...prev, 
        AdsProviderId: item.Id,
        TrackingKey: "" // Clear tracking key when provider changes
      }));

      // Fetch the ads provider details to get the description (tracking key)
      try {
        const response = await apiClient.get(`/AdsProvider/GetById?Id=${item.Id}`);
        if (response.data.success) {
          setFormData((prev) => ({ 
            ...prev, 
            TrackingKey: response.data.data?.Description || "" 
          }));
        }
      } catch (error) {
        console.error("Error fetching ads provider details:", error);
      }
    } else {
      setFormData((prev) => ({ 
        ...prev, 
        AdsProviderId: "",
        TrackingKey: "" 
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Required fields validation
    if (!formData.Name.trim()) newErrors.Name = "Subdomain name is required";
    if (!formData.Url.trim()) newErrors.Url = "URL is required";
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    onSubmit(formData);
  };

  const handleCancel = () => {
    setErrors({}); // Clear errors when closing
    onClose();
    setFormData({
      Name: "",
      Url: "",
      Domain: "",
      AssignUsers: [],
      AccountId: [],
      HeadTagScript: "",
      HeadTagScriptLandingPage: "",
      HeadTagScriptSearchPage: "",
      GId: "",
      AWId: "", // Commented out
      SendTo: "", // Commented out
      AdsProviderId: "", // New field
      TrackingKey: "", // New field
    });
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={(event, reason) => {
        if (reason === "backdropClick") return;
        handleCancel();
      }}
      fullWidth
      maxWidth="md"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {isEdit ? "Edit Subdomain" : "Add Subdomain"}
        </span>
        <IconButton
          aria-label="close"
          onClick={handleCancel}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form
          onSubmit={handleSubmit}
          style={{ display: "flex", flexDirection: "column", gap: 16 }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: 20,
            }}
          >
            <InputGroup
              label="Subdomain Name"
              type="text"
              name="Name"
              value={formData.Name}
              handleChange={handleChange}
              placeholder="Enter subdomain name"
              required
              error={errors.Name}
            />

            <InputGroup
              label="URL"
              type="text"
              name="Url"
              value={formData.Url}
              handleChange={handleChange}
              placeholder="Enter URL"
              required
              error={errors.Url}
            />
            <div className="z-50">
            <MultiSelectDropdown
              label="Assign Users"
              options={users}
              placeholder="Select users"
              value={formData.AssignUsers}
              onChange={(selectedIds) =>
                setFormData({ ...formData, AssignUsers: selectedIds })
              }
              displayKey="Name"
              idKey="Id"
              showSelectAll
            />
            </div>
            <div className="z-40">
            <MultiSelectDropdown
              label="Account Details"
              options={accountDetails}
              placeholder="Select accounts"
              value={formData.AccountId}
              onChange={(selectedIds) =>
                setFormData({ ...formData, AccountId: selectedIds })
              }
              displayKey="DescriptiveName"
              idKey="AccountId"
              showSelectAll
            />
            </div>

            <InputGroup
              label="AwId"
              name="AWId"
              type="text"
              placeholder="Enter AWId"
              value={formData.AWId}
              handleChange={handleChange}
            />

            <InputGroup
              label="Send To"
              name="SendTo"
              type="text"
              placeholder="Enter Send To"
              value={formData.SendTo}
              handleChange={handleChange}
            />
            {/* Replaced AWId with Ads Provider Dropdown */}
            <div className="w-full z-30">
              <SearchableDropdown
                label="Ads Provider"
                options={adsProviders.providersList}
                placeholder="Select Ads Provider"
                value={formData.AdsProviderId}
                onChange={handleAdsProviderSelect}
                displayKey="Provider"
                idKey="Id"
                isLoading={adsProviders.isLoading}
              />
            </div>

            <InputGroup
              label="Tracking Key"
              name="TrackingKey"
              type="text"
              placeholder="Tracking key will be auto-filled when you select an Ads Provider"
              value={formData.TrackingKey}
              handleChange={handleChange}
              showInformationIcon
              informationText="hello"
            />

            <InputGroup
              label="Gtag for Analytics"
              name="GId"
              type="text"
              placeholder="Enter Gtag for Analytics"
              value={formData.GId}
              handleChange={handleChange}
            />

            <TextAreaGroup
              label="Head Script"
              name="HeadTagScript"
              value={formData.HeadTagScript}
              handleChange={handleChange}
              rows={1}
              showInformationIcon
              informationText="Used In All Pages"
            />

            <TextAreaGroup
              label="Head Script Landing Page"
              name="HeadTagScriptLandingPage"
              value={formData.HeadTagScriptLandingPage}
              handleChange={handleChange}
              rows={3}
              showInformationIcon
              informationText="Used In Details Page"
            />

            <TextAreaGroup
              label="Head Script Search & Search Result Page"
              name="HeadTagScriptSearchPage"
              value={formData.HeadTagScriptSearchPage}
              handleChange={handleChange}
              rows={3}
              showInformationIcon
              informationText="Used In Search Page"
            />
          </div>
        </form>
      </DialogContent>

      <DialogActions>
        <Button
          type="submit"
          label={isEdit ? "Update SubDomain" : "Add SubDomain"}
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
        />
      </DialogActions>
    </Dialog>
  );
};

export default SubDomainModal;