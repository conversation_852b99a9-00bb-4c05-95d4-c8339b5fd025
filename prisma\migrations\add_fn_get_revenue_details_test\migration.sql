-- FUNCTION: public.fn_get_revenue_details_test(text, text, date, date)

-- DROP FUNCTION IF EXISTS public.fn_get_revenue_details_test(text, text, date, date);

CREATE OR REPLACE FUNCTION public.fn_get_revenue_details_test(
	p_article_id text,
	p_channel_id text,
	p_from_date date,
	p_to_date date)
    RETURNS TABLE(articleid uuid, channelid uuid, rv_revenue numeric, rv_impressions bigint, rv_rpm numeric, rv_clicks bigint, rv_ctr numeric, rv_rpc numeric, segmentdate date, country text) 
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
DECLARE
    sql TEXT;
BEGIN
    sql := '
        SELECT distinct
            art."articleid",
			art."channelid",
            ROUND(rv."EstimatedEarnings", 2) AS estimated_earnings,
            rv."Impressions"::BIGINT AS impressions,
            ROUND(rv."EstimatedEarnings" / NULLIF(rv."Impressions", 0::NUMERIC) * 1000::NUMERIC, 2) AS rpm,
            rv."Clicks"::BIGINT AS clicks,
            ROUND(rv."Clicks" / NULLIF(rv."Impressions", 0::NUMERIC) * 100::NUMERIC, 2) AS ctr,
            ROUND(rv."EstimatedEarnings" / NULLIF(rv."Clicks", 0::NUMERIC), 2) AS rpc,
            rv."Date" as "segmentdate",
			--rv."Country" AS country 
			isocm."Name" AS country 
        	FROM "Revenue" rv
        	JOIN vw_DistinctArticleChannelStyleMapping art 
            ON art.channelreportingdimensionid = rv."ChannalId"
           AND art."segmentdate" between $3 and $4
		   left join "Ads_ISO_CountryMaster" isocm on isocm."CountryCode" = rv."Country"
        	WHERE rv."Date" between $3 and $4 
		  	--AND rv."EstimatedEarnings" <> 0.0000 
		  	' ;

		  -- If p_article_id and  p_channel_id is not blank, apply the filter
		IF TRIM(p_article_id) <> '' AND TRIM(p_channel_id) <> '' THEN
	    -- Both article_id and channel_id are present
	    sql := sql || ' AND (art."articleid" = ANY(string_to_array($1, '','')::UUID[]) AND art."channelid" = ANY(string_to_array($2, '','')::UUID[])) ';
		ELSIF TRIM(p_article_id) <> '' THEN
	    -- Only article_id is present
	    sql := sql || ' AND art."articleid" = ANY(string_to_array($1, '','')::UUID[]) ';
		ELSIF TRIM(p_channel_id) <> '' THEN
	    -- Only channel_id is present
	    sql := sql || ' AND art."channelid" = ANY(string_to_array($2, '','')::UUID[]) ';
		END IF;

    -- Execute the query with safe parameter binding
    RETURN QUERY EXECUTE sql USING p_article_id, p_channel_id, p_from_date, p_to_date;
END;
$BODY$;
