import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

export async function POST(req: NextRequest) {
    try {
        // const user = await verifyToken(req);

        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const hasBody = req.headers.get('content-length') !== '0';
        const body = hasBody ? await req.json() : null;

        const selectFields = {
            Id: true,
            LmStyleId: true,
            DmStyleId: true,
            ChannalId: true,
            PubId: true,
            AdsAccountId: true,
            AdsClientId: true,
            HeadTagJSON: true,
            CreatedAt: true,
            UpdatedAt: true,
            CountAdsClick: true,
            CampaignClientId: true,
            CampaignClientSecret: true,
            CampaignRedirectUrl: true,
            CampaignScope: true,
            RevenueClientSecret: true,
            RevenueClientId: true,
            RevenueRedirectUrl: true,
            RevenueScope: true,
            DeveloperToken: true,
            LoginCustomerId: true
        };

        const existingSetting = await prisma.adminUserSetting.findFirst({
            select: selectFields
        });

        if (!existingSetting) {
            const newSetting = await prisma.adminUserSetting.create({
                data: {
                    LmStyleId: body?.LmStyleId ?? null,
                    DmStyleId: body?.DmStyleId ?? null,
                    ChannalId: body?.ChannalId ?? null,
                    PubId: body?.PubId ?? null,
                    AdsAccountId: body?.AdsAccountId ?? null,
                    AdsClientId: body?.AdsClientId ?? null,
                    HeadTagJSON: body?.HeadTagJSON ?? '{}',
                    CountAdsClick: body?.CountAdsClick || null,
                    CampaignClientId: body?.CampaignClientId ?? null,
                    CampaignClientSecret: body?.CampaignClientSecret ?? null,
                    CampaignRedirectUrl: body?.CampaignRedirectUrl ?? null,
                    CampaignScope: body?.CampaignScope ?? null,
                    RevenueClientSecret: body?.RevenueClientSecret ?? null,
                    RevenueClientId: body?.RevenueClientId ?? null,
                    RevenueRedirectUrl: body?.RevenueRedirectUrl ?? null,
                    RevenueScope: body?.RevenueScope ?? null,
                    DeveloperToken: body?.DeveloperToken ?? null,
                    LoginCustomerId: body?.LoginCustomerId ?? null,
                },
                select: selectFields
            });

            return NextResponse.json({
                success: true,
                message: 'Admin setting created successfully',
                data: newSetting
            });
        }

        if (body) {
            const updateData: any = {};

            // Helper function to safely handle UUID fields
            const setUUIDField = (fieldName: string, value: any) => {
                if (value !== undefined) {
                    updateData[fieldName] = (value && value.trim() !== '') ? value : null;
                }
            };

            if ('LmStyleId' in body) setUUIDField('LmStyleId', body.LmStyleId);
            if ('DmStyleId' in body) setUUIDField('DmStyleId', body.DmStyleId);
            if ('ChannalId' in body) setUUIDField('ChannalId', body.ChannalId);
            if ('PubId' in body) setUUIDField('PubId', body.PubId);
            if ('AdsAccountId' in body) setUUIDField('AdsAccountId', body.AdsAccountId);
            if ('AdsClientId' in body) setUUIDField('AdsClientId', body.AdsClientId);
            if ('CountAdsClick' in body) setUUIDField('CountAdsClick', body.CountAdsClick);
            if ('CampaignClientId' in body) setUUIDField('CampaignClientId', body.CampaignClientId);
            if ('RevenueClientId' in body) setUUIDField('RevenueClientId', body.RevenueClientId);
            if ('LoginCustomerId' in body) setUUIDField('LoginCustomerId', body.LoginCustomerId);
            if ('HeadTagJSON' in body) updateData.HeadTagJSON = body.HeadTagJSON;
            if ('CampaignClientSecret' in body) updateData.CampaignClientSecret = body.CampaignClientSecret;
            if ('CampaignRedirectUrl' in body) updateData.CampaignRedirectUrl = body.CampaignRedirectUrl;
            if ('CampaignScope' in body) updateData.CampaignScope = body.CampaignScope;
            if ('RevenueClientSecret' in body) updateData.RevenueClientSecret = body.RevenueClientSecret;
            if ('RevenueRedirectUrl' in body) updateData.RevenueRedirectUrl = body.RevenueRedirectUrl;
            if ('RevenueScope' in body) updateData.RevenueScope = body.RevenueScope;
            if ('DeveloperToken' in body) updateData.DeveloperToken = body.DeveloperToken;

            const updatedSetting = await prisma.adminUserSetting.update({
                where: { Id: existingSetting.Id },
                data: updateData,
                select: selectFields
            });

            return NextResponse.json({
                success: true,
                message: 'Admin setting updated successfully',
                data: updatedSetting
            });
        }

        return NextResponse.json({
            success: true,
            data: existingSetting
        });

    } catch (error) {
        console.error('Error handling admin user setting:', error);
        return NextResponse.json({
            error: 'Failed to process admin user setting',
            details: process.env.NODE_ENV === 'development' ? error : undefined
        }, { status: 500 });
    }
}