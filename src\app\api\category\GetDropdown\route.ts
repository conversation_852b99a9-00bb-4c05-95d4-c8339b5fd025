import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);

        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const domainId = searchParams.get("domainId");

        // Build conditional filter
        const whereCondition: any = {
            IsDeleted: false,
        };

        // If domainId is not an empty string, include it in filter
        if (domainId && domainId !== '') {
            whereCondition.Domain = domainId;
        }

        const categories = await prisma.category.findMany({
            where: whereCondition,
            select: {
                Id: true,
                Name: true,
            },
            orderBy: {
                Name: 'asc',
            },
        });

        return NextResponse.json({
            success: true,
            data: categories,
        });

    } catch (error) {
        console.error('Error in API handler:', error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    } 
}
