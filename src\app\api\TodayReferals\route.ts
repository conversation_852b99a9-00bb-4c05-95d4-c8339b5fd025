import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';
import { prisma } from '../../../lib/prisma';

export async function GET(req: NextRequest) {
    const client = new Client({
        connectionString: process.env.DATABASE_URL_API,
        ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    });

    try {
        await client.connect();
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;

        const { searchParams } = new URL(req.url);
        const orderBy = searchParams.get('orderBy') || 'Impression';
        const orderDir = (searchParams.get('orderDir') || 'desc').toLowerCase();
        const domainId = searchParams.get('domainId');
        const subDomainId = searchParams.get('subDomainId');
        const breakpoint = searchParams.get('breakpoint');
        
        // Handling multiple breakpoints like Url and Domain
        const breakpoints = breakpoint ? breakpoint.split(',') : [];

        let showUrlNames: string[] = [];

        if (role === 'Partner' || role === 'Account') {
            const userWiseSubdomains = await prisma.subDomainUserMappings.findMany({
                where: {
                    UserId: userId,
                    ...(subDomainId && { SubDomainId: subDomainId }),
                },
                select: { SubDomainId: true },
            });

            const subDomainIds = userWiseSubdomains
                .map((item) => item.SubDomainId)
                .filter((id): id is string => id !== null);

            if (!subDomainIds.length) {
                return NextResponse.json({ message: 'No subdomains found for the user' }, { status: 200 });
            }

            const subDomains = await prisma.subDomain.findMany({
                where: {
                    Id: { in: subDomainIds },
                    IsDeleted: false,
                    ...(domainId && { Domain: domainId }),
                },
                select: { Id: true, Url: true, Domain: true },
            });

            const domainIds = subDomains
                .map((item) => item.Domain)
                .filter((id): id is string => id !== null);

            if (!domainIds.length) {
                return NextResponse.json({ message: 'No domains found for the subdomains' }, { status: 200 });
            }

            const domains = await prisma.domain.findMany({
                where: { Id: { in: domainIds }, IsDeleted: false },
                select: { Id: true, ShowUrlName: true },
            });

            showUrlNames = subDomains
                .map((subDomain) => {
                    const domain = domains.find((d) => d.Id === subDomain.Domain);
                    return domain ? `${subDomain.Url}.${domain.ShowUrlName}` : null;
                })
                .filter((name): name is string => name !== null);

            if (!showUrlNames.length) {
                return NextResponse.json({ message: 'No valid domain names found' }, { status: 200 });
            }
        } else {
            if (subDomainId) {
                const subDomain = await prisma.subDomain.findFirst({
                    where: { Id: subDomainId, IsDeleted: false, ...(domainId && { Domain: domainId }) },
                    select: { Id: true, Url: true, Domain: true },
                });

                if (!subDomain) {
                    return NextResponse.json({ message: 'No valid subdomain found' }, { status: 200 });
                }

                const domain = await prisma.domain.findFirst({
                    where: { Id: subDomain.Domain as string, IsDeleted: false },
                    select: { Id: true, ShowUrlName: true },
                });

                if (!domain) {
                    return NextResponse.json({ message: 'No valid domain found for the subdomain' }, { status: 200 });
                }

                showUrlNames = [`${subDomain.Url}.${domain.ShowUrlName}`];
            } else {
                const domains = await prisma.domain.findMany({
                    where: { IsDeleted: false, ...(domainId && { Id: domainId }) },
                    select: { Id: true, ShowUrlName: true },
                });

                showUrlNames = domains
                    .map((item) => item.ShowUrlName)
                    .filter((name): name is string => name !== null);

                if (!showUrlNames.length) {
                    return NextResponse.json({ message: 'No valid domain names found' }, { status: 200 });
                }
            }
        }

        await client.query('BEGIN');
        await client.query('SET TRANSACTION ISOLATION LEVEL READ COMMITTED');

        let query: string;
        let queryParams: any[] = [];

        if (breakpoints.length > 0) {
            const selectColumns = breakpoints.map((bp) => `"${bp}"`).join(", ");
            const groupByColumns = breakpoints.map((bp) => `"${bp}"`).join(", ");
            const whereClauses = showUrlNames.map((_, index) => `"Domain" LIKE '%' || $${index + 1}`).join(' OR ');

            query = `
                SELECT ${selectColumns}, "ReferalDomain", SUM("Impression") AS "Impression"
                FROM vw_GetReferalForToday
                WHERE ${whereClauses}
                GROUP BY ${groupByColumns}, "ReferalDomain"
                ORDER BY "${orderBy}" ${orderDir}
                LIMIT 100
            `;
            queryParams = [...showUrlNames];
        } else {
            query = `
                SELECT "ReferalDomain", SUM("Impression") as "Impression" FROM vw_GetReferalForToday
                WHERE ${showUrlNames.map((_, index) => `"Domain" LIKE '%' || $${index + 1}`).join(' OR ')}
                GROUP BY "ReferalDomain"
                ORDER BY "${orderBy}" ${orderDir}
                LIMIT 100
            `;
            queryParams = showUrlNames;
        }

        const result = await client.query(query, queryParams);
        await client.query('COMMIT');

        return NextResponse.json({ data: result.rows }, { status: 200 });
    } catch (error) {
        console.error('❌ Error fetching referral data:', error);
        try {
            await client.query('ROLLBACK');
        } catch (rollbackError) {
            console.error('❌ Failed to rollback transaction:', rollbackError);
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return NextResponse.json({ error: 'Internal Server Error', details: errorMessage }, { status: 500 });
    }
}
