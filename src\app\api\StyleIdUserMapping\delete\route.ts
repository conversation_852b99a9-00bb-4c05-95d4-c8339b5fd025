import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function DELETE(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        // const url = new URL(req.url);
        // const userId = url.searchParams.get('Id');
        // const Id = userId;

        const {Id} = await req.json();
      
        if (!Id) {
            return NextResponse.json(
                { error: "Id is required" },
                { status: 400 }
            );
        }

        // Check if the user exists
        const existingUser = await prisma.styleIdUserMappings.findUnique({
            where: {
                Id: Id
            }
        });

        if (!existingUser) {
            return NextResponse.json(
                { error: "Mapping not found" },
                { status: 404 }
            );
        }

        // Delete the user
        // await prisma.AdminUser.delete({
        //     where: {
        //         Id: Id
        //     }
        // });
        const deletedMapping = await prisma.styleIdUserMappings.delete({
            where: {
                Id: Id
            },
        });

        return NextResponse.json(
            {
                success: true,
                message: " deleted successfully"
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } 
}