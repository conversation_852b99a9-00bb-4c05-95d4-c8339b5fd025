"use client";

import React, { useState } from 'react';
import { FaPlus, FaUsers } from 'react-icons/fa';
import {
  PageContainer,
  ContentCard,
  SmartDataTable,
  FormModal,
  ConfirmationModal,
  InputGroup,
  SearchableDropdown,
  Checkbox,
  Button,
} from '@/components/ui';
import {
  useForm,
  useApiPost,
  useApiDelete,
  commonValidationSchemas,
} from '@/hooks';
import { API_ENDPOINTS, USER_TYPES } from '@/constants';
import type { User } from '@/types/common';

const ModularUserManagement: React.FC = () => {
  // State management
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // API hooks
  const { post: createUser, loading: createLoading } = useApiPost({
    successMessage: 'User created successfully',
    showSuccessAlert: true,
  });

  const { post: updateUser, loading: updateLoading } = useApiPost({
    successMessage: 'User updated successfully',
    showSuccessAlert: true,
  });

  const { delete: deleteUser, loading: deleteLoading } = useApiDelete({
    successMessage: 'User deleted successfully',
    showSuccessAlert: true,
  });

  // Form management
  const initialFormValues = {
    Name: '',
    Email: '',
    Number: '',
    User_Type: '',
    Password: '',
    DisplayName: '',
    Status: false,
    AboutMe: '',
  };

  const {
    values,
    errors,
    handleChange,
    handleBlur,
    validateForm,
    reset,
    setFormData,
  } = useForm({
    initialValues: initialFormValues,
    validationRules: commonValidationSchemas.user,
    validateOnBlur: true,
  });

  // Table columns configuration
  const columns = [
    { id: 'Name', label: 'Name' },
    { id: 'Email', label: 'Email' },
    { id: 'Number', label: 'Phone' },
    { id: 'User_Type', label: 'User Type' },
    { id: 'Status', label: 'Status', format: (value: boolean) => value ? 'Active' : 'Inactive' },
  ];

  // User type options
  const userTypeOptions = Object.values(USER_TYPES).map(type => ({
    Id: type,
    Name: type,
  }));

  // Event handlers
  const handleAddUser = () => {
    reset();
    setEditingUser(null);
    setIsModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setFormData(user);
    setIsModalOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (editingUser) {
        await updateUser(API_ENDPOINTS.USERS.UPDATE, {
          ...values,
          Id: editingUser.Id,
        });
      } else {
        await createUser(API_ENDPOINTS.USERS.CREATE, values);
      }
      
      setIsModalOpen(false);
      reset();
      setEditingUser(null);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleConfirmDelete = async () => {
    if (!userToDelete) return;

    try {
      await deleteUser(`${API_ENDPOINTS.USERS.DELETE}/${userToDelete.Id}`);
      setIsDeleteModalOpen(false);
      setUserToDelete(null);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    reset();
    setEditingUser(null);
  };

  return (
    <PageContainer
      title="User Management"
      subtitle="Manage system users and their permissions"
    >
      <ContentCard>
        <SmartDataTable
          endpoint={API_ENDPOINTS.USERS.GET_LIST}
          columns={columns}
          title="Users"
          actionButtons={[
            {
              label: 'Add User',
              icon: <FaPlus size={14} />,
              onClick: handleAddUser,
              variant: 'primary',
            },
          ]}
          onEdit={handleEditUser}
          onDelete={handleDeleteUser}
          searchPlaceholder="Search users..."
        />
      </ContentCard>

      {/* Add/Edit User Modal */}
      <FormModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        title={editingUser ? 'Edit User' : 'Add New User'}
        onSubmit={handleFormSubmit}
        isLoading={createLoading || updateLoading}
        submitLabel={editingUser ? 'Update User' : 'Add User'}
        maxWidth="md"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputGroup
            label="Name"
            type="text"
            name="Name"
            value={values.Name}
            handleChange={handleChange}
            onBlur={handleBlur}
            placeholder="Enter full name"
            required
            error={errors.Name}
          />

          <InputGroup
            label="Email"
            type="email"
            name="Email"
            value={values.Email}
            handleChange={handleChange}
            onBlur={handleBlur}
            placeholder="Enter email address"
            required
            error={errors.Email}
          />

          <InputGroup
            label="Phone Number"
            type="tel"
            name="Number"
            value={values.Number}
            handleChange={handleChange}
            onBlur={handleBlur}
            placeholder="Enter phone number"
            required
            error={errors.Number}
          />

          <SearchableDropdown
            label="User Type"
            options={userTypeOptions}
            value={values.User_Type}
            onChange={(option) => handleChange({
              target: { name: 'User_Type', value: option?.Id || '' }
            } as any)}
            placeholder="Select user type"
            displayKey="Name"
            idKey="Id"
          />

          {!editingUser && (
            <InputGroup
              label="Password"
              type="password"
              name="Password"
              value={values.Password}
              handleChange={handleChange}
              onBlur={handleBlur}
              placeholder="Enter password"
              required
              error={errors.Password}
            />
          )}

          <InputGroup
            label="Display Name"
            type="text"
            name="DisplayName"
            value={values.DisplayName}
            handleChange={handleChange}
            onBlur={handleBlur}
            placeholder="Enter display name"
          />
        </div>

        <div className="mt-4">
          <Checkbox
            label="Active Status"
            name="Status"
            checked={values.Status}
            onChange={handleChange}
            withIcon="check"
            withBg
            radius="md"
          />
        </div>
      </FormModal>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete User"
        message={`Are you sure you want to delete "${userToDelete?.Name}"? This action cannot be undone.`}
        confirmLabel="Delete"
        confirmVariant="danger"
        isLoading={deleteLoading}
        icon={<FaUsers />}
      />
    </PageContainer>
  );
};

export default ModularUserManagement;
