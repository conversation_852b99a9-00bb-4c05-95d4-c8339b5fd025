"use client";

import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui-elements/button';

interface ActionButton {
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'green' | 'danger' | 'dark' | 'outlinePrimary' | 'outlineGreen' | 'outlineDark';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

interface ActionBarProps {
  actions?: ActionButton[];
  children?: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right' | 'between';
  wrap?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const ActionBar: React.FC<ActionBarProps> = ({
  actions = [],
  children,
  className = '',
  align = 'left',
  wrap = true,
  size = 'md',
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between',
  };

  const sizeClasses = {
    sm: 'gap-2',
    md: 'gap-3',
    lg: 'gap-4',
  };

  const buttonSizeMap = {
    sm: 'small' as const,
    md: 'default' as const,
    lg: 'default' as const,
  };

  return (
    <div className={cn(
      'flex items-center',
      alignClasses[align],
      sizeClasses[size],
      wrap ? 'flex-wrap' : 'flex-nowrap',
      className
    )}>
      {actions.map((action, index) => (
        <Button
          key={index}
          label={action.loading ? 'Loading...' : action.label}
          icon={action.icon}
          variant={action.variant || 'primary'}
          shape="rounded"
          size={buttonSizeMap[size]}
          onClick={action.onClick}
          disabled={action.disabled || action.loading}
          className={action.className}
        />
      ))}
      
      {children}
    </div>
  );
};

export default ActionBar;
