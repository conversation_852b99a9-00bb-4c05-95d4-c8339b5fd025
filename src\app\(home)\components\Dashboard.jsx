"use client"
import LanguageIcon from "@mui/icons-material/Language";
import PeopleIcon from "@mui/icons-material/People";
import ArticleIcon from "@mui/icons-material/Article";
import ShowChartIcon from "@mui/icons-material/ShowChart";
import AddIcon from "@mui/icons-material/Add";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import CreateIcon from "@mui/icons-material/Create";
import PieChartIcon from "@mui/icons-material/PieChart";
import SubdirectoryArrowRightIcon from "@mui/icons-material/SubdirectoryArrowRight";
import StyleIcon from "@mui/icons-material/Style";
import TodayIcon from "@mui/icons-material/Today";
import apiClient from "@/utils/axiosInstance";
import { useEffect, useState, useCallback } from "react";
import Swal from "sweetalert2";
import { useRouter } from "next/navigation";
import { decodeJWT } from "@/utils/functions";

export default function Dashboard() {
  const [data, setData] = useState({
    domainCount: 0,
    articleCount: 0,
    userCount: 0,
    roi: 0,
    roi_gst: 0
  });
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [token, setToken] = useState(null);
  const router = useRouter();

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken");
    setToken(accessToken);
    if (accessToken) {
      setUserData(decodeJWT(accessToken));
    }
  }, []);

  const fetchDashBoard = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.post("/DashBoard/GetInfo");
      console.log(response.data);

      if (response.data.data) {
        setData(response.data.data);
      }
    } catch (error) {
      console.error(error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to fetch dashboard data'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchDashBoard();
    }
  }, [token, fetchDashBoard]);

  // Navigation functions
  const navigateToAddDomain = () => {
    router.push('/domains/add');
  };

  const navigateToAddSubdomain = () => {
    router.push('/subdomains');
  };

  const navigateToAddUser = () => {
    router.push('/allUser');
  };

  const navigateToCreateArticle = () => {
    router.push('/articles');
  };

  const navigateToAssignStyle = () => {
    router.push('/style-ids');
  };

  const navigateToViewReports = () => {
    router.push('/revenuecampaingsmanagement');
  };

  // Format ROI values to display as percentages with 2 decimal places
  const formatRoi = (value) => {
    return `${value?.toFixed(2) || '0.00'}%`;
  };

  // Check if user is Admin or Super Admin
  const isAdminUser = userData && (userData.User_Type === "Super Admin" || userData.User_Type === "Admin");

  return (
    <div className="w-full min-h-screen relative overflow-x-hidden font-poppins bg-gray-50">
      <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="p-6">
          <h1 className="text-white text-2xl font-bold">Dashboard</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute w-[95%] bg-white dark:bg-gray-dark shadow-lg rounded-[10px] left-1/2 -translate-x-1/2 top-[90px] border border-stroke dark:border-dark-3 p-4 sm:p-6">
        <div
          className="flex flex-col p-6"
          style={{ minHeight: "calc(100vh - 230px)" }}
        >
          {/* Welcome */}
          <div className="text-center mb-10">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 bg-clip-text text-transparent mb-2">
              Welcome to Admin Panel
            </h1>
            <p className="text-xl text-gray-600 mb-2 max-w-2xl mx-auto">
              Manage sites, domains, styles, and more.
            </p>
            <p className="text-gray-500">
              Use the sidebar to get started with your administrative tasks.
            </p>
          </div>

          {loading ? (
            <div className="flex flex-col justify-center items-center h-96">
              <div className="relative mb-6">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-100 border-t-[#5750F1]"></div>
              </div>
              <div className="text-center">
                <p className="text-gray-600 font-medium mb-1">Loading Dashboard</p>
                <p className="text-sm text-gray-400">Please wait while we gather your data</p>
              </div>
            </div>
          ) : (
            <>
              {/* Stats Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center">
                    <div className="rounded-full bg-blue-200 p-3 mr-4">
                      <LanguageIcon className="text-blue-700 text-xl" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800">{data.domainCount}</h3>
                      <p className="text-gray-600">Domains</p>
                    </div>
                  </div>
                </div>

                {isAdminUser && (
                  <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200 shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-center">
                      <div className="rounded-full bg-green-200 p-3 mr-4">
                        <PeopleIcon className="text-green-700 text-xl" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-800">{data.userCount}</h3>
                        <p className="text-gray-600">Users</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center">
                    <div className="rounded-full bg-purple-200 p-3 mr-4">
                      <ArticleIcon className="text-purple-700 text-xl" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800">{data.articleCount}</h3>
                      <p className="text-gray-600">Articles</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-amber-50 to-amber-100 p-6 rounded-xl border border-amber-200 shadow-sm hover:shadow-md transition-shadow relative">
                  {/* Today's ROI Badge */}
                  <div className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center">
                    <TodayIcon className="text-xs mr-1" />
                    <span>Today</span>
                  </div>
                  
                  <div className="flex items-center">
                    <div className="rounded-full bg-amber-200 p-3 mr-4">
                      <ShowChartIcon className="text-amber-700 text-xl" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800">
                        {formatRoi(data.roi)} / {formatRoi(data.roi_gst)}
                      </h3>
                      <p className="text-gray-600">ROI / ROI+GST</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions - Show different buttons based on user role */}
              <div className="bg-white border border-gray-200 rounded-xl p-6 mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">
                    Quick Actions
                  </h2>
                  <span className="text-sm text-gray-500">
                    {isAdminUser ? 'Admin Access' : 'User Access'}
                  </span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                  {/* Only show Add Domain for Super Admin */}
                  {isAdminUser && (
                    <button
                      onClick={navigateToAddDomain}
                      className="flex flex-col items-center justify-center p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-all hover:scale-105 border border-blue-200"
                    >
                      <div className="bg-blue-200 p-3 rounded-full mb-2">
                        <AddIcon className="text-blue-700" />
                      </div>
                      <span className="text-sm font-medium text-gray-700">Add Domain</span>
                    </button>
                  )}

                  {/* Only show Add Subdomain for Super Admin */}
                  {isAdminUser && (
                    <button
                      onClick={navigateToAddSubdomain}
                      className="flex flex-col items-center justify-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-xl transition-all hover:scale-105 border border-indigo-200"
                    >
                      <div className="bg-indigo-200 p-3 rounded-full mb-2">
                        <SubdirectoryArrowRightIcon className="text-indigo-700" />
                      </div>
                      <span className="text-sm font-medium text-gray-700">Add Subdomain</span>
                    </button>
                  )}

                  {/* Only show Add User for Super Admin */}
                  {isAdminUser && (
                    <button
                      onClick={navigateToAddUser}
                      className="flex flex-col items-center justify-center p-4 bg-green-50 hover:bg-green-100 rounded-xl transition-all hover:scale-105 border border-green-200"
                    >
                      <div className="bg-green-200 p-3 rounded-full mb-2">
                        <PersonAddIcon className="text-green-700" />
                      </div>
                      <span className="text-sm font-medium text-gray-700">Add User</span>
                    </button>
                  )}

                  {/* Show Create Article for all Admin users (Admin and Super Admin) */}
                  <button
                    onClick={navigateToCreateArticle}
                    className="flex flex-col items-center justify-center p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition-all hover:scale-105 border border-purple-200"
                  >
                    <div className="bg-purple-200 p-3 rounded-full mb-2">
                      <CreateIcon className="text-purple-700" />
                    </div>
                    <span className="text-sm font-medium text-gray-700">Create Article</span>
                  </button>

                  {/* Only show Assign Style for Super Admin */}
                  {isAdminUser && (
                    <button
                      onClick={navigateToAssignStyle}
                      className="flex flex-col items-center justify-center p-4 bg-pink-50 hover:bg-pink-100 rounded-xl transition-all hover:scale-105 border border-pink-200"
                    >
                      <div className="bg-pink-200 p-3 rounded-full mb-2">
                        <StyleIcon className="text-pink-700" />
                      </div>
                      <span className="text-sm font-medium text-gray-700">Assign Style</span>
                    </button>
                  )}

                  <button
                    onClick={navigateToViewReports}
                    className="flex flex-col items-center justify-center p-4 bg-amber-50 hover:bg-amber-100 rounded-xl transition-all hover:scale-105 border border-amber-200"
                  >
                    <div className="bg-amber-200 p-3 rounded-full mb-2">
                      <PieChartIcon className="text-amber-700" />
                    </div>
                    <span className="text-sm font-medium text-gray-700">View Reports</span>
                  </button>
                </div>
              </div>

              {/* User Welcome Message */}
              {userData && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200 mb-8">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Welcome back, {userData.User_Name}!
                  </h3>
                  <p className="text-gray-600">
                    You have {userData.User_Type} access level. {isAdminUser
                      ? 'You can manage all system features.'
                      : 'You have access to basic features.'}
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}