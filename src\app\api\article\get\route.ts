import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
  try {
    // type AuthenticatedUser = {
    //   Id: string;
    //   User_Type: string;
    // };

    const role = req.headers.get('x-user-role') as string;
    const userId = req.headers.get('x-user-id');
    const email = req.headers.get('x-user-email');
    const name = req.headers.get('x-user-name');

    // const user = (await verifyToken(req)) as AuthenticatedUser;
    // if (!userId) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const { searchParams } = new URL(req.url);
    const start = parseInt(searchParams.get('page') || '1');
    const length = parseInt(searchParams.get('length') || '10');
    const searchRaw = searchParams.get("q");
    const search = searchRaw ? searchRaw.trim() : null;
    const orderByParam = searchParams.get('orderBy') || 'Id';
    const orderDirParam = (searchParams.get('orderDir') || 'asc').toLowerCase();
    const categoryIdFilter = searchParams.get('CategoryId') || '';
    const userIdFilter = searchParams.get('userId');
    const domainIdFilter = searchParams.get('domainId');
    const subDomainIdFilter = searchParams.get('subDomainId');
    const showsAdsFilter = searchParams.get('showsAds');
    const publishedFilter = searchParams.get('published');
    const draw = parseInt(searchParams.get('draw') || '1');

    const validOrderFields = ['Id', 'Title', 'CustomChannal', 'CategoryName', 'Domain', 'SubDomainId', 'CampaignCount', 'UserName', 'ShowsAds', 'Published'];

    const orderFieldMap: { [key: string]: string } = {
      'id': 'Id',
      'title': 'Title',
      'customchannal': 'CustomChannal',
      'categoryname': 'CategoryName',
      'domain': 'Domain',
      'subdomainid': 'SubDomainId',
      'campaigncount': 'CampaignCount',
      'username': 'UserName',
      'showsads': 'ShowsAds',
      'published': 'Published'
    };

    const orderBy = orderFieldMap[orderByParam.toLowerCase()] || 'CreatedAt';
    const orderDir = orderDirParam === 'desc' ? 'desc' : 'asc';

    let skip: number | undefined;
    let limit: number | undefined;

    if (length !== -1) {
      skip = (start - 1) * length;
      limit = length;
    }

    const where: any = { IsDeleted: false, AND: [] };

    if (role.toLowerCase() === 'admin' || role.toLowerCase() === 'super admin') {
      if (userIdFilter) {
        where.User_Id_Settings = userIdFilter;
      }
    } else {
      const userSubDomainMappings = await prisma.subDomainUserMappings.findMany({
        where: {
          UserId: userId
        },
        select: {
          SubDomainId: true
        }
      });

      const userSubDomainIds = userSubDomainMappings.map(mapping => mapping.SubDomainId);
      // console.log(userSubDomainIds, "userSubDomainIds");

      if (userSubDomainIds.length > 0) {
        where.AND = [
          { SubDomain: { in: userSubDomainIds } },
          { User_Id_Settings: userId }
        ];
      } else {
        where.AND = [
          { SubDomain: null },
          { User_Id_Settings: userId }
        ];
      }
    }

    if (categoryIdFilter) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(categoryIdFilter)) {
        return NextResponse.json({ error: 'Invalid category ID format' }, { status: 400 });
      }
      where.Category = categoryIdFilter;
    }

    // Domain/Subdomain validation
    if (subDomainIdFilter && !domainIdFilter) {
      return NextResponse.json({ error: 'domainId is required when subDomainId is provided' }, { status: 400 });
    }

    if (domainIdFilter) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

      if (uuidRegex.test(domainIdFilter)) {
        where.AND.push({
          OR: [
            { Domain: domainIdFilter },
            {
              SubDomain_ArticleDetails_SubDomainToSubDomain: {
                Domain: domainIdFilter
              }
            }
          ]
        });
      } else {
        const domainPattern = domainIdFilter.toLowerCase();
        where.AND.push({
          OR: [
            {
              Domain_ArticleDetails_DomainToDomain: {
                ShowUrlName: {
                  endsWith: domainPattern,
                  mode: 'insensitive'
                }
              }
            },
            {
              SubDomain_ArticleDetails_SubDomainToSubDomain: {
                Domain_SubDomain_DomainToDomain: {
                  ShowUrlName: {
                    endsWith: domainPattern,
                    mode: 'insensitive'
                  }
                }
              }
            }
          ]
        });
      }
    }

    // SubDomain filter
    if (subDomainIdFilter) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(subDomainIdFilter)) {
        return NextResponse.json({ error: 'Invalid subdomain ID format' }, { status: 400 });
      }
      where.AND.push({ SubDomain: subDomainIdFilter });
    }

    // ShowsAds filter (0 = true, 1 = false, -1 = all)
    if (showsAdsFilter && showsAdsFilter !== '-1') {
      if (showsAdsFilter === '0') {
        where.ShowsAds = true;
      } else if (showsAdsFilter === '1') {
        where.ShowsAds = false;
      } else {
        return NextResponse.json({ error: 'Invalid showsAds value. Use 0 (true), 1 (false), or -1 (all)' }, { status: 400 });
      }
    }

    // Published filter (0 = published, 1 = draft, -1 = all)
    if (publishedFilter && publishedFilter !== '-1') {
      if (publishedFilter === '0') {
        where.Published = true;
      } else if (publishedFilter === '1') {
        where.Published = false;
      } else {
        return NextResponse.json({ error: 'Invalid published value. Use 0 (published), 1 (draft), or -1 (all)' }, { status: 400 });
      }
    }

    const recordsTotal = await prisma.articleDetails.count({ where });

    const allArticles = await prisma.articleDetails.findMany({
      where,
      select: {
        Id: true,
        Title: true,
        Category: true,
        CustomChannal: true,
        Domain: true,
        SubDomain: true,
        ShowsAds: true,
        Published: true,
        User_Id_Settings: true,
        Url: true,
        _count: {
          select: {
            ArticleCampaignMappings: true,
          },
        },
        Domain_ArticleDetails_DomainToDomain: {
          select: {
            ShowUrlName: true,
          },
        },
        SubDomain_ArticleDetails_SubDomainToSubDomain: {
          select: {
            Url: true,
            Domain_SubDomain_DomainToDomain: {
              select: {
                ShowUrlName: true
              }
            }
          }
        }
      },
    });

    const categoryIds = [...new Set(allArticles.map((a) => a.Category).filter(Boolean))];
    const channelIds = [...new Set(allArticles.map((a) => a.CustomChannal).filter(Boolean))];
    const domainIds = [...new Set(allArticles.map((a) => a.Domain).filter(Boolean))];
    const subDomainIds = [...new Set(allArticles.map((a) => a.SubDomain).filter(Boolean))];
    const adminUserIds = [...new Set(allArticles.map((a) => a.User_Id_Settings).filter(Boolean))];

    const [categories, channels, domains, subDomains, adminUsers] = await Promise.all([
      categoryIds.length ? prisma.category.findMany({ where: { Id: { in: categoryIds as string[] } }, select: { Id: true, Name: true } }) : [],
      channelIds.length ? prisma.channals.findMany({ where: { Id: { in: channelIds as string[] } }, select: { Id: true, Name: true, DisplayName: true } }) : [],
      domainIds.length ? prisma.domain.findMany({ where: { Id: { in: domainIds as string[] } }, select: { Id: true, ShowUrlName: true } }) : [],
      subDomainIds.length ? prisma.subDomain.findMany({
        where: { Id: { in: subDomainIds as string[] } },
        select: {
          Id: true,
          Name: true,
          Url: true,
          Domain_SubDomain_DomainToDomain: {
            select: {
              ShowUrlName: true
            }
          }
        }
      }) : [],
      adminUserIds.length ? prisma.adminUser.findMany({ where: { Id: { in: adminUserIds as string[] } }, select: { Id: true, Name: true } }) : [],
    ]);

    const categoryMap = new Map(categories.map((cat) => [cat.Id, cat.Name]));
    const channelMap = new Map(channels.map((ch) => [ch.Id, { Name: ch.Name, DisplayName: ch.DisplayName }]));
    const adminUserMap = new Map(adminUsers.map((u) => [u.Id, u.Name]));

    const extractCustomChannalId = (channelName: string | null | undefined): string | null => {
      if (!channelName) return null;
      const parts = channelName.split('/');
      return parts.length > 0 ? parts[parts.length - 1] : null;
    };

    // Helper function to get full domain display
    const getDomainDisplay = (article: any): string | null => {
      if (article.SubDomain_ArticleDetails_SubDomainToSubDomain) {
        const sub = article.SubDomain_ArticleDetails_SubDomainToSubDomain;
        return `${sub.Url}.${sub.Domain_SubDomain_DomainToDomain?.ShowUrlName}`;
      }
      return article.Domain_ArticleDetails_DomainToDomain?.ShowUrlName || null;
    };

    // Helper function to check domain matches
    const domainMatchesSearch = (domain: string | null, searchTerm: string): boolean => {
      if (!domain) return false;
      return domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
        domain.toLowerCase().endsWith(`.${searchTerm.toLowerCase()}`);
    };

    let transformedArticles = allArticles.map((article) => {
      const channelData = article.CustomChannal ? channelMap.get(article.CustomChannal) : null;
      const channelName = channelData?.DisplayName || channelData?.Name || null;
      const domainDisplay = getDomainDisplay(article);

      return {
        Id: article.Id,
        Title: article.Title,
        Url: article.Url,
        CustomChannal: `${channelName} [${extractCustomChannalId(channelData?.Name)}]`,
        CategoryName: article.Category ? categoryMap.get(article.Category) || null : null,
        SubDomainId: article.SubDomain,
        Domain: domainDisplay,
        ShowsAds: article.ShowsAds,
        Published: article.Published,
        UserName: article.User_Id_Settings ? adminUserMap.get(article.User_Id_Settings) || null : null,
        CampaignCount: article._count.ArticleCampaignMappings,
      };
    });

    // Apply search filter with enhanced domain matching
    if (search) {
      const searchTerm = search.toLowerCase();
      transformedArticles = transformedArticles.filter(article => {
        return (
          (article.Title?.toLowerCase().includes(searchTerm)) ||
          (article.CustomChannal?.toLowerCase().includes(searchTerm)) ||
          (article.CategoryName?.toLowerCase().includes(searchTerm)) ||
          (domainMatchesSearch(article.Domain, searchTerm)) ||
          (article.UserName?.toLowerCase().includes(searchTerm)) ||
          (article.CampaignCount?.toString().includes(searchTerm))
        );
      });
    }

    // Enhanced sorting logic with null/undefined handling and case insensitivity
    transformedArticles.sort((a, b) => {
      const valA = (a as any)[orderBy];
      const valB = (b as any)[orderBy];

      if (valA == null && valB == null) return 0;
      if (valA == null) return orderDir === 'asc' ? 1 : -1;
      if (valB == null) return orderDir === 'asc' ? -1 : 1;

      if (orderBy === 'CampaignCount' || typeof valA === 'number' && typeof valB === 'number') {
        const numA = Number(valA) || 0;
        const numB = Number(valB) || 0;
        return orderDir === 'desc' ? numB - numA : numA - numB;
      }

      if (orderBy === 'ShowsAds' || orderBy === 'Published') {
        const boolA = Boolean(valA);
        const boolB = Boolean(valB);
        if (boolA === boolB) return 0;
        if (orderDir === 'desc') {
          return boolB ? 1 : -1;
        } else {
          return boolA ? 1 : -1;
        }
      }

      const strA = String(valA || '').toLowerCase().trim();
      const strB = String(valB || '').toLowerCase().trim();

      if (strA === strB) return 0;

      const comparison = strA.localeCompare(strB, undefined, {
        numeric: true,
        sensitivity: 'base'
      });

      return orderDir === 'desc' ? -comparison : comparison;
    });

    const recordsFiltered = transformedArticles.length;
    const paginatedArticles = length === -1
      ? transformedArticles
      : transformedArticles.slice(skip || 0, (skip || 0) + (limit || transformedArticles.length));

    const totalPages = length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1));

    return NextResponse.json({
      success: true,
      data: paginatedArticles,
      pagination: {
        draw,
        recordsTotal,
        recordsFiltered,
        currentPageCount: paginatedArticles.length,
        start,
        length,
        currentPage: start,
        totalPages,
        hasNextPage: length === -1 ? false : start * length < recordsFiltered,
        hasPreviousPage: start > 1,
      },
    });

  } catch (error) {
    console.error('Error fetching articles:', error);
    return NextResponse.json({ error: 'Failed to fetch articles' }, { status: 500 });
  }
}