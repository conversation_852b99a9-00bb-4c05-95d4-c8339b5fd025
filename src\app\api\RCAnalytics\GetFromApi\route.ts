import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import axios from 'axios';

interface GoogleAdsCampaignData {
    campaign: {
        id: string;
        name: string;
    };
    metrics: {
        clicks: string | number;
        conversions: string | number;
        costMicros: string | number;
        impressions: string | number;
        average_cpc: string | number;
    };
    geographicView?: {
        resource_name?: string;
        countryCriterionId?: string;
        location_type?: string;
    };
    segments?: {
        date?: string;
    };
}

interface ArticleDetails {
    Id: string;
    Domain: string | null;
    SubDomain: string | null;
}

interface SubDomain {
    Id: string;
    Domain: string;
    AccountId: (string | bigint | null)[];
}

interface CampaignMapping {
    ArticleId: string;
    CampaignId: number | null;
}

interface CampaignDetail {
    SNo: number;
    CampaignId: string;
}

interface GeographicData {
    date: string;
    countryCriterionId: string;
    metrics: {
        clicks: number;
        conversions: number;
        cost: number;
        impressions: number;
        averageCpc: number;
    };
}

interface ProcessedResult {
    geographic_data: GeographicData[];
}

function toNumber(value: string | number | undefined | null): number {
    if (value === undefined || value === null || value === "") return 0;
    if (typeof value === "number") return value;
    if (typeof value === "string") {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
}

async function getAccessToken(refreshToken: string, clientId: string, clientSecret: string): Promise<string> {
    const params = new URLSearchParams();
    params.append('refresh_token', refreshToken);
    params.append('client_id', clientId);
    params.append('client_secret', clientSecret);
    params.append('grant_type', 'refresh_token');

    const response = await axios.post('https://oauth2.googleapis.com/token', params);
    return response.data.access_token;
}

export async function POST(req: NextRequest) {
    try {
        const requestBody = await req.json();
        const { articleIds, startDate, endDate } = requestBody;

        if (!articleIds || !startDate || !endDate) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            );
        }

        const articleIdsArray: string[] = Array.isArray(articleIds)
            ? articleIds.filter((id: string) => id?.trim() !== '')
            : articleIds.split(',').filter((id: string) => id?.trim() !== '');

        if (articleIdsArray.length === 0) {
            return NextResponse.json({}, { status: 200 });
        }

        const adminSettings = await prisma.adminUserSetting.findFirst({
            select: {
                CampaignClientId: true,
                CampaignClientSecret: true,
                CampaignRefreshToken: true,
                CampaignAccessToken: true,
                DeveloperToken: true,
                LoginCustomerId: true
            },
        });

        if (!adminSettings?.CampaignRefreshToken) {
            return NextResponse.json(
                { error: "Google Ads credentials not configured" },
                { status: 400 }
            );
        }

        const developerToken = adminSettings?.DeveloperToken;
        const loginCustomerId = adminSettings?.LoginCustomerId;

        if (!developerToken || !loginCustomerId) {
            return NextResponse.json(
                { error: "Missing Google Ads configuration" },
                { status: 500 }
            );
        }

        // Get campaign mappings and details
        const campaignMappings = await prisma.articleCampaignMappings.findMany({
            where: { ArticleId: { in: articleIdsArray } },
            select: { ArticleId: true, CampaignId: true },
        });

        const validMappings: { ArticleId: string; CampaignId: number }[] = campaignMappings
            .filter((m): m is { ArticleId: string; CampaignId: number } => m.CampaignId !== null);

        if (validMappings.length === 0) {
            return NextResponse.json({}, { status: 200 });
        }

        const campaignDetails = await prisma.ads_Campaigns.findMany({
            where: { SNo: { in: validMappings.map(m => m.CampaignId) } },
            select: { SNo: true, CampaignId: true },
        });

        // Initialize result structure
        const result: Record<string, ProcessedResult> = {};
        articleIdsArray.forEach((articleId: string) => {
            result[articleId] = {
                geographic_data: []
            };
        });

        // Get articles and subdomains
        const articles = await prisma.articleDetails.findMany({
            where: { Id: { in: articleIdsArray } },
            select: { Id: true, Domain: true, SubDomain: true },
        });

        const subDomains = await prisma.subDomain.findMany({
            where: {
                Id: {
                    in: articles
                        .map(a => a.SubDomain)
                        .filter((id): id is string => id !== null)
                },
                Domain: {
                    in: articles
                        .map(a => a.Domain)
                        .filter((domain): domain is string => domain !== null)
                },
            },
            select: { Id: true, Domain: true, AccountId: true },
        });

        let accessToken = adminSettings.CampaignAccessToken;

        for (const article of articles) {
            const account = subDomains.find(
                sd => sd.Domain === article.Domain && sd.Id === article.SubDomain
            );

            if (!account?.AccountId || !Array.isArray(account.AccountId)) continue;

            const validAccountIds = account.AccountId
                .filter((id): id is string => typeof id === 'string');

            if (validAccountIds.length === 0) continue;

            const articleCampaigns = validMappings
                .filter(m => m.ArticleId === article.Id)
                .map(m => {
                    const campaign = campaignDetails.find(c => c.SNo === m.CampaignId);
                    return campaign?.CampaignId;
                })
                .filter((id): id is bigint => typeof id === 'bigint')
                .map(id => id.toString());

            if (articleCampaigns.length === 0) continue;

            for (const accountId of validAccountIds) {
                try {
                    const query = `
                        SELECT 
                            campaign.id,
                            segments.date,
                            metrics.clicks,
                            metrics.conversions,
                            metrics.cost_micros,
                            metrics.impressions,
                            metrics.average_cpc,
                            geographic_view.country_criterion_id
                        FROM geographic_view
                        WHERE campaign.id IN (${articleCampaigns.join(',')})
                        AND segments.date BETWEEN '${startDate}' AND '${endDate}'`;

                    const headers = {
                        'Authorization': `Bearer ${accessToken}`,
                        'developer-token': developerToken,
                        'login-customer-id': loginCustomerId,
                        'Content-Type': 'application/json',
                    };

                    let response;
                    try {
                        response = await axios.post(
                            `https://googleads.googleapis.com/v19/customers/${accountId}/googleAds:search`,
                            { query },
                            { headers }
                        );
                    } catch (error) {
                        if (axios.isAxiosError(error) && error.response?.status === 401) {
                            accessToken = await getAccessToken(
                                adminSettings.CampaignRefreshToken,
                                adminSettings.CampaignClientId as string,
                                adminSettings.CampaignClientSecret as string
                            );
                            headers.Authorization = `Bearer ${accessToken}`;
                            response = await axios.post(
                                `https://googleads.googleapis.com/v19/customers/${accountId}/googleAds:search`,
                                { query },
                                { headers }
                            );
                        } else {
                            throw error;
                        }
                    }

                    if (response.data?.results) {
                        response.data.results.forEach((r: GoogleAdsCampaignData) => {

                            const costMicros = toNumber(r.metrics.costMicros);

                            const cost = costMicros / 1000000;
                            const clicks = toNumber(r.metrics.clicks);
                            const conversions = toNumber(r.metrics.conversions);
                            const impressions = toNumber(r.metrics.impressions);
                            const averageCpc = toNumber(r.metrics.average_cpc) / 1000000; // Convert from micros

                            result[article.Id].geographic_data.push({
                                date: r.segments?.date || new Date().toISOString().split('T')[0],
                                countryCriterionId: r.geographicView?.countryCriterionId || 'unknown',
                                metrics: {
                                    clicks,
                                    conversions,
                                    cost,
                                    impressions,
                                    averageCpc
                                }
                            });
                        });
                    }
                } catch (error) {
                    console.error(`Error processing account ${accountId}:`, error);
                    continue;
                }
            }
        }

        // Always return with articleId as key, even for single article
        return NextResponse.json(result, { status: 200 });

    } catch (error) {
        console.error("API Error:", error);
        return NextResponse.json(
            {
                error: "Failed to fetch Google Ads data",
                details: error instanceof Error ? error.message : String(error),
            },
            { status: 500 }
        );
    }
}