import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { writeFile, access, constants } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { mkdirSync, existsSync } from 'fs';
import sharp from 'sharp';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || '/var/www/images/categories';
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MIN_FILE_SIZE = 1024;

type SharpImageFormat = keyof sharp.FormatEnum;

export async function POST(req: NextRequest) {
    // const user = await verifyToken(req);

    // if (!user) {
    //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    try {
        // Ensure upload directory exists and is writable
        if (!existsSync(UPLOAD_DIR)) {
            mkdirSync(UPLOAD_DIR, { recursive: true });
        }
        try {
            await access(UPLOAD_DIR, constants.W_OK);
        } catch (err) {
            console.error('Upload directory is not writable:', UPLOAD_DIR);
            return NextResponse.json(
                { error: 'Server configuration error: upload directory not writable' },
                { status: 500 }
            );
        }

        const formData = await req.formData();

        const name = formData.get('name')?.toString();
        const domain = formData.get('domain')?.toString();

        if (!name || !domain) {
            return NextResponse.json(
                { error: 'Missing required fields: name and domain are mandatory.' },
                { status: 400 }
            );
        }

        const showUrlName = formData.get('showUrlName')?.toString()?.trim() ||
            name.replace(/[^a-zA-Z0-9]+/g, '-').toLowerCase();
        const title = formData.get('title')?.toString();
        const shortDescription = formData.get('shortDescription')?.toString() || null;
        const imageFile = formData.get('image') as File | null;

        function isSharpImageFormat(format: string): format is SharpImageFormat {
            const sharpFormats: SharpImageFormat[] = ['jpeg', 'png', 'webp', 'gif', 'tiff', 'raw'];
            return sharpFormats.includes(format as SharpImageFormat);
        }

        let imagePath: string | null = null;

        if (imageFile && imageFile.size > 0) {
            try {
                const buffer = Buffer.from(await imageFile.arrayBuffer());
                let mimeType = imageFile.type;
                let originalExtension: SharpImageFormat = 'jpeg';

                if (mimeType === 'application/octet-stream') {
                    try {
                        const metadata = await sharp(buffer).metadata();
                        if (metadata.format && isSharpImageFormat(metadata.format)) {
                            mimeType = `image/${metadata.format}`;
                            originalExtension = metadata.format;
                        }
                    } catch {
                        throw new Error('Invalid image format');
                    }
                } else {
                    const ext = mimeType.split('/')[1];
                    if (ext && isSharpImageFormat(ext)) {
                        originalExtension = ext;
                    }
                }

                if (!ALLOWED_IMAGE_TYPES.includes(mimeType)) {
                    throw new Error(`Invalid file type: ${mimeType}`);
                }

                if (imageFile.size < MIN_FILE_SIZE) {
                    throw new Error(`File too small: ${imageFile.size} bytes`);
                }

                // Get domain prefix
                const domainpre = await prisma.domain.findUnique({
                    where: { Id: domain },
                    select: { Prefix: true }
                });
                const domainPrefix = domainpre?.Prefix?.toLowerCase();

                const uploadPath = domainPrefix
                    ? path.join(UPLOAD_DIR, domainPrefix)
                    : UPLOAD_DIR;

                // Use existsSync and mkdirSync from the imported fs module
                if (!existsSync(uploadPath)) {
                    mkdirSync(uploadPath, { recursive: true });
                }

                const uniqueId = uuidv4();
                const baseFileName = `category_${uniqueId}.${originalExtension}`;

                const [originalBuffer, thumbnailBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, 300, { fit: 'cover', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                await Promise.all([
                    writeFile(path.join(uploadPath, baseFileName), originalBuffer),
                    writeFile(path.join(uploadPath, `thumb_${uniqueId}.${originalExtension}`), thumbnailBuffer)
                ]);

                // Set image path based on domain prefix
                if (domainPrefix) {
                    imagePath = `${domainPrefix}/${baseFileName}`;
                } else {
                    imagePath = baseFileName;
                }
            } catch (error) {
                console.error('Error processing category image:', error);
                return NextResponse.json(
                    { error: 'Failed to process image. Please upload a valid image file.' },
                    { status: 400 }
                );
            }
        }

        const existingActiveWithSameUrl = await prisma.category.findFirst({
            where: {
                ShowUrlName: showUrlName,
                Domain: domain,
                IsDeleted: false
            },
        });

        if (existingActiveWithSameUrl) {
            return NextResponse.json(
                { error: 'A category with the same URL already exists in this domain.' },
                { status: 409 }
            );
        }

        const existingDeletedCategory = await prisma.category.findFirst({
            where: {
                ShowUrlName: showUrlName,
                Domain: domain,
                IsDeleted: true
            },
        });

        if (existingDeletedCategory) {
            const updatedCategory = await prisma.category.update({
                where: { Id: existingDeletedCategory.Id },
                data: {
                    Title: title,
                    Image: imagePath || existingDeletedCategory.Image,
                    ShortDescription: shortDescription,
                    Name: name,
                    IsDeleted: false
                },
            });

            return NextResponse.json({
                success: true,
                message: 'Category Created Successfully'
            });
        }

        // const existingActiveWithSameName = await prisma.category.findFirst({
        //     where: {
        //         Name: name,
        //         Domain: domain,
        //         IsDeleted: false
        //     },
        // });

        // if (existingActiveWithSameName) {
        //     return NextResponse.json(
        //         { error: 'Category name already exists in this domain' },
        //         { status: 409 }
        //     );
        // }

        const newCategory = await prisma.category.create({
            data: {
                Name: name,
                ShowUrlName: showUrlName,
                Title: title,
                Image: imagePath,
                ShortDescription: shortDescription,
                Domain: domain
            },
        });

        return NextResponse.json({
            success: true,
            data: {
                ...newCategory,
                imageUrl: imagePath ? `/images/categories/${imagePath}` : null,
            },
        });

    } catch (err) {
        console.error('Add Category Error:', err);
        return NextResponse.json(
            { error: err instanceof Error ? err.message : 'Internal Server Error' },
            { status: 500 }
        );
    }
}