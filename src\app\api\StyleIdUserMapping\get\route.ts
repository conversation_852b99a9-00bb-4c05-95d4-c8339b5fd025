import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const styleId = searchParams.get("Id");

        // Get sorting parameters with validation
        const orderBy = searchParams.get("orderBy")?.toLowerCase() || "createdat";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        if (!styleId) {
            return NextResponse.json(
                { error: "styleId parameter is required" },
                { status: 400 }
            );
        }

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        let where: any = {
            StyleId: styleId,
            AdminUser: {
                IsDeleted: false
            }
        };

        if (search && search !== "") {
            where.AdminUser = {
                ...where.AdminUser,
                OR: [
                    { Name: { contains: search, mode: 'insensitive' } },
                    { Email: { contains: search, mode: 'insensitive' } },
                    { User_Type: { contains: search, mode: 'insensitive' } }
                ]
            };
        }

        // Define the orderBy clause
        let orderByClause: any;

        switch (orderBy) {
            case 'username':
                orderByClause = { AdminUser: { Name: orderDir } };
                break;
            case 'useremail':
                orderByClause = { AdminUser: { Email: orderDir } };
                break;
            case 'usertype':
                orderByClause = { AdminUser: { User_Type: orderDir } };
                break;
            case 'createdat':
                orderByClause = { CreatedAt: orderDir };
                break;
            default:
                orderByClause = { [orderBy]: orderDir };
        }

        // Get counts
        const [recordsTotal, recordsFiltered] = await Promise.all([
            prisma.styleIdUserMappings.count({
                where: { StyleId: styleId }
            }),
            prisma.styleIdUserMappings.count({ where })
        ]);

        // Get paginated data
        const mappings = await prisma.styleIdUserMappings.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            include: {
                AdminUser: {
                    select: {
                        Id: true,
                        Name: true,
                        Email: true,
                        User_Type: true
                    }
                }
            }
        });

        // Transform data
        const transformedData = mappings.map(mapping => ({
            Id: mapping.Id,
            CreatedAt: mapping.CreatedAt,
            UserId: mapping.AdminUser?.Id,
            UserName: mapping.AdminUser?.Name,
            UserEmail: mapping.AdminUser?.Email,
            UserType: mapping.AdminUser?.User_Type
        }));

        const totalPages = length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1));

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                recordsFiltered,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages,
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    } 
}