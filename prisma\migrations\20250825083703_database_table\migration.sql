-- CreateTable
CREATE TABLE "AdminUser" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Name" TEXT,
    "Email" TEXT,
    "Password" TEXT,
    "User_Type" TEXT,
    "Block" BOOLEAN DEFAULT false,
    "Number" TEXT,
    "Status" BOOLEAN DEFAULT true,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),
    "DisplayName" TEXT,
    "ProfilePic" TEXT,
    "AccessExpiration" TIMESTAMP(6),
    "IsDeleted" BOOLEAN DEFAULT false,
    "AboutMe" TEXT,

    CONSTRAINT "adminuser_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "AdminUserSetting" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "LmStyleId" TEXT,
    "DmStyleId" TEXT,
    "ChannalId" UUID,
    "PubId" TEXT,
    "AdsAccountId" TEXT,
    "AdsClientId" TEXT,
    "HeadTagJSON" TEXT,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),
    "CountAdsClick" TEXT,
    "CampaignClientId" TEXT,
    "CampaignClientSecret" TEXT,
    "CampaignAccessToken" TEXT,
    "CampaignRefreshToken" TEXT,
    "RevenueClientId" TEXT,
    "RevenueClientSecret" TEXT,
    "RevenueAccessToken" TEXT,
    "RevenueRefreshToken" TEXT,
    "CampaignRedirectUrl" TEXT,
    "RevenueRedirectUrl" TEXT,
    "CampaignScope" TEXT,
    "RevenueScope" TEXT,
    "DeveloperToken" TEXT,
    "LoginCustomerId" TEXT,
    "PublicBaseUrl" TEXT,

    CONSTRAINT "adminusersetting_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Ads_AccountDetails" (
    "SNo" SERIAL NOT NULL,
    "AccountId" BIGINT NOT NULL,
    "ResourceName" TEXT,
    "ClientCustomer" TEXT,
    "Level" INTEGER,
    "TimeZone" TEXT,
    "Manager" BOOLEAN,
    "DescriptiveName" TEXT,
    "CurrencyCode" CHAR(10),

    CONSTRAINT "account_details_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Ads_CampaignCountryDetails" (
    "SNo" SERIAL NOT NULL,
    "CampaignId" BIGINT,
    "CountryId" BIGINT,
    "Clicks" INTEGER,
    "Impressions" BIGINT,
    "AverageCPC" DECIMAL(18,4),
    "CostMicros" DECIMAL(18,4),
    "Conversions" DECIMAL(18,4),
    "SegmentDate" DATE,

    CONSTRAINT "ads_campaigncountrydetails_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Ads_CampaignDetails" (
    "SNo" SERIAL NOT NULL,
    "CampaignId" BIGINT,
    "ActiveViewCPM" DECIMAL(18,4),
    "ActiveViewCTR" DECIMAL(18,4),
    "ActiveViewImpressions" BIGINT,
    "ActiveViewMeasurability" DECIMAL(18,4),
    "ActiveViewMeasurableCostMicros" BIGINT,
    "ActiveViewMeasurableImpressions" BIGINT,
    "ActiveViewViewAbility" DECIMAL(18,4),
    "AllConversionsValue" DECIMAL(18,4),
    "AllConversionsValueByConversionDate" DECIMAL(18,4),
    "AllConversions" DECIMAL(18,4),
    "AllConversionsByConversionDate" DECIMAL(18,4),
    "AverageCost" DECIMAL(18,4),
    "AverageCPC" DECIMAL(18,4),
    "AverageCPM" DECIMAL(18,4),
    "Clicks" INTEGER,
    "ConversionsFromInteractionsRate" DECIMAL(18,4),
    "ConversionsValue" DECIMAL(18,4),
    "ConversionsValueByConversionDate" DECIMAL(18,4),
    "Conversions" DECIMAL(18,4),
    "ConversionsByConversionDate" DECIMAL(18,4),
    "CostMicros" BIGINT,
    "CostPerAllConversions" DECIMAL(18,4),
    "CostPerConversion" DECIMAL(18,4),
    "CostPerCurrentModelAttributedConversion" DECIMAL(18,4),
    "CTR" DECIMAL(18,4),
    "CurrentModelAttributedConversions" DECIMAL(18,4),
    "CurrentModelAttributedConversionsFromInteractionsRate" DECIMAL(18,4),
    "CurrentModelAttributedConversionsFromInteractionsValuePerIntera" DECIMAL(18,4),
    "CurrentModelAttributedConversionsValue" DECIMAL(18,4),
    "CurrentModelAttributedConversionsValuePerCost" DECIMAL(18,4),
    "Engagements" BIGINT,
    "Impressions" BIGINT,
    "InteractionRate" DECIMAL(18,4),
    "Interactions" BIGINT,
    "InvalidClickRate" DECIMAL(18,4),
    "InvalidClicks" BIGINT,
    "GeneralInvalidClickRate" DECIMAL(18,4),
    "GeneralInvalidClicks" BIGINT,
    "AverageTargetCpaMicros" BIGINT,
    "ValuePerAllConversions" DECIMAL(18,4),
    "ValuePerAllConversionsByConversionDate" DECIMAL(18,4),
    "ValuePerConversion" DECIMAL(18,4),
    "ValuePerConversionsByConversionDate" DECIMAL(18,4),
    "ValuePerCurrentModelAttributedConversion" DECIMAL(18,4),
    "SegmentDate" DATE,

    CONSTRAINT "campaign_details_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Ads_Campaigns" (
    "SNo" SERIAL NOT NULL,
    "AccountId" BIGINT,
    "CampaignId" BIGINT,
    "CustomerResourceName" TEXT,
    "CampaignResourceName" TEXT,
    "Status" TEXT,
    "AdvertisingChannelType" TEXT,
    "NetworkSettings" JSON,
    "ExperimentType" TEXT,
    "ServingStatus" TEXT,
    "BiddingStrategyType" TEXT,
    "GeoTargetTypeSetting" JSON,
    "PaymentMode" TEXT,
    "Name" TEXT,
    "StartDate" DATE,
    "EndDate" DATE,
    "OptimizationScore" DECIMAL(18,4),
    "PrimaryStatus" TEXT,
    "PrimaryStatusReasons" TEXT[],
    "BrandGuidelinesEnabled" BOOLEAN,
    "SegmentDate" DATE,

    CONSTRAINT "campaigns_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Ads_CampaignsBudget" (
    "SNo" SERIAL NOT NULL,
    "CampaignId" BIGINT,
    "CampaignBudgetId" BIGINT,
    "CampaignBudgetResourceName" TEXT,
    "Name" TEXT,
    "Status" TEXT,
    "AmountMicros" BIGINT,
    "DeliveryMethod" TEXT,
    "ExplicitlyShared" BOOLEAN,
    "HasRecommendedBudget" BOOLEAN,
    "Period" TEXT,
    "ReferenceCount" INTEGER,
    "Type" TEXT,
    "SegmentDate" DATE,

    CONSTRAINT "ads_campaignsbudget_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Ads_CountryMaster" (
    "SNo" SERIAL NOT NULL,
    "CountryId" BIGINT,
    "Name" TEXT,
    "CountryCode" TEXT,

    CONSTRAINT "ads_countrymaster_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "ArticleCampaignMappings" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "ArticleId" UUID,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "CampaignId" INTEGER,

    CONSTRAINT "campaignmappings_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ArticleDetails" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Title" TEXT,
    "Category" UUID,
    "Url" TEXT,
    "Description" TEXT,
    "MetaTitle" TEXT,
    "MetaDescription" TEXT,
    "MetaKeys" TEXT,
    "StyleIdLm" UUID,
    "User_Id_Settings" UUID,
    "AdRelatedSearches" TEXT,
    "Remark" TEXT,
    "Image" TEXT,
    "Published" BOOLEAN DEFAULT true,
    "ShowArticle" BOOLEAN DEFAULT true,
    "ShowUrlName" TEXT,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),
    "ShowsAds" BOOLEAN DEFAULT true,
    "CustomChannal" UUID,
    "Domain" UUID,
    "SubDomain" UUID,
    "ShortDescription" TEXT,
    "IsDeleted" BOOLEAN DEFAULT false,
    "ReadTime" INTEGER,
    "CheckRelatedSearches2" BOOLEAN,
    "AdReletadSearches2" TEXT,
    "StyleIdDm" UUID,
    "ShortName" TEXT,
    "ShowInDomain" BOOLEAN DEFAULT false,

    CONSTRAINT "articledetails_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "CampaignBudgets" (
    "SNo" SERIAL NOT NULL,
    "CampaignId" BIGINT NOT NULL,
    "BudgetId" BIGINT,
    "AmountMicros" BIGINT,
    "SegmentDate" DATE NOT NULL,

    CONSTRAINT "CampaignBudgets_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Campaigns" (
    "SNo" SERIAL NOT NULL,
    "CampaignId" BIGINT NOT NULL,
    "AccountId" BIGINT,
    "Name" TEXT,
    "Status" TEXT,
    "Type" TEXT,
    "SegmentDate" DATE DEFAULT CURRENT_DATE,

    CONSTRAINT "Campaigns_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Category" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Name" TEXT NOT NULL,
    "ShowUrlName" TEXT,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),
    "IsDeleted" BOOLEAN DEFAULT false,
    "Title" TEXT,
    "Image" TEXT,
    "ShortDescription" TEXT,
    "Domain" UUID,

    CONSTRAINT "category_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Channals" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Name" TEXT,
    "ReportingDimensionId" TEXT,
    "DisplayName" TEXT,
    "Active" BOOLEAN DEFAULT true,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),

    CONSTRAINT "assignchannal_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Domain" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Name" TEXT,
    "ShowUrlName" TEXT,
    "SubDomains" TEXT[],
    "Articles" TEXT[],
    "CreatedBy" UUID,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),
    "V" INTEGER,
    "Prefix" TEXT,
    "ChannelId" TEXT,
    "CookieMinutes" INTEGER,
    "StyleIdDm" BIGINT,
    "StyleIdLm" BIGINT,
    "IsDeleted" BOOLEAN DEFAULT false,
    "HeadTagScript" TEXT,
    "HeadTagScriptLandingPage" TEXT,
    "HeadTagScriptSearchPage" TEXT,
    "GId" TEXT,
    "AWId" TEXT,
    "SendTo" TEXT,
    "WideLogo" TEXT,
    "SquareLogo" TEXT,
    "ContactEmail" TEXT,
    "Address" TEXT,
    "AdsTxtContent" TEXT,

    CONSTRAINT "domain_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "IpAddress" (
    "Id" UUID NOT NULL,
    "IpAddress" TEXT,
    "NoOfBlackList" INTEGER,

    CONSTRAINT "IpAddress_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "QueryAnalytics" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "BrowserId" TEXT,
    "IpAddress" TEXT,
    "URL" TEXT,
    "Keyword" TEXT,
    "Count" INTEGER,
    "AdsClickCounter" INTEGER,
    "Country" TEXT,
    "State" TEXT,
    "Created_At" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "Domain" TEXT,
    "City" TEXT,

    CONSTRAINT "QueryAnalytics_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Revenue" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Date" DATE,
    "StyleId" TEXT,
    "ChannalId" TEXT,
    "Country" TEXT,
    "PlatfromType" TEXT,
    "EstimatedEarnings" DECIMAL(18,4),
    "Impressions" DECIMAL(18,4),
    "ImpressionsRpm" DECIMAL(18,4),
    "Clicks" DECIMAL(18,4),
    "ImpressionsCtr" DECIMAL(18,4),
    "CostPerClick" DECIMAL(18,4),

    CONSTRAINT "Revenue_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "SavedReport" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Name" TEXT,
    "Title" TEXT,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "savedreport_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "StyleIdUserMappings" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "UserId" UUID,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "StyleId" UUID,
    "V" INTEGER,

    CONSTRAINT "styleidmappings_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "StyleIds" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "StyleId" TEXT,
    "Name" TEXT,
    "Prefix" TEXT,
    "CreatedBy" UUID,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),
    "V" INTEGER,
    "DomainId" TEXT,
    "IsDeleted" BOOLEAN DEFAULT false,

    CONSTRAINT "styleids_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "SubDomain" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "Name" TEXT,
    "Url" TEXT,
    "Domain" UUID,
    "HeadTag" TEXT,
    "CId" TEXT,
    "Articles" TEXT[],
    "CreatedBy" UUID,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(6),
    "V" INTEGER,
    "AccountId" TEXT[],
    "IsDeleted" BOOLEAN DEFAULT false,
    "HeadTagScript" TEXT,
    "HeadTagScriptLandingPage" TEXT,
    "HeadTagScriptSearchPage" TEXT,
    "GId" TEXT,
    "AWId" TEXT,
    "SendTo" TEXT,
    "WideLogo" TEXT,
    "SquareLogo" TEXT,

    CONSTRAINT "subdomain_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "SubDomainUserMappings" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "SubDomainId" UUID,
    "UserId" UUID,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "V" INTEGER,

    CONSTRAINT "subdomainusermappings_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "adsanalytics" (
    "id" BIGSERIAL NOT NULL,
    "uuid" UUID NOT NULL,
    "browser_id" TEXT NOT NULL,
    "ip_address" TEXT,
    "state" TEXT,
    "country" TEXT,
    "url" TEXT,
    "keyword" TEXT,
    "is_ads_click" BOOLEAN,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "adsanalytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cookie_data" (
    "id" SERIAL NOT NULL,
    "cookie_value" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cookie_data_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "getGeoTargetConstants" (
    "SNo" SERIAL NOT NULL,
    "CountryId" BIGINT NOT NULL,
    "Name" TEXT NOT NULL,

    CONSTRAINT "getGeoTargetConstants_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "getGeographicView" (
    "SNo" SERIAL NOT NULL,
    "CampaignId" BIGINT NOT NULL,
    "CountryId" BIGINT NOT NULL,
    "Clicks" INTEGER,
    "Impressions" BIGINT,
    "AverageCPC" DECIMAL,
    "CostMicros" DECIMAL,
    "Conversions" DECIMAL,
    "SegmentDate" DATE,

    CONSTRAINT "getGeographicView_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "timeinformation" (
    "uuid" UUID NOT NULL,
    "time1" TIMESTAMPTZ(6) NOT NULL,
    "time2" TIMESTAMPTZ(6) NOT NULL,

    CONSTRAINT "timeinformation_pkey" PRIMARY KEY ("uuid")
);

-- CreateTable
CREATE TABLE "Ads_ISO_CountryMaster" (
    "SNo" SERIAL NOT NULL,
    "Name" TEXT,
    "CountryCode" TEXT,

    CONSTRAINT "ads_countrymaster_test_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "Ads_RateConversion" (
    "SNo" SERIAL NOT NULL,
    "Date" DATE NOT NULL,
    "USDtoINR" DECIMAL(15,6),
    "INRtoUSD" DECIMAL(15,6),

    CONSTRAINT "Ads_RateConversion_pkey" PRIMARY KEY ("SNo")
);

-- CreateTable
CREATE TABLE "ArticleIdMap" (
    "SourceId" TEXT NOT NULL,
    "DestinationId" UUID NOT NULL,
    "SourceType" TEXT NOT NULL,
    "CreatedAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ArticleIdMap_pkey" PRIMARY KEY ("SourceId")
);

ALTER TABLE "AdminUser" ADD CONSTRAINT adminuser_email_key UNIQUE("Email");
ALTER TABLE "Ads_AccountDetails" ADD CONSTRAINT ads_accountdetails_accountid_key UNIQUE("AccountId");
ALTER TABLE "Ads_CampaignDetails" ADD CONSTRAINT unique_campaigndetails_id_date UNIQUE("CampaignId", "SegmentDate");
ALTER TABLE "CampaignBudgets" ADD CONSTRAINT unique_campaign_budget_date UNIQUE("CampaignId", "SegmentDate");
ALTER TABLE "Campaigns" ADD CONSTRAINT campaigns_campaignid_key UNIQUE("CampaignId");
ALTER TABLE "Category" ADD CONSTRAINT showurlname_domain UNIQUE("ShowUrlName", "Domain");
ALTER TABLE "Category" ADD CONSTRAINT name_domain UNIQUE("Name", "Domain");
ALTER TABLE "Channals" ADD CONSTRAINT uq_channals_name UNIQUE("Name");
ALTER TABLE "IpAddress" ADD CONSTRAINT ipaddress_ip_key UNIQUE("IpAddress");
ALTER TABLE "Revenue" ADD CONSTRAINT date_styleid_channalid_country_platfromtype UNIQUE("Date", "StyleId", "ChannalId", "Country", "PlatfromType");
ALTER TABLE "StyleIds" ADD CONSTRAINT uq_styleid UNIQUE("StyleId");
ALTER TABLE "SubDomainUserMappings" ADD CONSTRAINT userId_subdomainid UNIQUE("SubDomainId", "UserId");
ALTER TABLE "SubDomainUserMappings" ADD CONSTRAINT subdomain_user_unique UNIQUE("SubDomainId", "UserId");
ALTER TABLE "adsanalytics" ADD CONSTRAINT adsanalytics_uuid_key UNIQUE("uuid");
ALTER TABLE "getGeoTargetConstants" ADD CONSTRAINT unique_country_id UNIQUE("CountryId");
ALTER TABLE "Ads_RateConversion" ADD CONSTRAINT unique_date UNIQUE("Date");

-- AddForeignKey
ALTER TABLE "AdminUserSetting" ADD CONSTRAINT "fk_channals" FOREIGN KEY ("ChannalId") REFERENCES "Channals"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleCampaignMappings" ADD CONSTRAINT "fk_Article" FOREIGN KEY ("ArticleId") REFERENCES "ArticleDetails"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleCampaignMappings" ADD CONSTRAINT "fk_ads_campaigns" FOREIGN KEY ("CampaignId") REFERENCES "Ads_Campaigns"("SNo") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleDetails" ADD CONSTRAINT "fk_adminuser" FOREIGN KEY ("User_Id_Settings") REFERENCES "AdminUser"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleDetails" ADD CONSTRAINT "fk_category" FOREIGN KEY ("Category") REFERENCES "Category"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleDetails" ADD CONSTRAINT "fk_channals" FOREIGN KEY ("CustomChannal") REFERENCES "Channals"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleDetails" ADD CONSTRAINT "fk_domain" FOREIGN KEY ("Domain") REFERENCES "Domain"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleDetails" ADD CONSTRAINT "fk_styleids" FOREIGN KEY ("StyleIdLm") REFERENCES "StyleIds"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleDetails" ADD CONSTRAINT "fk_styleidsdm" FOREIGN KEY ("StyleIdDm") REFERENCES "StyleIds"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ArticleDetails" ADD CONSTRAINT "fk_subdomain" FOREIGN KEY ("SubDomain") REFERENCES "SubDomain"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "CampaignBudgets" ADD CONSTRAINT "CampaignBudgets_CampaignId_fkey" FOREIGN KEY ("CampaignId") REFERENCES "Campaigns"("CampaignId") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Campaigns" ADD CONSTRAINT "Campaigns_AccountId_fkey" FOREIGN KEY ("AccountId") REFERENCES "Ads_AccountDetails"("AccountId") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Category" ADD CONSTRAINT "fk_domain" FOREIGN KEY ("Domain") REFERENCES "Domain"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StyleIdUserMappings" ADD CONSTRAINT "fk_StyleIds" FOREIGN KEY ("StyleId") REFERENCES "StyleIds"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "StyleIdUserMappings" ADD CONSTRAINT "fk_adminuser" FOREIGN KEY ("UserId") REFERENCES "AdminUser"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SubDomain" ADD CONSTRAINT "fk_Domain" FOREIGN KEY ("Domain") REFERENCES "Domain"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SubDomainUserMappings" ADD CONSTRAINT "fk_SubDomain" FOREIGN KEY ("SubDomainId") REFERENCES "SubDomain"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "SubDomainUserMappings" ADD CONSTRAINT "fk_adminuser" FOREIGN KEY ("UserId") REFERENCES "AdminUser"("Id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "getGeographicView" ADD CONSTRAINT "fk_country" FOREIGN KEY ("CountryId") REFERENCES "getGeoTargetConstants"("CountryId") ON DELETE NO ACTION ON UPDATE NO ACTION;
