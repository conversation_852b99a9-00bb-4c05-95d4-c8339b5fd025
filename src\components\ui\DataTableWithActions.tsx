"use client";

import React from 'react';
import CustomDataTable from '@/components/DataTable/CustomDataTable';
import InputGroup from '@/components/FormElements/InputGroup';
import { Button } from '@/components/ui-elements/button';
import { FaPlus, FaFilter } from 'react-icons/fa';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string;
}

interface ActionButton {
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'green' | 'danger' | 'dark';
  className?: string;
}

interface DataTableWithActionsProps {
  // Data props
  data: any[];
  columns: Column[];
  loading?: boolean;
  totalCount?: number;
  
  // Pagination props
  page: number;
  rowsPerPage: number;
  onPageChange: (page: number) => void;
  onRowsPerPageChange: (rowsPerPage: number) => void;
  showPagination?: boolean;
  
  // Sorting props
  order?: 'asc' | 'desc';
  orderBy?: string;
  onRequestSort?: (property: string) => void;
  
  // Search props
  searchTerm?: string;
  onSearchChange?: (searchTerm: string) => void;
  searchPlaceholder?: string;
  showSearch?: boolean;
  
  // Action props
  actionButtons?: ActionButton[];
  showActions?: boolean;
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  onView?: (row: any) => void;
  onClone?: (row: any) => void;
  onBlock?: (row: any) => void;
  onReset?: (row: any) => void;
  
  // Custom action handlers
  handleSubDomainClick?: (row: any) => void;
  handleStyleIDClick?: (row: any) => void;
  handleUserCountClick?: (row: any) => void;
  handleViewCampaignsClick?: (row: any) => void;
  handleAddCampaignClick?: (row: any) => void;
  onInPersonLogin?: (row: any) => void;
  
  // Layout props
  title?: string;
  className?: string;
  containerClassName?: string;
  
  // Other props
  totals?: any;
  notShowAction?: boolean;
}

const DataTableWithActions: React.FC<DataTableWithActionsProps> = ({
  // Data props
  data,
  columns,
  loading = false,
  totalCount = 0,
  
  // Pagination props
  page,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  showPagination = true,
  
  // Sorting props
  order = 'desc',
  orderBy = '',
  onRequestSort,
  
  // Search props
  searchTerm = '',
  onSearchChange,
  searchPlaceholder = 'Search...',
  showSearch = true,
  
  // Action props
  actionButtons = [],
  showActions = true,
  onEdit,
  onDelete,
  onView,
  onClone,
  onBlock,
  onReset,
  
  // Custom action handlers
  handleSubDomainClick,
  handleStyleIDClick,
  handleUserCountClick,
  handleViewCampaignsClick,
  handleAddCampaignClick,
  onInPersonLogin,
  
  // Layout props
  title,
  className = '',
  containerClassName = '',
  
  // Other props
  totals,
  notShowAction = false,
}) => {
  return (
    <div className={`w-full ${containerClassName}`}>
      {/* Header Section */}
      <div className="mb-6">
        {title && (
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {title}
          </h2>
        )}
        
        {/* Action Bar */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          {/* Action Buttons */}
          {actionButtons.length > 0 && (
            <div className="flex flex-col items-center gap-4 sm:flex-row sm:items-center">
              {actionButtons.map((button, index) => (
                <Button
                  key={index}
                  type="button"
                  label={button.label}
                  className={`flex w-full items-center justify-center gap-2 sm:w-auto ${button.className || ''}`}
                  variant={button.variant || 'primary'}
                  shape="rounded"
                  icon={button.icon}
                  onClick={button.onClick}
                />
              ))}
            </div>
          )}
          
          {/* Search */}
          {showSearch && onSearchChange && (
            <div className="w-full sm:w-64">
              <InputGroup
                label="Search"
                type="text"
                placeholder={searchPlaceholder}
                value={searchTerm}
                handleChange={(e) => onSearchChange(e.target.value)}
              />
            </div>
          )}
        </div>
      </div>
      
      {/* Data Table */}
      <div className={`rounded-xl border border-stroke bg-white p-4 shadow-lg dark:border-dark-3 dark:bg-gray-dark sm:p-6 ${className}`}>
        <CustomDataTable
          isLoading={loading}
          columns={columns}
          rows={data}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={onPageChange}
          onRowsPerPageChange={onRowsPerPageChange}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onRequestSort={onRequestSort}
          onEdit={onEdit}
          onClone={onClone}
          onDelete={onDelete}
          onBlock={onBlock}
          onReset={onReset}
          onView={onView}
          notShowAction={notShowAction || !showActions}
          handleSubDomainClick={handleSubDomainClick}
          handleStyleIDClick={handleStyleIDClick}
          handleUserCountClick={handleUserCountClick}
          handleViewCampaignsClick={handleViewCampaignsClick}
          handleAddCampaignClick={handleAddCampaignClick}
          onInPersonLogin={onInPersonLogin}
          totals={totals}
          showPagination={showPagination}
        />
      </div>
    </div>
  );
};

export default DataTableWithActions;
