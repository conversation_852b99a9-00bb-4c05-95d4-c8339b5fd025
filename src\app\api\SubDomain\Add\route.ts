import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { sendKafkaEvent } from '@/lib/kafka';

type SubDomainUserMapping = {
    Id: string;
    CreatedAt: Date | null;
    UserId: string | null;
    V: number | null;
    SubDomainId: string | null;
};

function getString(formData: FormData, key: string): string | null {
    const val = formData.get(key);
    return typeof val === 'string' ? val.trim() : null;
}

function getNumber(formData: FormData, key: string): number | null {
    const val = getString(formData, key);
    return val ? Number(val) : null;
}

function getArray(formData: FormData, key: string): string[] {
    const val = getString(formData, key);
    return val ? JSON.parse(val) : [];
}

// Helper function to get string or undefined (for UUID fields)
function getStringOrUndefined(formData: FormData, key: string): string | undefined {
    const val = getString(formData, key);
    return val || undefined; // Return undefined if empty/null
}

export async function POST(req: NextRequest) {
    try {
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');
        const formData = await req.formData();

        const Name = getString(formData, 'Name');
        const Url = getString(formData, 'Url');
        const Domain = getString(formData, 'Domain');
        const AssignUsers = getArray(formData, 'AssignUsers');

        if (!Name || !Url || !Domain) {
            return NextResponse.json(
                { error: 'Name, Url, and Domain are required' },
                { status: 400 }
            );
        }

        const AccountId = getArray(formData, 'AccountId');
        const CId = getStringOrUndefined(formData, 'CId');
        const HeadTag = getString(formData, 'HeadTag');
        const HeadTagScript = getString(formData, 'HeadTagScript');
        const HeadTagScriptLandingPage = getString(formData, 'HeadTagScriptLandingPage');
        const HeadTagScriptSearchPage = getString(formData, 'HeadTagScriptSearchPage');
        const GId = getStringOrUndefined(formData, 'GId');
        const AWId = getStringOrUndefined(formData, 'AWId');
        const SendTo = getString(formData, 'SendTo');
        const AdsProviderId = getStringOrUndefined(formData, 'AdsProviderId');
        const AdsProviderDescription = getString(formData, 'AdsProviderDescription');
        const TrackingKey = getString(formData, 'TrackingKey');

        let validUsers: { Id: string }[] = [];

        if (AssignUsers.length > 0) {
            validUsers = await prisma.adminUser.findMany({
                where: {
                    Id: { in: AssignUsers },
                    IsDeleted: false
                },
                select: { Id: true }
            });

            if (validUsers.length !== AssignUsers.length) {
                const foundIds = validUsers.map(u => u.Id);
                const invalidIds = AssignUsers.filter(id => !foundIds.includes(id));
                return NextResponse.json({ error: 'Invalid user IDs', invalidIds }, { status: 400 });
            }
        }

        const existingSubdomain = await prisma.subDomain.findFirst({
            where: {
                Name,
                Domain,
                IsDeleted: false
            }
        });

        if (existingSubdomain) {
            return NextResponse.json({
                error: `SubDomain '${Name}' already exists in domain '${Domain}'`
            }, { status: 409 });
        }

        const existingUrl = await prisma.subDomain.findFirst({
            where: {
                Url,
                Domain,
                IsDeleted: false
            }
        });

        if (existingUrl) {
            return NextResponse.json({
                error: `SubDomain with URL '${Url}' already exists in domain '${Domain}'`
            }, { status: 409 });
        }

        const result = await prisma.$transaction(async (tx) => {
            if (AdsProviderId) {
                try {
                    await tx.adsProvider.update({
                        where: { Id: AdsProviderId },
                        data: {
                            Description: AdsProviderDescription,
                            TrackingKey: TrackingKey
                        }
                    });
                } catch (error) {
                    console.error('Error updating AdsProvider description:', error);
                }
            }

            const newSubdomain = await tx.subDomain.create({
                data: {
                    Name,
                    Url,
                    Domain,
                    CId,
                    AccountId: AccountId.length ? AccountId : undefined,
                    HeadTag,
                    HeadTagScript,
                    HeadTagScriptLandingPage,
                    HeadTagScriptSearchPage,
                    GId,
                    AWId,
                    SendTo,
                    CreatedBy: userId,
                    CreatedAt: new Date(),
                    AdsProviderId,
                    TrackingKey
                },
            });

            let mappings: SubDomainUserMapping[] = [];

            if (AssignUsers.length > 0) {
                mappings = await Promise.all(
                    AssignUsers.map((userId) =>
                        tx.subDomainUserMappings.create({
                            data: {
                                SubDomainId: newSubdomain.Id,
                                UserId: userId,
                            },
                        })
                    )
                );
            }
            return { subdomain: newSubdomain, mappings };
        });

        try {
            const payload = {
                'Type': "subDomain",
                'Id': result.subdomain.Id,
            };

            await sendKafkaEvent(payload);
        } catch (kafkaError) {
            console.error("Kafka event failed:", kafkaError);
        }

        return NextResponse.json({
            success: true,
            message: 'SubDomain created successfully',
            subdomain: result.subdomain,
            mappingsCreated: result.mappings.length
        }, { status: 201 });

    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(
            { error: 'Internal Server Error', details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined },
            { status: 500 }
        );
    }
}