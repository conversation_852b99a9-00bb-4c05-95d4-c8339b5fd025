import { useState, useEffect, useCallback } from 'react';
import { useApiPost } from './useApi';
import useDebounce from './useDebounce';

interface PaginationState {
  page: number;
  rowsPerPage: number;
  totalCount: number;
  searchTerm: string;
  order: 'asc' | 'desc';
  orderBy: string;
}

interface UsePaginatedDataOptions {
  initialPage?: number;
  initialRowsPerPage?: number;
  initialOrder?: 'asc' | 'desc';
  initialOrderBy?: string;
  debounceDelay?: number;
  autoFetch?: boolean;
  additionalFilters?: Record<string, any>;
}

interface UsePaginatedDataReturn<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  setPage: (page: number) => void;
  setRowsPerPage: (rowsPerPage: number) => void;
  setSearchTerm: (searchTerm: string) => void;
  setOrder: (order: 'asc' | 'desc') => void;
  setOrderBy: (orderBy: string) => void;
  handleSort: (property: string) => void;
  refetch: () => Promise<void>;
  reset: () => void;
}

export function usePaginatedData<T = any>(
  endpoint: string,
  options: UsePaginatedDataOptions = {}
): UsePaginatedDataReturn<T> {
  const {
    initialPage = 0,
    initialRowsPerPage = 10,
    initialOrder = 'desc',
    initialOrderBy = '',
    debounceDelay = 500,
    autoFetch = true,
    additionalFilters = {},
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    rowsPerPage: initialRowsPerPage,
    totalCount: 0,
    searchTerm: '',
    order: initialOrder,
    orderBy: initialOrderBy,
  });

  const debouncedSearchTerm = useDebounce(pagination.searchTerm, debounceDelay);

  const { data: apiData, loading, error, post, reset: resetApi } = useApiPost({
    showErrorAlert: false,
  });

  const fetchData = useCallback(async () => {
    if (!endpoint) return;

    const requestData = {
      page: pagination.page,
      pageSize: pagination.rowsPerPage,
      searchTerm: debouncedSearchTerm,
      sortBy: pagination.orderBy,
      sortOrder: pagination.order,
      ...additionalFilters,
    };

    const response = await post(endpoint, requestData);
    
    if (response?.success) {
      setData(response.data || []);
      setPagination(prev => ({
        ...prev,
        totalCount: response.totalCount || 0,
      }));
    } else {
      setData([]);
      setPagination(prev => ({
        ...prev,
        totalCount: 0,
      }));
    }
  }, [
    endpoint,
    pagination.page,
    pagination.rowsPerPage,
    debouncedSearchTerm,
    pagination.order,
    pagination.orderBy,
    additionalFilters,
    post,
  ]);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const reset = useCallback(() => {
    setData([]);
    setPagination({
      page: initialPage,
      rowsPerPage: initialRowsPerPage,
      totalCount: 0,
      searchTerm: '',
      order: initialOrder,
      orderBy: initialOrderBy,
    });
    resetApi();
  }, [initialPage, initialRowsPerPage, initialOrder, initialOrderBy, resetApi]);

  // Convenience setters
  const setPage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const setRowsPerPage = useCallback((rowsPerPage: number) => {
    setPagination(prev => ({ ...prev, rowsPerPage, page: 0 }));
  }, []);

  const setSearchTerm = useCallback((searchTerm: string) => {
    setPagination(prev => ({ ...prev, searchTerm, page: 0 }));
  }, []);

  const setOrder = useCallback((order: 'asc' | 'desc') => {
    setPagination(prev => ({ ...prev, order }));
  }, []);

  const setOrderBy = useCallback((orderBy: string) => {
    setPagination(prev => ({ ...prev, orderBy }));
  }, []);

  const handleSort = useCallback((property: string) => {
    const isAsc = pagination.orderBy === property && pagination.order === 'asc';
    setPagination(prev => ({
      ...prev,
      order: isAsc ? 'desc' : 'asc',
      orderBy: property,
    }));
  }, [pagination.orderBy, pagination.order]);

  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [autoFetch, fetchData]);

  return {
    data,
    loading,
    error,
    pagination,
    setPagination,
    setPage,
    setRowsPerPage,
    setSearchTerm,
    setOrder,
    setOrderBy,
    handleSort,
    refetch,
    reset,
  };
}
