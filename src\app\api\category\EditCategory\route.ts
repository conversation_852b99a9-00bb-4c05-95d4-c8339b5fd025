import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { writeFile, access, constants, unlink } from 'fs/promises'; // Added unlink here
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { mkdirSync, existsSync } from 'fs';
import sharp from 'sharp';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || '/var/www/images/categories';
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MIN_FILE_SIZE = 1024;

type SharpImageFormat = keyof sharp.FormatEnum;

export async function PUT(req: NextRequest) {
    // const user = await verifyToken(req);

    // if (!user) {
    //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    try {
        if (!existsSync(UPLOAD_DIR)) {
            mkdirSync(UPLOAD_DIR, { recursive: true });
        }
        try {
            await access(UPLOAD_DIR, constants.W_OK);
        } catch (err) {
            console.error('Upload directory is not writable:', UPLOAD_DIR);
            return NextResponse.json(
                { error: 'Server configuration error: upload directory not writable' },
                { status: 500 }
            );
        }

        const formData = await req.formData();

        const Id = formData.get('id')?.toString();
        const Name = formData.get('name')?.toString();
        const Title = formData.get('title')?.toString();
        const ShowUrlName = formData.get('showUrlName')?.toString();
        const ShortDescription = formData.get('shortDescription')?.toString();
        const imageFile = formData.get('image') as File | null;
        const Domain = formData.get('domain')?.toString();

        if (!Id) {
            return NextResponse.json(
                { error: "Missing required fields: Id is required." },
                { status: 400 }
            );
        }

        const existingCategory = await prisma.category.findFirst({
            where: {
                Id,
                Domain,
                OR: [
                    { IsDeleted: false },
                    { IsDeleted: null }
                ]
            }
        });

        if (!existingCategory) {
            return NextResponse.json(
                { error: "Category not found" },
                { status: 404 }
            );
        }

        if (ShowUrlName && ShowUrlName !== existingCategory.ShowUrlName) {
            const urlConflict = await prisma.category.findFirst({
                where: {
                    ShowUrlName,
                    Domain,
                    Id: { not: Id },
                    OR: [
                        { IsDeleted: false },
                        { IsDeleted: null }
                    ]
                }
            });

            if (urlConflict) {
                return NextResponse.json(
                    { error: "A category with this URL already exists in this domain" },
                    { status: 409 }
                );
            }
        }

        function isSharpImageFormat(format: string): format is SharpImageFormat {
            const sharpFormats: SharpImageFormat[] = ['jpeg', 'png', 'webp', 'gif', 'tiff', 'raw'];
            return sharpFormats.includes(format as SharpImageFormat);
        }

        let imagePath: string | null = null;

        if (imageFile && imageFile.size > 0) {
            try {
                const buffer = Buffer.from(await imageFile.arrayBuffer());
                let mimeType = imageFile.type;
                let originalExtension: SharpImageFormat = 'jpeg';

                if (mimeType === 'application/octet-stream') {
                    try {
                        const metadata = await sharp(buffer).metadata();
                        if (metadata.format && isSharpImageFormat(metadata.format)) {
                            mimeType = `image/${metadata.format}`;
                            originalExtension = metadata.format;
                        }
                    } catch {
                        throw new Error('Invalid image format');
                    }
                } else {
                    const ext = mimeType.split('/')[1];
                    if (ext && isSharpImageFormat(ext)) {
                        originalExtension = ext;
                    }
                }

                if (!ALLOWED_IMAGE_TYPES.includes(mimeType)) {
                    throw new Error(`Invalid file type: ${mimeType}`);
                }

                if (imageFile.size < MIN_FILE_SIZE) {
                    throw new Error(`File too small: ${imageFile.size} bytes`);
                }

                const domainData = await prisma.domain.findUnique({
                    where: { Id: Domain },
                    select: { Prefix: true }
                });
                const domainPrefix = domainData?.Prefix?.toLowerCase();

                const uploadPath = domainPrefix
                    ? path.join(UPLOAD_DIR, domainPrefix)
                    : UPLOAD_DIR;

                if (!existsSync(uploadPath)) {
                    mkdirSync(uploadPath, { recursive: true });
                }

                const uniqueId = uuidv4();
                const baseFileName = `category_${uniqueId}.${originalExtension}`;

                const [originalBuffer, thumbnailBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, 300, { fit: 'cover', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                await Promise.all([
                    writeFile(path.join(uploadPath, baseFileName), originalBuffer),
                    writeFile(path.join(uploadPath, `thumb_${uniqueId}.${originalExtension}`), thumbnailBuffer)
                ]);

                if (domainPrefix) {
                    imagePath = `${domainPrefix}/${baseFileName}`;
                } else {
                    imagePath = baseFileName;
                }

            } catch (error) {
                console.error('Error processing category image:', error);
                return NextResponse.json(
                    { error: 'Failed to process image. Please upload a valid image file.' },
                    { status: 400 }
                );
            }
        }

        const updatedCategory = await prisma.category.update({
            where: { Id },
            data: {
                Name: Name || existingCategory.Name,
                ShowUrlName: ShowUrlName,
                Title: Title || existingCategory.Title,
                Image: imagePath || existingCategory.Image,
                ShortDescription: ShortDescription !== undefined ? ShortDescription : existingCategory.ShortDescription,
                Domain: Domain || existingCategory.Domain,
            },
            select: {
                Id: true,
                Name: true,
                Title: true,
                Image: true,
                ShortDescription: true,
                ShowUrlName: true,
                Domain: true,
            },
        });

        return NextResponse.json(
            {
                success: true,
                message: 'Category updated successfully',
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error updating category:", error instanceof Error ? error.message : String(error));
        return NextResponse.json(
            { error: "Failed to update category" },
            { status: 500 }
        );
    }
}