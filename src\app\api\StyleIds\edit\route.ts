import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function PUT(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { Id, StyleId, Name, AssignUsers } = await req.json();

        if (!Id) {
            return NextResponse.json(
                { error: "Id is required for updating a style" },
                { status: 400 }
            );
        }

        // Prepare update data
        const updateData: any = {
            UpdatedAt: new Date()
        };
        if (StyleId !== undefined) updateData.StyleId = StyleId;
        if (Name !== undefined) updateData.Name = Name;

        // Check if StyleId is being changed to a value that already exists
        if (StyleId !== undefined) {
            const existingStyleWithSameId = await prisma.styleIds.findFirst({
                where: {
                    StyleId,
                    Id: { not: Id } // Exclude current style
                }
            });

            if (existingStyleWithSameId) {
                return NextResponse.json(
                    { error: "Another style already uses this StyleId" },
                    { status: 409 }
                );
            }
        }

        // Validate AssignUsers if provided (can be empty array)
        if (AssignUsers !== undefined) {
            if (!Array.isArray(AssignUsers)) {
                return NextResponse.json(
                    { error: "AssignUsers must be an array" },
                    { status: 400 }
                );
            }

            // Validate user IDs if array is not empty
            if (AssignUsers.length > 0) {
                const existingUsers = await prisma.adminUser.findMany({
                    where: {
                        Id: { in: AssignUsers },
                        IsDeleted: false
                    },
                    select: { Id: true }
                });

                if (existingUsers.length !== AssignUsers.length) {
                    const invalidUserIds = AssignUsers.filter(id =>
                        !existingUsers.some(user => user.Id === id)
                    );
                    return NextResponse.json(
                        {
                            error: "One or more user IDs are invalid",
                            invalidUserIds
                        },
                        { status: 400 }
                    );
                }
            }
        }

        const result = await prisma.$transaction(async (prismaTransaction) => {
            // Update the style
            const updatedStyle = await prismaTransaction.styleIds.update({
                where: { Id },
                data: updateData,
                select: {
                    Id: true,
                    StyleId: true,
                    Name: true
                }
            });

            let mappingsCount = 0;
            if (AssignUsers !== undefined) {
                await prismaTransaction.styleIdUserMappings.deleteMany({
                    where: { StyleId: Id }
                });

                // if (AssignUsers.length > 0) {
                //     await prismaTransaction.styleIdUserMappings.createMany({
                //         data: AssignUsers.map(userId => ({
                //             StyleId: Id,
                //             UserId: userId
                //         }))
                //     });
                //     mappingsCount = AssignUsers.length;
                // }

                if (AssignUsers.length > 0) {
                    for (const userId of AssignUsers) {
                        await prismaTransaction.styleIdUserMappings.createMany({
                            data: {
                                StyleId: Id,
                                UserId: userId
                            }
                        });
                    }
                }
            }

            return {
                style: updatedStyle,
                mappingsCount
            };
        });

        return NextResponse.json({
            success: true,
            message: AssignUsers !== undefined
                ? "Style and user mappings updated successfully"
                : "Style updated successfully (user mappings unchanged)",
            data: {
                ...result.style,
                userCount: result.mappingsCount
            }
        }, { status: 200 });

    } catch (error) {
        console.error("Error updating style:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } 
}