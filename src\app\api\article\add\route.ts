// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
// import sharp from 'sharp';
// import { v4 as uuidv4 } from 'uuid';
// import fs from 'fs';
// import path from 'path';

// const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

// function serializeBigInt(obj: any): any {
//     if (obj === null || obj === undefined) return obj;
//     if (typeof obj === 'bigint') return obj.toString();
//     if (Array.isArray(obj)) return obj.map(serializeBigInt);
//     if (typeof obj === 'object') {
//         const serialized: any = {};
//         for (const key in obj) {
//             if (obj.hasOwnProperty(key)) {
//                 serialized[key] = serializeBigInt(obj[key]);
//             }
//         }
//         return serialized;
//     }
//     return obj;
// }

// async function checkExistingCombinations(customChannel: string | null, styleIdLm: string | null, styleIdDm: string | null) {
//     const existingCombinations = [];

//     if (customChannel && styleIdLm) {
//         // Check: CustomChannel + StyleIdLm
//         const existingLm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdLm: styleIdLm,
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdLm: true,
//                 StyleIdDm: true
//             }
//         });

//         if (existingLm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdLm',
//                 combination: { customChannel, styleId: styleIdLm },
//                 existingArticles: existingLm.map(a => ({
//                     id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
//                 }))
//             });
//         }

//         const crossLmAsDm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdDm: styleIdLm,
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdLm: true,
//                 StyleIdDm: true
//             }
//         });

//         if (crossLmAsDm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdDm (from styleIdLm)',
//                 combination: { customChannel, styleId: styleIdLm },
//                 existingArticles: crossLmAsDm.map(a => ({
//                     id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
//                 }))
//             });
//         }
//     }

//     if (customChannel && styleIdDm) {
//         // Check: CustomChannel + StyleIdDm
//         const existingDm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdDm: styleIdDm,
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdDm: true,
//                 StyleIdLm: true
//             }
//         });

//         if (existingDm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdDm',
//                 combination: { customChannel, styleId: styleIdDm },
//                 existingArticles: existingDm.map(a => ({
//                     id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
//                 }))
//             });
//         }

//         // Cross-check: CustomChannel + styleIdDm as StyleIdLm
//         const crossDmAsLm = await prisma.articleDetails.findMany({
//             where: {
//                 CustomChannal: customChannel,
//                 StyleIdLm: styleIdDm,
//                 IsDeleted: false
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 CustomChannal: true,
//                 StyleIdLm: true,
//                 StyleIdDm: true
//             }
//         });

//         if (crossDmAsLm.length > 0) {
//             existingCombinations.push({
//                 type: 'CustomChannel + StyleIdLm (from styleIdDm)',
//                 combination: { customChannel, styleId: styleIdDm },
//                 existingArticles: crossDmAsLm.map(a => ({
//                     id: a.Id, title: a.Title, customChannelId: a.CustomChannal, styleIdLm: a.StyleIdLm, styleIdDm: a.StyleIdDm
//                 }))
//             });
//         }
//     }

//     return existingCombinations;
// }

// export async function POST(req: NextRequest) {
//     const user = await verifyToken(req) as { Id: string };
//     if (!user) {
//         return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//     }

//     try {
//         const formData = await req.formData();
//         const textFields = {
//             Title: formData.get('Title') as string,
//             Category: formData.get('Category') as string,
//             Url: formData.get('Url') as string | null,
//             Description: formData.get('Description') as string | null,
//             ShortName: formData.get('ShortName') as string | null,
//             Published: formData.get('Published') === 'true',
//             ShowArticle: formData.get('ShowArticle') === 'true',
//             ShowsAds: formData.get('ShowsAds') === 'true',
//             MetaTitle: formData.get('MetaTitle') as string | null,
//             MetaDescription: formData.get('MetaDescription') as string | null,
//             MetaKeys: formData.get('MetaKeys') as string || '',
//             CustomChannal: formData.get('CustomChannal') as string | null,
//             StyleIdLm: formData.get('StyleIdLm') as string | null,
//             StyleIdDm: formData.get('StyleIdDm') as string | null,
//             AdRelatedSearches: formData.get('AdRelatedSearches') as string | null,
//             Remark: formData.get('Remark') as string || '',
//             ShortDescription: formData.get('ShortDescription') as string | null,
//             Domain: formData.get('Domain') as string,
//             SubDomain: formData.get('SubDomain') as string | null,
//             CampaignIds: JSON.parse(formData.get('CampaignIds') as string || '[]') as number[],
//             User_Id_Settings: user.Id,
//             ReadTime: formData.get('ReadTime') ? parseInt(formData.get('ReadTime') as string) : null,
//             CheckRelatedSearches2: formData.get('CheckRelatedSearches2') === 'true',
//             AdReletadSearches2: formData.get('AdReletadSearches2') as string | null
//         };

//         const isValidUUID = (uuid: string): boolean => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid);
//         const validationErrors: string[] = [];

//         if (!textFields.Title?.trim()) validationErrors.push('Title is required and cannot be empty');
//         if (!textFields.Category || !isValidUUID(textFields.Category)) validationErrors.push('Category must be a valid UUID');
//         if (!textFields.Domain || !isValidUUID(textFields.Domain)) validationErrors.push('Domain must be a valid UUID');
//         if (textFields.SubDomain && !isValidUUID(textFields.SubDomain)) validationErrors.push('SubDomain must be a valid UUID');
//         if (textFields.CustomChannal && !isValidUUID(textFields.CustomChannal)) validationErrors.push('CustomChannal must be a valid UUID');
//         if (textFields.StyleIdLm && !isValidUUID(textFields.StyleIdLm)) validationErrors.push('StyleIdLm must be a valid UUID');
//         if (textFields.StyleIdDm && !isValidUUID(textFields.StyleIdDm)) validationErrors.push('StyleIdDm must be a valid UUID');
//         if (textFields.Title && textFields.Title.length > 255) validationErrors.push('Title must be less than 255 characters');
//         if (textFields.MetaTitle && textFields.MetaTitle.length > 255) validationErrors.push('MetaTitle must be less than 255 characters');
//         if (textFields.MetaDescription && textFields.MetaDescription.length > 500) validationErrors.push('MetaDescription must be less than 500 characters');

//         if (validationErrors.length > 0) {
//             return NextResponse.json({ error: "Validation failed", details: validationErrors }, { status: 400 });
//         }

//         const existingCombinations = await checkExistingCombinations(
//             textFields.CustomChannal,
//             textFields.StyleIdLm,
//             textFields.StyleIdDm
//         );

//         if (existingCombinations.length > 0) {
//             const messages = existingCombinations.map(combo => {
//                 const titles = combo.existingArticles.map(a => a.title).filter(Boolean).join(', ');
//                 return `Combination of ${combo.type} already exists for: ${titles}`;
//             });

//             return NextResponse.json({ error: "Combination already exists", message: messages.join(' | ') }, { status: 409 });
//         }

//         const existing = await prisma.articleDetails.findFirst({
//             where: {
//                 Title: textFields.Title,
//                 Domain: textFields.Domain,
//                 // SubDomain: textFields.SubDomain,
//                 IsDeleted: false,
//             }
//         });

//         if (existing) {
//             return NextResponse.json({ error: "Article title already exists for this domain" }, { status: 409 });
//         }

//         const existingShortName = await prisma.articleDetails.findFirst({
//             where: {
//                 ShortName: textFields.ShortName,
//                 // Domain: textFields.Domain,
//                 // SubDomain: textFields.SubDomain,
//                 IsDeleted: false,
//             }
//         });

//         if (existingShortName) {
//             return NextResponse.json({ error: "Article ShortName already exists for another Article" }, { status: 409 });
//         }

//         const file = formData.get('file') as File | null;
//         let imagePath: string | null = null;

//         if (file) {
//             try {
//                 const buffer = Buffer.from(await file.arrayBuffer());
//                 const extension = (file.type.split('/')[1] || 'jpeg') as keyof sharp.FormatEnum;
//                 const uniqueId = uuidv4();

//                 if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR, { recursive: true });

//                 const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
//                     sharp(buffer).toBuffer(),
//                     sharp(buffer).resize(300).toFormat(extension, { quality: 80 }).toBuffer(),
//                     sharp(buffer).resize(720).toFormat(extension, { quality: 80 }).toBuffer()
//                 ]);

//                 const baseFileName = `${uniqueId}.${extension}`;
//                 const versions = [
//                     { suffix: '', buffer: originalBuffer },
//                     { suffix: '_small', buffer: smallBuffer },
//                     { suffix: '_medium', buffer: mediumBuffer }
//                 ];

//                 await Promise.all(versions.map(({ suffix, buffer }) =>
//                     fs.promises.writeFile(path.resolve(UPLOAD_DIR, `${uniqueId}${suffix}.${extension}`), buffer)
//                 ));

//                 imagePath = baseFileName;
//             } catch (error) {
//                 console.error("Image processing error:", error);
//                 return NextResponse.json({ error: "Failed to process image" }, { status: 500 });
//             }
//         }

//         const ShowUrlName = textFields.Title.replace(/[^a-zA-Z0-9]+/g, "-").toLowerCase().replace(/^-+|-+$/g, '');
//         const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
//             ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
//             : [];

//         const articleData: any = {
//             Title: textFields.Title.trim(),
//             Url: textFields.Url || null,
//             Description: textFields.Description || null,
//             Published: textFields.Published,
//             ShowArticle: textFields.ShowArticle,
//             ShowsAds: textFields.ShowsAds,
//             MetaTitle: textFields.MetaTitle || null,
//             MetaDescription: textFields.MetaDescription || null,
//             MetaKeys: textFields.MetaKeys.trim(),
//             AdRelatedSearches: textFields.AdRelatedSearches || null,
//             Remark: textFields.Remark.trim(),
//             Image: imagePath,
//             ShowUrlName,
//             ShortDescription: textFields.ShortDescription || null,
//             ReadTime: textFields.ReadTime,
//             AdReletadSearches2: textFields.AdReletadSearches2 || null,
//             CheckRelatedSearches2: textFields.CheckRelatedSearches2,
//             IsDeleted: false,
//             CreatedAt: new Date(),
//             UpdatedAt: new Date(),
//             Category_ArticleDetails_CategoryToCategory: { connect: { Id: textFields.Category } },
//             Domain_ArticleDetails_DomainToDomain: { connect: { Id: textFields.Domain } },
//             AdminUser: { connect: { Id: textFields.User_Id_Settings } }
//         };

//         if (textFields.SubDomain) {
//             articleData.SubDomain_ArticleDetails_SubDomainToSubDomain = { connect: { Id: textFields.SubDomain } };
//         }

//         if (textFields.CustomChannal) {
//             articleData.Channals = { connect: { Id: textFields.CustomChannal } };
//         }

//         if (textFields.StyleIdLm) {
//             articleData.StyleIds_ArticleDetails_StyleIdLmToStyleIds = { connect: { Id: textFields.StyleIdLm } };
//         }

//         if (textFields.StyleIdDm) {
//             articleData.StyleIds_ArticleDetails_StyleIdDmToStyleIds = { connect: { Id: textFields.StyleIdDm } };
//         }

//         const newArticle = await prisma.articleDetails.create({ data: articleData });

//         if (normalizedCampaignIds.length > 0) {
//             await prisma.articleCampaignMappings.createMany({
//                 data: normalizedCampaignIds.map(id => ({
//                     ArticleId: newArticle.Id,
//                     CampaignId: id,
//                     CreatedAt: new Date()
//                 })),
//                 skipDuplicates: true
//             });
//         }

//         const articleWithRelations = await prisma.articleDetails.findUnique({
//             where: { Id: newArticle.Id },
//         });

//         return NextResponse.json({
//             success: true,
//             message: "Article created successfully",
//             article: serializeBigInt(articleWithRelations),
//             campaignMappings: normalizedCampaignIds.length
//         }, { status: 201 });

//     } catch (error) {
//         console.error("Error in article creation:", error);
//         return NextResponse.json({
//             error: "Failed to create article",
//             details: error instanceof Error ? error.message : String(error)
//         }, { status: 500 });
//     } 
// }



import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import Busboy from 'busboy';
import { sendKafkaEvent } from '@/lib/kafka';

const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

// Helper function to serialize BigInt values for JSON response
function serializeBigInt(obj: any): any {
    if (obj === null || obj === undefined) return obj;
    if (typeof obj === 'bigint') return obj.toString();
    if (Array.isArray(obj)) return obj.map(serializeBigInt);
    if (typeof obj === 'object') {
        const serialized: any = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                serialized[key] = serializeBigInt(obj[key]);
            }
        }
        return serialized;
    }
    return obj;
}

// Validates UUID format
function isValidUUID(uuid: string): boolean {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid);
}

// Checks for existing article combinations
async function checkExistingCombinations(
    customChannel: string | null,
    styleIdLm: string | null,
    styleIdDm: string | null
) {
    const existingCombinations = [];

    // Check CustomChannel + StyleIdLm combination
    if (customChannel && styleIdLm) {
        const existingLm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdLm: styleIdLm,
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdLm: true,
                StyleIdDm: true
            }
        });

        if (existingLm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdLm',
                combination: { customChannel, styleId: styleIdLm },
                existingArticles: existingLm.map(a => ({
                    id: a.Id,
                    title: a.Title,
                    customChannelId: a.CustomChannal,
                    styleIdLm: a.StyleIdLm,
                    styleIdDm: a.StyleIdDm
                }))
            });
        }

        // Cross-check with StyleIdDm
        const crossLmAsDm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdDm: styleIdLm,
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdLm: true,
                StyleIdDm: true
            }
        });

        if (crossLmAsDm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdDm (from styleIdLm)',
                combination: { customChannel, styleId: styleIdLm },
                existingArticles: crossLmAsDm.map(a => ({
                    id: a.Id,
                    title: a.Title,
                    customChannelId: a.CustomChannal,
                    styleIdLm: a.StyleIdLm,
                    styleIdDm: a.StyleIdDm
                }))
            });
        }
    }

    // Check CustomChannel + StyleIdDm combination
    if (customChannel && styleIdDm) {
        const existingDm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdDm: styleIdDm,
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdDm: true,
                StyleIdLm: true
            }
        });

        if (existingDm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdDm',
                combination: { customChannel, styleId: styleIdDm },
                existingArticles: existingDm.map(a => ({
                    id: a.Id,
                    title: a.Title,
                    customChannelId: a.CustomChannal,
                    styleIdLm: a.StyleIdLm,
                    styleIdDm: a.StyleIdDm
                }))
            });
        }

        // Cross-check with StyleIdLm
        const crossDmAsLm = await prisma.articleDetails.findMany({
            where: {
                CustomChannal: customChannel,
                StyleIdLm: styleIdDm,
                IsDeleted: false
            },
            select: {
                Id: true,
                Title: true,
                CustomChannal: true,
                StyleIdLm: true,
                StyleIdDm: true
            }
        });

        if (crossDmAsLm.length > 0) {
            existingCombinations.push({
                type: 'CustomChannel + StyleIdLm (from styleIdDm)',
                combination: { customChannel, styleId: styleIdDm },
                existingArticles: crossDmAsLm.map(a => ({
                    id: a.Id,
                    title: a.Title,
                    customChannelId: a.CustomChannal,
                    styleIdLm: a.StyleIdLm,
                    styleIdDm: a.StyleIdDm
                }))
            });
        }
    }

    return existingCombinations;
}

export async function POST(req: NextRequest) {

    const userId = req.headers.get('x-user-id') as string;
    // const user = await verifyToken(req) as { Id: string };
    // if (!user) {
    //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    try {
        const contentType = req.headers.get('content-type');
        if (!contentType || !contentType.includes('multipart/form-data')) {
            return NextResponse.json({ error: 'Invalid content type' }, { status: 400 });
        }

        // Initialize Busboy for form parsing
        const formData: Record<string, any> = {};
        const fileBuffers: { field: string; buffer: Buffer; filename: string; mimeType: string }[] = [];

        const busboy = Busboy({ headers: { 'content-type': contentType } });

        // Process form fields
        busboy.on('field', (fieldname, val) => {
            formData[fieldname] = val;
        });

        // Process files
        busboy.on('file', (fieldname, file, info) => {
            const chunks: Buffer[] = [];
            const { filename, mimeType } = info;

            file.on('data', (data) => {
                chunks.push(data);
            });

            file.on('end', () => {
                if (chunks.length > 0) {
                    fileBuffers.push({
                        field: fieldname,
                        buffer: Buffer.concat(chunks),
                        filename: filename || '',
                        mimeType
                    });
                }
            });
        });

        // Wait for parsing to complete
        const busboyFinished = new Promise((resolve, reject) => {
            busboy.on('finish', resolve);
            busboy.on('error', reject);
        });

        // Pipe the request body to busboy
        const reqBody = await req.blob();
        const reqBuffer = Buffer.from(await reqBody.arrayBuffer());
        busboy.end(reqBuffer);

        await busboyFinished;

        // Extract and validate form data
        const textFields = {
            Title: formData.Title as string,
            Category: formData.Category as string,
            Url: formData.Url as string | null,
            Description: formData.Description as string | null,
            ShortName: formData.ShortName as string | null,
            Published: formData.Published === 'true',
            ShowArticle: formData.ShowArticle === 'true',
            ShowsAds: formData.ShowsAds === 'true',
            ShowInDomain: formData.ShowInDomain === 'true',
            MetaTitle: formData.MetaTitle as string | null,
            MetaDescription: formData.MetaDescription as string | null,
            MetaKeys: formData.MetaKeys as string || '',
            CustomChannal: formData.CustomChannal as string | null,
            StyleIdLm: formData.StyleIdLm as string | null,
            StyleIdDm: formData.StyleIdDm as string | null,
            AdRelatedSearches: formData.AdRelatedSearches as string | null,
            Remark: formData.Remark as string || '',
            ShortDescription: formData.ShortDescription as string | null,
            Domain: formData.Domain as string,
            SubDomain: formData.SubDomain as string | null,
            CampaignIds: JSON.parse(formData.CampaignIds as string || '[]') as number[],
            User_Id_Settings: userId,
            ReadTime: formData.ReadTime ? parseInt(formData.ReadTime as string) : null,
            CheckRelatedSearches2: formData.CheckRelatedSearches2 === 'true',
            AdReletadSearches2: formData.AdReletadSearches2 as string | null
        };

        // Validate input fields
        const validationErrors: string[] = [];

        if (!textFields.Title?.trim()) validationErrors.push('Title is required');
        if (!textFields.Category || !isValidUUID(textFields.Category)) validationErrors.push('Invalid Category UUID');
        if (!textFields.Domain || !isValidUUID(textFields.Domain)) validationErrors.push('Invalid Domain UUID');
        if (textFields.SubDomain && !isValidUUID(textFields.SubDomain)) validationErrors.push('Invalid SubDomain UUID');
        if (textFields.CustomChannal && !isValidUUID(textFields.CustomChannal)) validationErrors.push('Invalid CustomChannal UUID');
        if (textFields.StyleIdLm && !isValidUUID(textFields.StyleIdLm)) validationErrors.push('Invalid StyleIdLm UUID');
        if (textFields.StyleIdDm && !isValidUUID(textFields.StyleIdDm)) validationErrors.push('Invalid StyleIdDm UUID');

        if (validationErrors.length > 0) {
            return NextResponse.json({ error: "Validation failed", details: validationErrors }, { status: 400 });
        }

        // Check for existing combinations
        const existingCombinations = await checkExistingCombinations(
            textFields.CustomChannal,
            textFields.StyleIdLm,
            textFields.StyleIdDm
        );

        if (existingCombinations.length > 0) {
            const messages = existingCombinations.map(combo => {
                const titles = combo.existingArticles.map(a => a.title).filter(Boolean).join(', ');
                return `Combination of ${combo.type} already exists for: ${titles}`;
            });

            return NextResponse.json({
                error: messages.join(' | ')
            }, { status: 409 });
        }

        // Check for duplicate title
        const existingTitle = await prisma.articleDetails.findFirst({
            where: {
                Title: textFields.Title,
                Domain: textFields.Domain,
                IsDeleted: false,
                SubDomain: textFields.SubDomain && textFields.SubDomain.trim() !== '' ? textFields.SubDomain : null
            }
        });

        if (existingTitle) {
            return NextResponse.json({
                error: "Article title already exists for this domain"
            }, { status: 409 });
        }

        // Check for duplicate short name
        if (textFields.ShortName) {
            const existingShortName = await prisma.articleDetails.findFirst({
                where: {
                    ShortName: textFields.ShortName,
                    IsDeleted: false,
                }
            });

            if (existingShortName) {
                return NextResponse.json({
                    error: "Article ShortName already exists"
                }, { status: 409 });
            }
        }

        let imagePath: string | null = null;
        const fileData = fileBuffers.find(f => f.field === 'file');

        if (fileData) {
            try {
                const buffer = fileData.buffer;
                const extension = (fileData.mimeType.split('/')[1] || 'jpeg') as keyof sharp.FormatEnum;
                const uniqueId = uuidv4();

                const domain = await prisma.domain.findUnique({
                    where: { Id: textFields.Domain },
                    select: { Prefix: true }
                });

                const domainPrefix = domain?.Prefix?.toLowerCase();

                const uploadPath = domainPrefix
                    ? path.resolve(UPLOAD_DIR, domainPrefix)
                    : UPLOAD_DIR;

                if (!fs.existsSync(uploadPath)) {
                    fs.mkdirSync(uploadPath, { recursive: true });
                }

                const originalBuffer = await sharp(buffer).toBuffer();

                const baseFileName = `${uniqueId}.${extension}`;

                await fs.promises.writeFile(
                    path.resolve(uploadPath, baseFileName),
                    originalBuffer
                );

                if (domainPrefix) {
                    imagePath = `${domainPrefix}/${baseFileName}`;
                } else {
                    imagePath = baseFileName;
                }

            } catch (error) {
                console.error("Image processing error:", error);
                return NextResponse.json({
                    error: "Failed to process image"
                }, { status: 500 });
            }
        }

        // Generate URL-friendly name
        const ShowUrlName = textFields.Title
            .replace(/[^a-zA-Z0-9]+/g, "-")
            .toLowerCase()
            .replace(/^-+|-+$/g, '');

        // Normalize campaign IDs
        const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
            ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
            : [];

        // Prepare article data
        const articleData: any = {
            Title: textFields.Title.trim(),
            Url: textFields.Url || null,
            Description: textFields.Description || null,
            Published: textFields.Published,
            ShowArticle: textFields.ShowArticle,
            ShowsAds: textFields.ShowsAds,
            ShowInDomain: textFields.ShowInDomain,
            MetaTitle: textFields.MetaTitle || null,
            MetaDescription: textFields.MetaDescription || null,
            MetaKeys: textFields.MetaKeys.trim(),
            AdRelatedSearches: textFields.AdRelatedSearches || null,
            Remark: textFields.Remark.trim(),
            Image: imagePath,
            ShowUrlName,
            ShortDescription: textFields.ShortDescription || null,
            ReadTime: textFields.ReadTime,
            AdReletadSearches2: textFields.AdReletadSearches2 || null,
            CheckRelatedSearches2: textFields.CheckRelatedSearches2,
            IsDeleted: false,
            CreatedAt: new Date(),
            UpdatedAt: new Date(),
            Category_ArticleDetails_CategoryToCategory: { connect: { Id: textFields.Category } },
            Domain_ArticleDetails_DomainToDomain: { connect: { Id: textFields.Domain } },
            AdminUser: { connect: { Id: textFields.User_Id_Settings } }
        };

        // Optional relationships
        if (textFields.SubDomain) {
            articleData.SubDomain_ArticleDetails_SubDomainToSubDomain = {
                connect: { Id: textFields.SubDomain }
            };
        }

        if (textFields.CustomChannal) {
            articleData.Channals = {
                connect: { Id: textFields.CustomChannal }
            };
        }

        if (textFields.StyleIdLm) {
            articleData.StyleIds_ArticleDetails_StyleIdLmToStyleIds = {
                connect: { Id: textFields.StyleIdLm }
            };
        }

        if (textFields.StyleIdDm) {
            articleData.StyleIds_ArticleDetails_StyleIdDmToStyleIds = {
                connect: { Id: textFields.StyleIdDm }
            };
        }

        // Create article
        const newArticle = await prisma.articleDetails.create({
            data: articleData
        });

        // Create campaign mappings if needed
        if (normalizedCampaignIds.length > 0) {
            await prisma.articleCampaignMappings.createMany({
                data: normalizedCampaignIds.map(id => ({
                    ArticleId: newArticle.Id,
                    CampaignId: id,
                    CreatedAt: new Date()
                })),
                skipDuplicates: true
            });
        }

        // Fetch complete article data
        const articleWithRelations = await prisma.articleDetails.findUnique({
            where: { Id: newArticle.Id },
        });

        try {

            const payload = {
                'Type': "articleDetails",
                'Id': newArticle.Id,
            };

            await sendKafkaEvent(payload);
        } catch (kafkaError) {
            console.error('Failed to send Kafka event:', kafkaError);

        }

        return NextResponse.json({
            success: true,
            message: "Article created successfully",
            article: serializeBigInt(articleWithRelations),
            campaignMappings: normalizedCampaignIds.length
        }, { status: 201 });

    } catch (error) {
        console.error("Error in article creation:", error);
        return NextResponse.json({
            error: "Failed to create article",
            details: error instanceof Error ? error.message : String(error)
        }, { status: 500 });
    }
}