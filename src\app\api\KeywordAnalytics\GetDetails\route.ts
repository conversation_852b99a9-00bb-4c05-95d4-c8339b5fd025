// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

// export async function GET(req: NextRequest) {
//     try {
//         // const user = await verifyToken(req);
//         // if (!user) {
//         //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         // }

//         const { searchParams } = new URL(req.url);
//         const start = parseInt(searchParams.get("page") || "1");
//         const length = parseInt(searchParams.get("length") || "10");
//         const searchRaw = searchParams.get("q");
//         const search = searchRaw ? searchRaw.trim() : null;
//         const orderBy = searchParams.get("orderBy") || "ClickCount";
//         const orderDir = searchParams.get("orderDir") || "asc";
//         const startDate = searchParams.get("startDate");
//         const endDate = searchParams.get("endDate");
//         if (!startDate || !endDate) {
//             throw new Error(" startDate and endDate are required.");
//         }

//         let whereCondition: any = {};

//         if (startDate && endDate) {
//             const startDateTime = new Date(startDate + 'T00:00:00.000Z');
//             const endDateTime = new Date(endDate + 'T23:59:59.999Z');

//             whereCondition.Created_At = {
//                 gte: startDateTime,
//                 lte: endDateTime
//             };
//         }

//         if (search && search !== "") {
//             whereCondition.OR = [
//                 { URL: { contains: search, mode: 'insensitive' } },
//                 { Keyword: { contains: search, mode: 'insensitive' } },
//                 { IpAddress: { contains: search, mode: 'insensitive' } },
//                 { Domain: { contains: search, mode: 'insensitive' } }
//             ];
//         }

//         const aggregatedData = await prisma.queryAnalytics.groupBy({
//             by: ['URL', 'Domain'],
//             _sum: {
//                 Count: true,
//                 AdsClickCounter: true
//             },
//             where: whereCondition,
//             orderBy: orderBy === 'ClickCount' ? { _sum: { Count: orderDir as 'asc' | 'desc' } } :
//                 orderBy === 'AdsClickCount' ? { _sum: { AdsClickCounter: orderDir as 'asc' | 'desc' } } :
//                     orderBy === 'URL' ? { URL: orderDir as 'asc' | 'desc' } :
//                         { Domain: orderDir as 'asc' | 'desc' }
//         });

//         const transformedData = aggregatedData.map(item => {
//             const clickCount = item._sum.Count || 0;
//             const adsClickCount = item._sum.AdsClickCounter || 0;
//             const adsPerCount = clickCount > 0 ? (adsClickCount / clickCount) * 100 : 0;

//             return {
//                 ClickCount: clickCount,
//                 AdsClickCount: adsClickCount,
//                 AdsPerCount: parseFloat(adsPerCount.toFixed(2)), 
//                 URL: item.URL,
//                 Domain: item.Domain,
//                 StartDate: startDate,
//                 EndDate: endDate
//             };
//         });

//         const totalRecords = transformedData.length;

//         let paginatedData = transformedData;
//         let skip: number | undefined;
//         let limit: number | undefined;

//         // if (length !== -1) {
//         //     const skipCount = (start === 1 ? 0 : start - 1) * length;
//         //     skip = skipCount > 0 ? skipCount : 0;
//         //     limit = length;
//         //     paginatedData = transformedData.slice(skip, skip + limit);
//         // }

//         const totalPages = length === -1 ? 1 : Math.ceil(totalRecords / length);

//         return NextResponse.json({
//             success: true,
//             data: paginatedData
//         });

//     } catch (error) {
//         console.error("Error processing request:", error);
//         return NextResponse.json(
//             {
//                 error: "Internal Server Error",
//                 details: error instanceof Error ? error.message : String(error)
//             },
//             { status: 500 }
//         );
//     } 
// }



import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';
import { prisma } from '../../../../lib/prisma';

interface TransformedDataItem {
    ClickCount: number;
    AdsClickCount: number;
    AdsPerCount: number;
    URL: string;
    Domain: string;
    StartDate: string;
    EndDate: string;
}

export async function GET(req: NextRequest) {
    const client = new Client({
        connectionString: process.env.DATABASE_URL_API,
        ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    });

    try {
        await client.connect();
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;

        const { searchParams } = new URL(req.url);
        const orderBy = searchParams.get('orderBy') || 'Impression';
        const orderDir = (searchParams.get('orderDir') || 'desc').toLowerCase();
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        if (!startDate || !endDate) {
            throw new Error(" startDate and endDate are required.");
        }

        let showUrlNames: string[] = [];

        if (role === 'Partner' || role === 'Account') {
            const userWiseSubdomains = await prisma.subDomainUserMappings.findMany({
                where: {
                    UserId: userId,
                },
                select: { SubDomainId: true },
            });

            const subDomainIds = userWiseSubdomains
                .map((item) => item.SubDomainId)
                .filter((id): id is string => id !== null);

            if (!subDomainIds.length) {
                return NextResponse.json({ success: true, data: [] }, { status: 200 });
            }

            const subDomains = await prisma.subDomain.findMany({
                where: {
                    Id: { in: subDomainIds },
                    IsDeleted: false,
                },
                select: { Id: true, Url: true, Domain: true },
            });

            const domainIds = subDomains
                .map((item) => item.Domain)
                .filter((id): id is string => id !== null);

            if (!domainIds.length) {
                return NextResponse.json({ success: true, data: [] }, { status: 200 });
            }

            const domains = await prisma.domain.findMany({
                where: { Id: { in: domainIds }, IsDeleted: false },
                select: { Id: true, ShowUrlName: true },
            });

            showUrlNames = subDomains
                .map((subDomain) => {
                    const domain = domains.find((d) => d.Id === subDomain.Domain);
                    return domain ? `${subDomain.Url}.${domain.ShowUrlName}` : null;
                })
                .filter((name): name is string => name !== null);

            if (!showUrlNames.length) {
                return NextResponse.json({ success: true, data: [] }, { status: 200 });
            }
        } else {
            const domains = await prisma.domain.findMany({
                where: { IsDeleted: false },
                select: { Id: true, ShowUrlName: true },
            });

            showUrlNames = domains
                .map((item) => item.ShowUrlName)
                .filter((name): name is string => name !== null);

            if (!showUrlNames.length) {
                return NextResponse.json({ success: true, data: [] }, { status: 200 });
            }
        }

        await client.query('BEGIN');
        await client.query('SET TRANSACTION ISOLATION LEVEL READ COMMITTED');

        let query: string;
        let queryParams: any[] = [];

        const whereConditions: string[] = [];

        if (role === 'Partner' || role === 'Account') {
            whereConditions.push(`"Domain" = ANY($${whereConditions.length + 1})`);
            queryParams.push(showUrlNames);
        } else {
            const domainConditions = showUrlNames.map((_, index) =>
                `"Domain" LIKE '%' || $${whereConditions.length + index + 1}`
            );
            whereConditions.push(`(${domainConditions.join(' OR ')})`);
            queryParams.push(...showUrlNames);
        }

        if (startDate && endDate) {
            const startDateTime = new Date(startDate + 'T00:00:00.000Z').toISOString();
            const endDateTime = new Date(endDate + 'T23:59:59.999Z').toISOString();

            whereConditions.push(`"Created_At_Date" >= $${queryParams.length + 1}`);
            whereConditions.push(`"Created_At_Date" <= $${queryParams.length + 2}`);
            queryParams.push(startDateTime, endDateTime);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const orderByColumn = getOrderByColumn(orderBy);

        query = `
            SELECT 
                "URL",
                "Domain",
                SUM("Count_Sum") AS "ClickCount",
                SUM("AdsClickCounter_Sum") AS "AdsClickCount"
            FROM vw_cleaned_query_analytics
            ${whereClause}
            GROUP BY "URL", "Domain"
            ORDER BY ${orderByColumn} ${orderDir}
            LIMIT 100
        `;

        const result = await client.query(query, queryParams);
        await client.query('COMMIT');

        const transformedData: TransformedDataItem[] = result.rows.map((item: any) => {
            const clickCount = Number(item.ClickCount) || 0;
            const adsClickCount = Number(item.AdsClickCount) || 0;
            const adsPerCount = clickCount > 0 ? (adsClickCount / clickCount) * 100 : 0;

            return {
                ClickCount: clickCount,
                AdsClickCount: adsClickCount,
                AdsPerCount: parseFloat(adsPerCount.toFixed(2)),
                URL: item.URL,
                Domain: item.Domain,
                StartDate: startDate,
                EndDate: endDate
            }
        });

        if (orderBy === 'ClickCount') {
            transformedData.sort((a: TransformedDataItem, b: TransformedDataItem) => {
                const aIsHighVolume = a.ClickCount >= 50;
                const bIsHighVolume = b.ClickCount >= 50;

                if (aIsHighVolume && bIsHighVolume) {
                    return b.AdsPerCount - a.AdsPerCount;
                }
                else if (aIsHighVolume) {
                    return -1;
                }
                else if (bIsHighVolume) {
                    return 1;
                }
                else {
                    return orderDir === 'asc' ? a.ClickCount - b.ClickCount : b.ClickCount - a.ClickCount;
                }
            });
        }

        return NextResponse.json({ success: true, data: transformedData }, { status: 200 });
    } catch (error) {
        console.error('❌ Error fetching referral data:', error);
        try {
            await client.query('ROLLBACK');
        } catch (rollbackError) {
            console.error('❌ Failed to rollback transaction:', rollbackError);
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return NextResponse.json({ success: false, error: 'Internal Server Error', details: errorMessage }, { status: 500 });
    } finally {
        try {
            await client.end();
        } catch (endError) {
            console.error('❌ Failed to close database connection:', endError);
        }
    }
}

function getOrderByColumn(orderBy: string): string {
    switch (orderBy) {
        case 'ClickCount':
            return 'SUM("Count_Sum")';
        case 'AdsClickCount':
            return 'SUM("AdsClickCounter_Sum")';
        case 'URL':
            return '"URL"';
        case 'Domain':
            return '"Domain"';
        case 'Impression':
        default:
            return 'SUM("Count_Sum")';
    }
}