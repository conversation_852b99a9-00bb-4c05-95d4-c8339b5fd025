# Modular Components Guide

This guide documents the new modular and reusable components created to improve code reusability and maintainability in the blog admin application.

## 🎯 Overview

The refactoring introduces several categories of reusable components:

1. **UI Components** - Reusable interface elements
2. **Custom Hooks** - Data fetching and form management
3. **Constants & Types** - Shared configuration and type definitions
4. **Layout Components** - Page structure and containers

## 📦 New Components

### UI Components (`src/components/ui/`)

#### Modal Components
- **BaseModal** - Foundation modal component
- **FormModal** - Modal with form handling capabilities
- **ConfirmationModal** - Modal for confirmation dialogs

#### Table Components
- **DataTableWithActions** - Enhanced data table with action buttons
- **SmartDataTable** - Intelligent table with built-in API integration

#### Layout Components
- **PageContainer** - Standardized page layout with header
- **ContentCard** - Reusable content container
- **ActionBar** - Flexible action button container

#### Utility Components
- **LoadingSpinner** - Configurable loading indicator
- **EmptyState** - Empty state placeholder

### Custom Hooks (`src/hooks/`)

#### Data Fetching Hooks
- **useApi** - Generic API request hook
- **useApiGet/Post/Put/Delete** - HTTP method-specific hooks
- **useDropdownData** - Hook for dropdown data fetching
- **usePaginatedData** - Hook for paginated table data

#### Form Management Hooks
- **useForm** - Comprehensive form state management
- **useFormValidation** - Form validation utilities

#### Specialized Dropdown Hooks
- **useDomainDropdown** - Domain options
- **useSubdomainDropdown** - Subdomain options
- **useCategoryDropdown** - Category options
- **useUserDropdown** - User options

### Constants & Types (`src/constants/`, `src/types/`)

#### API Constants
- **API_ENDPOINTS** - Centralized API endpoint definitions
- **HTTP_METHODS** - HTTP method constants

#### UI Constants
- **BUTTON_VARIANTS** - Button styling options
- **MODAL_SIZES** - Modal size configurations
- **PAGINATION_OPTIONS** - Table pagination settings

#### Route Constants
- **ROUTES** - Application route definitions
- **NAVIGATION_MENU** - Menu structure configuration

#### TypeScript Types
- **Common Types** - Shared interface definitions
- **Entity Types** - Domain model interfaces

## 🚀 Usage Examples

### Basic Page with Table

```tsx
import React from 'react';
import { PageContainer, SmartDataTable } from '@/components/ui';
import { API_ENDPOINTS } from '@/constants';

const UsersPage = () => {
  const columns = [
    { id: 'Name', label: 'Name' },
    { id: 'Email', label: 'Email' },
    { id: 'Status', label: 'Status' },
  ];

  return (
    <PageContainer title="Users" subtitle="Manage system users">
      <SmartDataTable
        endpoint={API_ENDPOINTS.USERS.GET_LIST}
        columns={columns}
        actionButtons={[
          {
            label: 'Add User',
            onClick: () => console.log('Add user'),
            variant: 'primary',
          },
        ]}
        onEdit={(user) => console.log('Edit:', user)}
        onDelete={(user) => console.log('Delete:', user)}
      />
    </PageContainer>
  );
};
```

### Form with Validation

```tsx
import React from 'react';
import { useForm, commonValidationSchemas } from '@/hooks';
import { FormModal, InputGroup } from '@/components/ui';

const UserForm = ({ isOpen, onClose, onSubmit }) => {
  const { values, errors, handleChange, validateForm } = useForm({
    initialValues: { Name: '', Email: '' },
    validationRules: commonValidationSchemas.user,
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(values);
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="Add User"
      onSubmit={handleSubmit}
    >
      <InputGroup
        label="Name"
        type="text"
        name="Name"
        value={values.Name}
        handleChange={handleChange}
        error={errors.Name}
        required
      />
      <InputGroup
        label="Email"
        type="email"
        name="Email"
        value={values.Email}
        handleChange={handleChange}
        error={errors.Email}
        required
      />
    </FormModal>
  );
};
```

### API Data Fetching

```tsx
import React from 'react';
import { useApiGet, useDomainDropdown } from '@/hooks';
import { API_ENDPOINTS } from '@/constants';

const DomainSelector = () => {
  const { options, loading } = useDomainDropdown();
  
  return (
    <SearchableDropdown
      label="Select Domain"
      options={options}
      loading={loading}
      placeholder="Choose a domain..."
    />
  );
};
```

## 🔧 Migration Guide

### Before (Old Pattern)
```tsx
// Repetitive modal setup
const [modalOpen, setModalOpen] = useState(false);
const [formData, setFormData] = useState({});
const [errors, setErrors] = useState({});

// Manual API calls
const fetchData = async () => {
  try {
    setLoading(true);
    const response = await apiClient.post('/endpoint', data);
    // Handle response...
  } catch (error) {
    // Handle error...
  } finally {
    setLoading(false);
  }
};
```

### After (New Pattern)
```tsx
// Simplified with hooks
const { values, errors, handleChange, validateForm } = useForm({
  initialValues: {},
  validationRules: commonValidationSchemas.user,
});

const { post, loading } = useApiPost({
  successMessage: 'User created successfully',
});
```

## 📋 Benefits

1. **Reduced Code Duplication** - Common patterns extracted into reusable components
2. **Consistent UI** - Standardized components ensure visual consistency
3. **Type Safety** - TypeScript interfaces improve development experience
4. **Easier Maintenance** - Centralized components simplify updates
5. **Better Testing** - Isolated components are easier to test
6. **Faster Development** - Pre-built components speed up feature development

## 🎨 Customization

All components support customization through props:

```tsx
<PageContainer
  title="Custom Page"
  className="custom-styles"
  fullWidth={true}
  noPadding={true}
>
  <ContentCard
    variant="elevated"
    size="lg"
    headerActions={<Button label="Action" />}
  >
    Content here
  </ContentCard>
</PageContainer>
```

## 📚 Next Steps

1. Gradually migrate existing components to use the new modular system
2. Add more specialized hooks as needed
3. Extend type definitions for better type safety
4. Create additional UI components for common patterns
5. Add comprehensive tests for all new components

## 🤝 Contributing

When adding new components:

1. Follow the established patterns
2. Add TypeScript types
3. Include proper documentation
4. Export from index files
5. Add usage examples

This modular approach significantly improves code maintainability and developer productivity while maintaining all existing functionality.
