-- FUNCTION: public.fn_get_campaign_details_test(text, text, date, date)

-- DROP FUNCTION IF EXISTS public.fn_get_campaign_details_test(text, text, date, date);

CREATE OR REPLACE FUNCTION public.fn_get_campaign_details_test(
	p_article_id text,
	p_channel_id text,
	p_from_date date,
	p_to_date date)
    RETURNS TABLE(articleid uuid, channelid uuid, ads_clicks numeric, ads_conversions numeric, ads_cpa numeric, ads_cpc numeric, ads_cost numeric, segmentdate date, country text) 
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
DECLARE
    sql TEXT;
BEGIN
	sql := '
        SELECT distinct
		art."articleid",
		art."channelid",
		ads."Clicks"::NUMERIC,
		ads."Conversions"::NUMERIC,
		CASE
			WHEN ads."Conversions" > 0 THEN round(ads."CostMicros" / ads."Conversions" / 1000000.0, 4)
			ELSE 0::NUMERIC
		END AS cpa,
		CASE
			WHEN ads."Clicks" > 0 THEN round(ads."CostMicros" / ads."Clicks"::NUMERIC / 1000000.0, 4)
			ELSE 0::NUMERIC
		END AS cpc,
		round(ads."CostMicros" / 1000000.0, 4) AS cost,
		ads."SegmentDate",
		--ascm."CountryCode" AS country
		isocm."Name" as country
		FROM "ArticleDetails" ad
		JOIN vw_DistinctArticleChannelStyleMapping art on art.articleid = ad."Id" AND art."segmentdate" between $3 and $4
		JOIN "ArticleCampaignMappings" acm ON art.articleid = acm."ArticleId"
		JOIN "Ads_Campaigns" camp ON acm."CampaignId" = camp."SNo"
		JOIN "Ads_CampaignCountryDetails" ads ON ads."CampaignId" = camp."CampaignId"
		JOIN "Ads_CountryMaster" ascm ON ascm."CountryId" = ads."CountryId"
		join "Ads_ISO_CountryMaster" isocm on isocm."CountryCode" = ascm."CountryCode"
		WHERE ads."SegmentDate" between $3 and $4 ';

		  -- If p_article_id and  p_channel_id is not blank, apply the filter
		IF TRIM(p_article_id) <> '' AND TRIM(p_channel_id) <> '' THEN
	    -- Both article_id and channel_id are present
	    sql := sql || ' AND (art."articleid" = ANY(string_to_array($1, '','')::UUID[]) AND art."channelid" = ANY(string_to_array($2, '','')::UUID[])) ';
		ELSIF TRIM(p_article_id) <> '' THEN
	    -- Only article_id is present
	    sql := sql || ' AND art."articleid" = ANY(string_to_array($1, '','')::UUID[]) ';
		ELSIF TRIM(p_channel_id) <> '' THEN
	    -- Only channel_id is present
	    sql := sql || ' AND art."channelid" = ANY(string_to_array($2, '','')::UUID[]) ';
		END IF;

		-- Execute the query with safe parameter binding
		RETURN QUERY EXECUTE sql USING p_article_id, p_channel_id, p_from_date, p_to_date;
	END;
		
$BODY$;



GRANT EXECUTE ON FUNCTION public.fn_get_campaign_details_test(text, text, date, date) TO PUBLIC;


