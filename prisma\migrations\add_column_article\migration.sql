ALTER TABLE "ArticleDetails"
ADD COLUMN IF NOT EXISTS "Updated_By" UUID;


CREATE TABLE "AdsProvider" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "Provider" TEXT UNIQUE NOT NULL,
    "Description" TEXT,
    "TrackingKey" TEXT
);


ALTER TABLE "Domain"
    ADD COLUMN IF NOT EXISTS "AdsProviderId" UUID,
    ADD COLUMN IF NOT EXISTS "TrackingKey" TEXT;

ALTER TABLE "SubDomain"
    ADD COLUMN IF NOT EXISTS "AdsProviderId" UUID,
    ADD COLUMN IF NOT EXISTS "TrackingKey" TEXT;

