import { useState, useCallback, useEffect } from 'react';

interface FormField {
  value: any;
  error?: string;
  touched?: boolean;
}

interface FormState {
  [key: string]: FormField;
}

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any, formData: FormState) => string | null;
  message?: string;
}

interface ValidationRules {
  [key: string]: ValidationRule;
}

interface UseFormOptions<T> {
  initialValues: T;
  validationRules?: ValidationRules;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

interface UseFormReturn<T> {
  formData: FormState;
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isDirty: boolean;
  setValue: (name: string, value: any) => void;
  setError: (name: string, error: string) => void;
  setTouched: (name: string, touched?: boolean) => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleBlur: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  validateField: (name: string, value?: any) => string | null;
  validateForm: () => boolean;
  reset: (newValues?: Partial<T>) => void;
  setFormData: (data: Partial<T>) => void;
}

export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T>
): UseFormReturn<T> {
  const {
    initialValues,
    validationRules = {},
    validateOnChange = false,
    validateOnBlur = true,
  } = options;

  // Initialize form state
  const initializeFormState = useCallback((values: T): FormState => {
    const state: FormState = {};
    Object.keys(values).forEach(key => {
      state[key] = {
        value: values[key],
        error: '',
        touched: false,
      };
    });
    return state;
  }, []);

  const [formData, setFormDataState] = useState<FormState>(() => 
    initializeFormState(initialValues)
  );

  // Derived values
  const values = Object.keys(formData).reduce((acc, key) => {
    acc[key as keyof T] = formData[key].value;
    return acc;
  }, {} as T);

  const errors = Object.keys(formData).reduce((acc, key) => {
    acc[key] = formData[key].error || '';
    return acc;
  }, {} as Record<string, string>);

  const touched = Object.keys(formData).reduce((acc, key) => {
    acc[key] = formData[key].touched || false;
    return acc;
  }, {} as Record<string, boolean>);

  const isValid = Object.values(formData).every(field => !field.error);
  const isDirty = Object.values(formData).some(field => field.touched);

  // Validation function
  const validateField = useCallback((name: string, value?: any): string | null => {
    const fieldValue = value !== undefined ? value : formData[name]?.value;
    const rules = validationRules[name];
    
    if (!rules) return null;

    // Required validation
    if (rules.required && (!fieldValue || (typeof fieldValue === 'string' && !fieldValue.trim()))) {
      return rules.message || `${name} is required`;
    }

    // Skip other validations if field is empty and not required
    if (!fieldValue && !rules.required) return null;

    // String validations
    if (typeof fieldValue === 'string') {
      if (rules.minLength && fieldValue.length < rules.minLength) {
        return rules.message || `${name} must be at least ${rules.minLength} characters`;
      }
      
      if (rules.maxLength && fieldValue.length > rules.maxLength) {
        return rules.message || `${name} must be no more than ${rules.maxLength} characters`;
      }
      
      if (rules.pattern && !rules.pattern.test(fieldValue)) {
        return rules.message || `${name} format is invalid`;
      }
    }

    // Custom validation
    if (rules.custom) {
      return rules.custom(fieldValue, formData);
    }

    return null;
  }, [formData, validationRules]);

  // Set individual field value
  const setValue = useCallback((name: string, value: any) => {
    setFormDataState(prev => {
      const newState = {
        ...prev,
        [name]: {
          ...prev[name],
          value,
          touched: true,
        },
      };

      // Validate on change if enabled
      if (validateOnChange) {
        const error = validateField(name, value);
        newState[name].error = error || '';
      }

      return newState;
    });
  }, [validateField, validateOnChange]);

  // Set field error
  const setError = useCallback((name: string, error: string) => {
    setFormDataState(prev => ({
      ...prev,
      [name]: {
        ...prev[name],
        error,
      },
    }));
  }, []);

  // Set field touched
  const setTouched = useCallback((name: string, touched = true) => {
    setFormDataState(prev => ({
      ...prev,
      [name]: {
        ...prev[name],
        touched,
      },
    }));
  }, []);

  // Handle input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    let fieldValue = value;

    // Handle different input types
    if (type === 'checkbox') {
      fieldValue = (e.target as HTMLInputElement).checked;
    } else if (type === 'number') {
      fieldValue = value === '' ? '' : Number(value);
    }

    setValue(name, fieldValue);
  }, [setValue]);

  // Handle input blur
  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name } = e.target;
    
    setTouched(name, true);
    
    if (validateOnBlur) {
      const error = validateField(name);
      setError(name, error || '');
    }
  }, [validateField, validateOnBlur, setTouched, setError]);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    let isFormValid = true;
    const newFormData = { ...formData };

    Object.keys(formData).forEach(name => {
      const error = validateField(name);
      newFormData[name] = {
        ...newFormData[name],
        error: error || '',
        touched: true,
      };
      
      if (error) {
        isFormValid = false;
      }
    });

    setFormDataState(newFormData);
    return isFormValid;
  }, [formData, validateField]);

  // Reset form
  const reset = useCallback((newValues?: Partial<T>) => {
    const resetValues = newValues ? { ...initialValues, ...newValues } : initialValues;
    setFormDataState(initializeFormState(resetValues));
  }, [initialValues, initializeFormState]);

  // Set form data
  const setFormData = useCallback((data: Partial<T>) => {
    setFormDataState(prev => {
      const newState = { ...prev };
      Object.keys(data).forEach(key => {
        if (newState[key]) {
          newState[key] = {
            ...newState[key],
            value: data[key as keyof T],
          };
        }
      });
      return newState;
    });
  }, []);

  return {
    formData,
    values,
    errors,
    touched,
    isValid,
    isDirty,
    setValue,
    setError,
    setTouched,
    handleChange,
    handleBlur,
    validateField,
    validateForm,
    reset,
    setFormData,
  };
}
