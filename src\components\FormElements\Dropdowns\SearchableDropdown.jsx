"use client";
import { useEffect, useRef, useState } from "react";
import { GoTriangleUp, GoTriangleDown } from "react-icons/go";
import { IoCloseOutline } from "react-icons/io5";

const SearchableDropdown = ({
  label,
  options = [],
  placeholder = "Search...",
  value, // expected to be Id
  onChange,
  error = "",
  displayKey = "",
  required,
  displayKey2 = "",
  idKey = "Id",
}) => {
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);
  const containerRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isKeyboardNavigation, setIsKeyboardNavigation] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [justOpened, setJustOpened] = useState(false);

  const selectedItem = options.find((item) => item[idKey] === value);
  const hasValue = !!selectedItem;
  const normalizedSearch = searchTerm.trim().toLowerCase();

  const filteredOptions = options.filter(
    (item) =>
      item[displayKey]?.toLowerCase().includes(normalizedSearch) ||
      item[idKey]?.toLowerCase().includes(normalizedSearch)
  );  
  const formatText = (text) => {
    if (text && text.startsWith("arc_")) {
      return <span className="underline decoration-red-500">{text}</span>;
    }
    return text;
  };
  useEffect(() => {
    if (isDropdownOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isDropdownOpen]);

  const handleSelect = (item) => {
    onChange(item); // send whole item
    setSearchTerm("");
    setIsDropdownOpen(false);
    setIsFocused(false);
    setIsKeyboardNavigation(false);
    setFocusedIndex(-1);
    setJustOpened(false);
  };

  const handleClear = (e) => {
    e.preventDefault();
    e.stopPropagation();
    onChange(null); // clear whole item
    setSearchTerm("");
    setIsDropdownOpen(false);
    setIsFocused(false);
    setIsKeyboardNavigation(false);
    setFocusedIndex(-1);
    setJustOpened(false);
  };

  const handleToggleDropdown = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (justOpened) {
      return;  // 🚀 SKIP if just opened
    }
    if (isDropdownOpen) {
      setIsDropdownOpen(false);
      setIsFocused(false);
      setIsKeyboardNavigation(false);
      setFocusedIndex(-1);
      setJustOpened(false);
    } else {
      setIsDropdownOpen(true);
      setIsFocused(true);
      setIsKeyboardNavigation(false);
      setFocusedIndex(-1);
      setJustOpened(true);
      setTimeout(() => {
        setJustOpened(false);
      }, 300);
      
    }
  };

  const handleFocus = () => {
    if (!isDropdownOpen) {
      setIsFocused(true);
      setIsKeyboardNavigation(true);
      setIsDropdownOpen(true);
      setFocusedIndex(-1);
      setJustOpened(true);
      setTimeout(() => {
        setJustOpened(false);
      }, 300);
    }
  };

  const handleBlur = (e) => {
    const nextFocusTarget = e.relatedTarget;
    if (
      dropdownRef.current?.contains(nextFocusTarget) ||
      containerRef.current?.contains(nextFocusTarget) ||
      inputRef.current?.contains(nextFocusTarget)
    ) {
      return;
    }
    setIsFocused(false);
    setIsDropdownOpen(false);
    setIsKeyboardNavigation(false);
    setFocusedIndex(-1);
    setSearchTerm("");
    setJustOpened(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      setIsDropdownOpen(!isDropdownOpen);
      setIsKeyboardNavigation(true);
      setFocusedIndex(-1);
    } else if (e.key === "Escape") {
      setIsDropdownOpen(false);
      setIsFocused(false);
      setIsKeyboardNavigation(false);
      setSearchTerm("");
      setFocusedIndex(-1);
      containerRef.current?.blur();
    }
  };

  const handleInputKeyDown = (e) => {
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setFocusedIndex(0);
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setFocusedIndex(filteredOptions.length - 1);
    } else if (e.key === "Tab") {
      setIsDropdownOpen(false);
      setIsFocused(false);
      setIsKeyboardNavigation(false);
      setFocusedIndex(-1);
    } else if (e.key === "Escape") {
      setIsDropdownOpen(false);
      setIsFocused(false);
      setIsKeyboardNavigation(false);
      setSearchTerm("");
      setFocusedIndex(-1);
      containerRef.current?.focus();
    }
  };

  const handleOptionKeyDown = (e, item, index) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleSelect(item);
    } else if (e.key === "ArrowDown") {
      e.preventDefault();
      const nextIndex = (index + 1) % filteredOptions.length;
      setFocusedIndex(nextIndex);
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      const prevIndex = index === 0 ? filteredOptions.length - 1 : index - 1;
      setFocusedIndex(prevIndex);
    } else if (e.key === "Escape") {
      setIsDropdownOpen(false);
      setIsFocused(false);
      setIsKeyboardNavigation(false);
      setSearchTerm("");
      setFocusedIndex(-1);
      containerRef.current?.focus();
    } else if (e.key === "Tab") {
      setIsDropdownOpen(false);
      setIsFocused(false);
      setIsKeyboardNavigation(false);
      setFocusedIndex(-1);
      setSearchTerm("");
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (justOpened) return;
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setSearchTerm("");
        setIsFocused(false);
        setIsKeyboardNavigation(false);
        setFocusedIndex(-1);
        setJustOpened(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [justOpened]);


  return (
    <div className="relative z-10 w-full pt-5" ref={dropdownRef}>
      <div
        ref={containerRef}
        className={`flex w-full items-center justify-between rounded-lg border-[1.5px] border-stroke bg-transparent px-4 py-3 text-left outline-none transition-all duration-200 focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary cursor-pointer ${
          error ? "border-red-500" : ""
        } ${isFocused ? "border-primary" : ""}`}
        onClick={handleToggleDropdown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        <span className={`${!hasValue ? "opacity-0" : "text-dark dark:text-white"}`}>
          {selectedItem
            ? <>
                {formatText(selectedItem[displayKey] || "")}
                {selectedItem[displayKey2] ? ` (${selectedItem[displayKey2]})` : ""}
              </>
            : ""}
        </span>
        <div className="flex items-center gap-2 py-1">
          {selectedItem && (
            <button
              type="button"
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={handleClear}
            >
              <IoCloseOutline size={16} />
            </button>
          )}
          {isDropdownOpen ? <GoTriangleUp /> : <GoTriangleDown />}
        </div>
      </div>

      <label
        className={`absolute left-4 top-8 cursor-text transition-all duration-200 pointer-events-none ${
          isFocused || isDropdownOpen || hasValue
            ? "left-3 top-1 -translate-y-4 bg-white px-1 text-xs dark:bg-dark-2"
            : "text-dark-6 dark:text-white"
        } ${isFocused || isDropdownOpen ? "text-primary" : ""} ${
          error ? "text-red-500" : ""
        }`}
      >
        {label} {required && <span className="ml-1 select-none text-red">*</span>}
      </label>

      {isDropdownOpen && (
        <div className="absolute mt-1 w-full rounded-lg border border-stroke bg-white shadow-lg dark:border-dark-3 dark:bg-gray-dark z-50">
          <div className="p-2">
            <input
              ref={inputRef}
              type="text"
              className="w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-4 py-3 text-sm outline-none focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleInputKeyDown}
              onClick={(e) => e.stopPropagation()}
              required={required}
            />
          </div>
          <div className="max-h-60 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((item, index) => (
                <div
                  key={item[idKey]}
                  className={`cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-dark-3 focus:bg-gray-100 dark:focus:bg-dark-3 outline-none ${
                    value === item[idKey] ? "bg-primary/10 text-primary" : ""
                  } ${focusedIndex === index ? "bg-gray-100 dark:bg-dark-3" : ""}`}
                  onClick={() => handleSelect(item)}
                  onKeyDown={(e) => handleOptionKeyDown(e, item, index)}
                  tabIndex={0}
                  ref={(el) => {
                    if (focusedIndex === index && el) {
                      el.focus();
                    }
                  }}
                >
                <>
                  {formatText(item[displayKey] || "")}
                  {item[displayKey2] ? ` (${item[displayKey2]})` : ""}
                </>
                </div>
              ))
            ) : (
              <div className="px-3 py-3 text-sm text-gray-500 dark:text-gray-400">
                No options found
              </div>
            )}
          </div>
        </div>
      )}
      {error && (
        <p className="absolute -bottom-5 mt-1 text-xs text-red-500">{error}</p>
      )}
    </div>
  );
};

export default SearchableDropdown;