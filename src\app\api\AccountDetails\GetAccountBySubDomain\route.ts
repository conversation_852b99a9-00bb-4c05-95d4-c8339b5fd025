import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
import { prisma } from '../../../../lib/prisma';

export async function GET(req: NextRequest) {
    try {
      
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const subDomainId = searchParams.get("subDomainId");

        if (!subDomainId) {
            return NextResponse.json(
                { error: "subDomainId is Required" },
                { status: 400 }
            );
        }

        const subdomain = await prisma.subDomain.findUnique({
            where: {
                Id: subDomainId,
                IsDeleted: false,
            },
            select: {
                AccountId: true,
            },
        });

        if (!subdomain) {
            return NextResponse.json(
                { error: "Subdomain not found or deleted" },
                { status: 404 }
            );
        }

        const accountIds = subdomain.AccountId.map((id: string) => BigInt(id));

        const accountDetails = await prisma.ads_AccountDetails.findMany({
            where: {
                AccountId: {
                    in: accountIds,
                }
            },
            select: {
                AccountId: true,
                ClientCustomer: true,
                DescriptiveName: true,
            },
        });

        const sanitizedDetails = accountDetails.map((item) => ({
            ...item,
            AccountId: item.AccountId.toString(),
            ClientCustomer: item.ClientCustomer?.toString(),
        }));

        return NextResponse.json({
            success: true,
            data: sanitizedDetails,
        });
    } catch (error) {
        console.error("Error fetching account details:", error);
        return NextResponse.json(
            { error: "Failed to fetch account details" },
            { status: 500 }
        );
    }
}