import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { GoogleAdsService } from '@/services/googleCampaignService';
import { prisma } from '../../../../lib/prisma';

export async function POST(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const body = await req.json();
        const { budgetName, budgetAmountMicros, campaignName, status, customerId, articleId } = body;

        if (!budgetName || !budgetAmountMicros || !campaignName || !customerId) {
            return NextResponse.json(
                { error: 'Missing required campaign fields' },
                { status: 400 }
            );
        }

        const amountInMicros = Math.round(Number(budgetAmountMicros) * 1000000);

        const googleAdsService = new GoogleAdsService();
        const result = await googleAdsService.createCampaign({
            budgetName,
            budgetAmountMicros: amountInMicros.toString(),
            campaignName,
            status,
            customerId,
        });

        const campaignDetails = result.campaignDetails;
        const campaign = campaignDetails.campaign;
        const campaignBudget = campaignDetails.campaign_budget;

        if (!campaign) {
            throw new Error('Campaign object is undefined');
        }
        if (!campaignBudget) {
            throw new Error('Campaign budget object is undefined');
        }
        if (!campaign.resource_name) {
            throw new Error('Campaign resource_name is undefined');
        }
        if (!campaignBudget.resource_name) {
            throw new Error('Campaign budget resource_name is undefined');
        }

        const campaignId = campaign.resource_name.split('/').pop() ?? '';
        const budgetId = campaignBudget.resource_name.split('/').pop() ?? '';

        const savedCampaign = await prisma.ads_Campaigns.create({
            data: {
                AccountId: BigInt(customerId),
                CampaignId: BigInt(campaignId),
                CampaignResourceName: campaign.resource_name,
                Status: campaign.status,
                AdvertisingChannelType: campaign.advertising_channel_type,
                Name: campaign.name,
            },
        });

        const savedBudget = await prisma.ads_CampaignsBudget.create({
            data: {
                CampaignId: BigInt(campaignId),
                CampaignBudgetId: BigInt(budgetId),
                CampaignBudgetResourceName: campaignBudget.resource_name,
                Name: campaignBudget.name,
                Status: campaignBudget.status,
                AmountMicros: BigInt(campaignBudget.amount_micros),
            },
        });

        const CampaignSno = await prisma.ads_Campaigns.findFirst({
            where: {
                CampaignId: parseInt(campaignId)
            },
            select: {
                SNo: true
            }
        });

        if (!CampaignSno) {
            return NextResponse.json({
                success: false,
                message: "Campaign not found"
            }, { status: 404 });
        }

        const existingMapping = await prisma.articleCampaignMappings.findFirst({
            where: {
                ArticleId: articleId,
                CampaignId: CampaignSno.SNo
            }
        });

        if (existingMapping) {
            return NextResponse.json({
                success: true,
                message: "Mapping already exists"
            });
        }

        const newMapping = await prisma.articleCampaignMappings.create({
            data: {
                ArticleId: articleId,
                CampaignId: CampaignSno.SNo
            }
        });
        return NextResponse.json({
            success: true,
            message: "Campaign created and assigned to the article successfully.",
            result,
        }, { status: 200 });

    } catch (error: any) {
        console.error('Full error:', error);
        const message = error?.response?.data ?? error.message ?? 'Unknown error occurred';
        return NextResponse.json(
            {
                error: 'Failed to create campaign',
                details: message,
            },
            { status: 500 }
        );
    }
}