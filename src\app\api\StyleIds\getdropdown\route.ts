import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // type AuthenticatedUser = {
        //     User_Type: string;
        //     Id: string;
        // };

        // const user = await verifyToken(req) as AuthenticatedUser;
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const { searchParams } = new URL(req.url);
        const userIdFromQuery = searchParams.get('userId');

        let where: any = {
            IsDeleted: false
        };

        // If specific userId query provided
        if (userIdFromQuery) {
            // Find all StyleIdUserMappings for this user
            const userMappings = await prisma.styleIdUserMappings.findMany({
                where: {
                    UserId: userIdFromQuery
                },
                select: {
                    StyleId: true
                }
            });

            const styleIds = userMappings.map(mapping => mapping.StyleId);

            where.Id = {
                in: styleIds
            };
        }
        else if (role !== 'Super Admin' && role !== 'Admin') {
            where.StyleIdUserMappings = {
                some: {
                    UserId: userId
                }
            };
        }

        const stylesWithUsers = await prisma.styleIds.findMany({
            where,
            orderBy: {
                Name: 'desc'
            },
            select: {
                Id: true,
                Name: true,
            }
        });

        const response = NextResponse.json({
            success: true,
            data: stylesWithUsers,
        });
        return response;

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } 
}