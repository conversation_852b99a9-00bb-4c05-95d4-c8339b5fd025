import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const domainId = searchParams.get('domainId');
        const subDomainId = searchParams.get('subDomainId');

        let channels;
        if (domainId || subDomainId) {
            const articleWhere: any = {};
            if (domainId) articleWhere.Domain = domainId;
            if (subDomainId) articleWhere.SubDomain = subDomainId;

            const articles = await prisma.articleDetails.findMany({
                where: articleWhere,
                select: {
                    Id: true,
                    CustomChannal: true
                }
            });

            const customChannelIds = articles
                .map(article => article.CustomChannal)
                .filter((channelId): channelId is string => channelId !== null && channelId !== undefined);

            channels = await prisma.channals.findMany({
                where: {
                    Id: {
                        in: customChannelIds.length > 0 ? customChannelIds : []
                    }
                },
                orderBy: {
                    DisplayName: 'desc',
                },
                select: {
                    Id: true,
                    Name: true,
                    DisplayName: true,
                },
            });
        } else {
            channels = await prisma.channals.findMany({
                orderBy: {
                    DisplayName: 'desc',
                },
                select: {
                    Id: true,
                    Name: true,
                    DisplayName: true,
                },
            });
        }

        const dataWithCustomChannelId = channels.map(channel => {
            const parts = (channel.Name ?? "").split('/');
            const customChannelId = parts[parts.length - 1];
            return {
                Id: channel.Id,
                DisplayName: channel.DisplayName,
                CustomChannelId: customChannelId,
            };
        });

        return NextResponse.json({
            success: true,
            data: dataWithCustomChannelId,
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Failed to fetch users" },
            { status: 500 }
        );
    }
}