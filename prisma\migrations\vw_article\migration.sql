-- View: public.vw_article

-- DROP VIEW public.vw_article;

CREATE OR REPLACE VIEW public.vw_article
 AS
 SELECT c."Name" AS "CategoryName",
    c."ShowUrlName" AS "CategoryUrl",
    d."Name" AS "DomainName",
    d."ShowUrlName" AS "DomainUrl",
    d."GId",
    d."AWId",
    d."SendTo",
    d."HeadTagScript",
    d."HeadTagScriptLandingPage",
    d."HeadTagScriptSearchPage",
    d."StyleIdLm",
    d."StyleIdDm",
    sd."Name" AS "SubDomainName",
    (sd."Url" || '.'::text) || d."ShowUrlName" AS "SubDomainUrl",
    sd."GId" AS "SubGId",
    sd."AWId" AS "SubAWId",
    sd."SendTo" AS "SubSendTo",
    sd."HeadTagScript" AS "SubHeadTagScript",
    sd."HeadTagScriptLandingPage" AS "SubHeadTagScriptLandingPage",
    sd."HeadTagScriptSearchPage" AS "SubHeadTagScriptSearchPage",
    cc."ReportingDimensionId" AS "ChannalName",
    s."StyleId",
    a."Id",
    a."Title",
    a."Url",
    a."MetaTitle",
    a."MetaDescription",
    a."MetaKeys",
    a."Image",
    a."ShowUrlName",
    a."ShowsAds",
    a."UpdatedAt",
    a."CreatedAt",
    a."ReadTime",
    au."Name" AS "CreatedBy",
    au."AboutMe",
    au."ProfilePic" AS "ProfileImage",
    a."Description",
    a."ShortDescription",
    d."WideLogo",
    d."SquareLogo",
    a."AdRelatedSearches",
    a."CheckRelatedSearches2",
    a."AdReletadSearches2",
    d."ContactEmail",
    d."Address",
    d."AdsTxtContent"
   FROM "ArticleDetails" a
     LEFT JOIN "Domain" d ON a."Domain" = d."Id"
     LEFT JOIN "SubDomain" sd ON a."SubDomain" = sd."Id"
     LEFT JOIN "Category" c ON a."Category" = c."Id"
     LEFT JOIN "Channals" cc ON a."CustomChannal" = cc."Id"
     LEFT JOIN "StyleIds" s ON a."StyleIdLm" = s."Id"
     LEFT JOIN "AdminUser" au ON au."Id" = a."User_Id_Settings"
  WHERE COALESCE(a."IsDeleted", false) = false AND COALESCE(a."Published", false) = true AND COALESCE(a."ShowArticle", false) = true;
