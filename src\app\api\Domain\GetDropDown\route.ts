import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // type AuthenticatedUser = {
        //     User_Type: string;
        //     Id: string;
        // };

        // const user = await verifyToken(req) as AuthenticatedUser;

        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        let domainWhere: any = {
            IsDeleted: false
        };

        if (role !== 'Super Admin' && role !== 'Admin') {
            domainWhere = {
                ...domainWhere,
                SubDomain_SubDomain_DomainToDomain: {
                    some: {
                        IsDeleted: false,
                        SubDomainUserMappings: {
                            some: {
                                UserId: userId
                            }
                        }
                    }
                }
            };
        }

        const domains = await prisma.domain.findMany({
            where: domainWhere,
            select: {
                Id: true,
                Name: true,
                ShowUrlName: true
            },
            orderBy: {
                Name: 'desc'
            }
        });

        return NextResponse.json({
            success: true,
            data: domains
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } 
}