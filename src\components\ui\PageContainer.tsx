"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface PageContainerProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  className?: string;
  headerActions?: React.ReactNode;
  breadcrumbs?: React.ReactNode;
  fullWidth?: boolean;
  noPadding?: boolean;
}

const PageContainer: React.FC<PageContainerProps> = ({
  children,
  title,
  subtitle,
  className = '',
  headerActions,
  breadcrumbs,
  fullWidth = false,
  noPadding = false,
}) => {
  return (
    <div className="w-full min-h-screen relative overflow-x-hidden font-poppins bg-gray-50 dark:bg-[#020d1a]">
      {/* Header Background */}
      <div className="h-[170px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="p-6">
          {breadcrumbs && (
            <div className="mb-2">
              {breadcrumbs}
            </div>
          )}
          {title && (
            <h1 className="text-white text-2xl font-bold">
              {title}
            </h1>
          )}
          {subtitle && (
            <p className="text-blue-100 text-sm mt-1">
              {subtitle}
            </p>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className={cn(
        "absolute bg-white dark:bg-gray-dark shadow-lg rounded-[10px] left-1/2 -translate-x-1/2 top-[90px] border border-stroke dark:border-dark-3",
        fullWidth ? "w-[98%]" : "w-[95%]",
        noPadding ? "" : "p-4 sm:p-6",
        className
      )}>
        {headerActions && (
          <div className="flex justify-end mb-4">
            {headerActions}
          </div>
        )}
        
        <div
          className={cn(
            "flex flex-col",
            noPadding ? "" : "p-6"
          )}
          style={{ minHeight: "calc(100vh - 230px)" }}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

export default PageContainer;
