import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        let where: any = {
            IsDeleted: false,
            User_Type: {
                in: ['Partner', 'Account']
            }
        };

        const users = await prisma.adminUser.findMany({
            where,
            orderBy: {
                Name: 'desc'
            },
            select: {
                Id: true,
                Name: true,
                Email: true
            }
        });
        return NextResponse.json({
            success: true,
            data: users,

        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Failed to fetch users" },
            { status: 500 }
        );
    }
}