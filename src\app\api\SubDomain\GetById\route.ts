import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const Id = searchParams.get("id");

        let where: any = {
            Id: Id,
            IsDeleted: false
        };

        const SubDomainsWithUsers = await prisma.subDomain.findMany({
            where,
            include: {
                Domain_SubDomain_DomainToDomain: {
                    select: {
                        Name: true
                    }
                },
                SubDomainUserMappings: {
                    select: {
                        UserId: true
                    }
                },
                _count: {
                    select: {
                        SubDomainUserMappings: true
                    }
                }
            }
        });

        const transformedData = SubDomainsWithUsers.map(SubDomain => ({
            Id: SubDomain.Id,
            Name: SubDomain.Name,
            Url: SubDomain.Url,
            CId: SubDomain.CId,
            HeadTag: SubDomain.HeadTag,
            Articles: SubDomain.Articles,
            AccountId: SubDomain.AccountId,
            HeadTagScript: SubDomain.HeadTagScript,
            HeadTagScriptLandingPage: SubDomain.HeadTagScriptLandingPage,
            HeadTagScriptSearchPage: SubDomain.HeadTagScriptSearchPage,
            GId: SubDomain.GId,
            AWId: SubDomain.AWId,
            SendTo: SubDomain.SendTo,
            DomainName: SubDomain.Domain_SubDomain_DomainToDomain?.Name,
            CreatedAt: SubDomain.CreatedAt,
            UserMappings: SubDomain.SubDomainUserMappings.map(mapping => mapping.UserId),
            UserCount: SubDomain._count.SubDomainUserMappings,
            // WideLogo: SubDomain.WideLogo,
            // SquareLogo: SubDomain.SquareLogo,
            AdsProviderId: SubDomain.AdsProviderId,
            TrackingKey: SubDomain.TrackingKey
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    }
}