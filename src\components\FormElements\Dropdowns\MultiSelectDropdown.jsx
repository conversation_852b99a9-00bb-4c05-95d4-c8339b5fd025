"use client";
import { useEffect, useRef, useState } from "react";
import { GoTriangleUp, GoTriangleDown } from "react-icons/go";

const MultiSelectDropdown = ({
  label,
  options = [],
  placeholder = "Search...",
  value = [],
  onChange,
  error = "",
  required,
  displayKey = "DisplayName",
  idKey = "Id",
  showSelectAll = true,
}) => {
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [justOpened, setJustOpened] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);

  const normalizedSearch = searchTerm.trim().toLowerCase();
  const filteredOptions = options
    .filter(
      (item) =>
        item[displayKey]?.toLowerCase().includes(normalizedSearch) ||
        item[idKey]?.toLowerCase().includes(normalizedSearch)
    )
    .sort((a, b) => {
      const aSelected = value.includes(a[idKey]);
      const bSelected = value.includes(b[idKey]);
      return aSelected === bSelected ? 0 : aSelected ? -1 : 1;
    });


  const allFilteredSelected =
    filteredOptions.length > 0 &&
    filteredOptions.every((item) => value.includes(item[idKey]));

  const openDropdown = () => {
    setIsDropdownOpen(true);
    setIsFocused(true);
    setJustOpened(true);
    setTimeout(() => {
      setJustOpened(false);
      inputRef.current?.focus();
    }, 10);
  };

  const closeDropdown = () => {
    setIsDropdownOpen(false);
    setIsFocused(false);
    setSearchTerm("");
    setFocusedIndex(-1);
    setJustOpened(false);
  };

  const toggleItem = (itemId) => {
    const newValue = value.includes(itemId)
      ? value.filter((id) => id !== itemId)
      : [...value, itemId];
    onChange(newValue);
  };

  const toggleSelectAll = () => {
    const filteredIds = filteredOptions.map((item) => item[idKey]);
    const newValue = allFilteredSelected
      ? value.filter((id) => !filteredIds.includes(id))
      : [...new Set([...value, ...filteredIds])];
    onChange(newValue);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (justOpened) return;
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        closeDropdown();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [justOpened]);

  const handleInputKeyDown = (e) => {
    if (e.key === "Escape" || e.key === "Tab") {
      closeDropdown();
    } else if (e.key === "ArrowDown") {
      e.preventDefault();
      setFocusedIndex((prev) =>
        prev + 1 >= filteredOptions.length ? 0 : prev + 1
      );
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setFocusedIndex((prev) =>
        prev - 1 < 0 ? filteredOptions.length - 1 : prev - 1
      );
    } else if ((e.key === "Enter" || e.key === " ") && focusedIndex >= 0) {
      e.preventDefault();
      toggleItem(filteredOptions[focusedIndex][idKey]);
    }
  };

  const hasValue = value.length > 0;

  return (
    <div className="relative z-10 w-full pt-5" ref={dropdownRef}>
      <div
        className={`flex w-full items-center justify-between rounded-lg border-[1.5px] bg-transparent px-4 py-4 text-left outline-none transition-all duration-200 cursor-pointer dark:border-dark-3 dark:bg-dark-2 ${
          error ? "border-red-500" : isFocused ? "border-primary" : "border-stroke"
        }`}
        onMouseDown={(e) => {
          e.preventDefault(); // Prevent blur before click
          isDropdownOpen ? closeDropdown() : openDropdown();
        }}
        onFocus={() => {
          if (!isDropdownOpen) openDropdown();
        }}
        tabIndex={0}
      >
        <span className={`text-dark dark:text-white ${!hasValue ? "opacity-0" : ""}`}>
          {hasValue ? `${value.length} item${value.length > 1 ? "s" : ""} selected` : ""}
        </span>
        {isDropdownOpen ? <GoTriangleUp /> : <GoTriangleDown />}
      </div>

      <label
        className={`absolute left-4 top-8 cursor-text transition-all duration-200 ${
          isFocused || isDropdownOpen || hasValue
            ? "left-3 top-1 -translate-y-4 bg-white px-1 text-xs dark:bg-dark-2"
            : "text-dark-6 dark:text-white"
        } ${
          isFocused || isDropdownOpen ? "text-primary" : ""
        } ${error ? "text-red-500" : ""}`}
        onClick={() => {
          if (!isDropdownOpen) openDropdown();
        }}
      >
        {label} {required && <span className="ml-1 select-none text-red">*</span>}
      </label>

      {isDropdownOpen && (
        <div className="absolute mt-1 w-full rounded-lg border border-stroke bg-white shadow-lg dark:border-dark-3 dark:bg-gray-dark z-50">
          <div className="p-2">
            <input
              ref={inputRef}
              type="text"
              className="w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-4 py-4 text-sm outline-none focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleInputKeyDown}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
          <div className="max-h-60 overflow-y-auto">
            {showSelectAll && filteredOptions.length > 0 && (
              <div
                className="flex cursor-pointer items-center gap-2 px-3 py-2 hover:bg-gray-100 dark:hover:bg-dark-3"
                onClick={toggleSelectAll}
              >
                <input
                  type="checkbox"
                  checked={allFilteredSelected}
                  onChange={toggleSelectAll}
                  onClick={(e) => e.stopPropagation()}
                  className="rounded border-stroke text-primary focus:ring-primary dark:border-dark-3"
                />
                <span>Select All</span>
              </div>
            )}
            {filteredOptions.length > 0 ? (
              filteredOptions.map((item, index) => (
                <div
                  key={item[idKey]}
                  className={`flex cursor-pointer items-center gap-2 px-3 py-2 hover:bg-gray-100 dark:hover:bg-dark-3 ${
                    focusedIndex === index ? "bg-gray-100 dark:bg-dark-3" : ""
                  }`}
                  onClick={() => toggleItem(item[idKey])}
                  onMouseEnter={() => setFocusedIndex(index)}
                  ref={(el) => {
                    if (focusedIndex === index && el) {
                      el.scrollIntoView({ block: "nearest" });
                    }
                  }}
                >
                  <input
                    type="checkbox"
                    checked={value.includes(item[idKey])}
                    onChange={() => toggleItem(item[idKey])}
                    onClick={(e) => e.stopPropagation()}
                    className="rounded border-stroke text-primary focus:ring-primary dark:border-dark-3"
                  />
                  <span>
                    {item[displayKey] && item[displayKey].startsWith("arc_") 
                      ? <span className="underline decoration-red-500">{item[displayKey]}</span>
                      : item[displayKey]}
                  </span>
                </div>
              ))
            ) : (
              <div className="px-3 py-3 text-sm text-gray-500 dark:text-gray-400">
                No options found
              </div>
            )}
          </div>
        </div>
      )}

      {error && (
        <p className="absolute -bottom-5 mt-1 text-xs text-red-500">{error}</p>
      )}
    </div>
  );
};

export default MultiSelectDropdown;