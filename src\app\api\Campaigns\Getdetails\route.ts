
// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

// export async function GET(req: NextRequest) {
//     try {
//         const user = await verifyToken(req);
//         if (!user) {
//             return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         }

//         const { searchParams } = new URL(req.url);
//         const start = parseInt(searchParams.get("page") || "1");
//         const length = parseInt(searchParams.get("length") || "10");
//         const orderBy = searchParams.get("orderBy") || "CampaignId";
//         const orderDir = searchParams.get("orderDir") || "asc";
//         const search = searchParams.get("q")?.toLowerCase();
//         const startDate = searchParams.get("startDate");
//         const endDate = searchParams.get("endDate");

//         if (startDate && isNaN(Date.parse(startDate))) {
//             return NextResponse.json({ error: "Invalid startDate format" }, { status: 400 });
//         }
//         if (endDate && isNaN(Date.parse(endDate))) {
//             return NextResponse.json({ error: "Invalid endDate format" }, { status: 400 });
//         }

//         // Step 1: Get campaign details between startDate and endDate
//         const detailsWhere: any = {};
//         if (startDate || endDate) {
//             detailsWhere.SegmentDate = {};
//             if (startDate) detailsWhere.SegmentDate.gte = new Date(startDate);
//             if (endDate) detailsWhere.SegmentDate.lte = new Date(endDate);
//         }

//         const campaignDetails = await prisma.ads_CampaignDetails.findMany({
//             where: detailsWhere,
//             select: {
//                 CampaignId: true,
//                 Clicks: true,
//                 Impressions: true,
//                 CTR: true,
//                 AverageCPC: true,
//                 AverageCost: true,
//                 InvalidClicks: true,
//                 Conversions: true,
//                 CostPerConversion: true
//             }
//         });

//         // Group by CampaignId and aggregate
//         const campaignIdGroups = new Map<string, any[]>();
//         for (const detail of campaignDetails) {
//             if (!detail.CampaignId) continue;
//             const id = detail.CampaignId.toString();
//             if (!campaignIdGroups.has(id)) campaignIdGroups.set(id, []);
//             campaignIdGroups.get(id)!.push(detail);
//         }

//         const campaignIds = Array.from(campaignIdGroups.keys()).map(id => BigInt(id));

//         // Step 2: Get campaigns that match these CampaignIds
//         const adsCampaigns = await prisma.ads_Campaigns.findMany({
//             where: {
//                 CampaignId: {
//                     in: campaignIds
//                 }
//             },
//             select: {
//                 CampaignId: true,
//                 Name: true,
//                 OptimizationScore: true,
//                 BiddingStrategyType: true,
//                 SNo: true
//             }
//         });

//         const sNos = adsCampaigns.map(c => c.SNo).filter((s): s is NonNullable<typeof s> => s !== null);

//         // Step 3: Get mappings from ArticleCampaignMappings using SNo
//         const mappings = await prisma.articleCampaignMappings.findMany({
//             where: {
//                 CampaignId: {
//                     in: sNos
//                 }
//             },
//             select: {
//                 CampaignId: true,
//                 ArticleId: true
//             }
//         });

//         const articleIds = mappings.map(m => m.ArticleId).filter((id): id is NonNullable<typeof id> => id !== null);

//         // Step 4: Get article details
//         const articleDetails = await prisma.articleDetails.findMany({
//             where: {
//                 Id: {
//                     in: articleIds
//                 }
//             },
//             select: {
//                 Id: true,
//                 Title: true,
//                 StyleId: true
//             }
//         });

//         const styleIds = articleDetails.map(a => a.StyleId).filter((id): id is NonNullable<typeof id> => id !== null);

//         const styles = await prisma.styleIds.findMany({
//             where: {
//                 Id: {
//                     in: styleIds
//                 }
//             },
//             select: {
//                 Id: true,
//                 Name: true
//             }
//         });

//         const articleMap = new Map(articleDetails.map(a => [a.Id, a]));
//         const styleMap = new Map(styles.map(s => [s.Id, s.Name]));
//         const mappingMap = new Map(
//             mappings
//                 .filter(m => m.CampaignId !== null && m.ArticleId !== null)
//                 .map(m => [m.CampaignId!.toString(), m.ArticleId!])
//         );

//         const results = adsCampaigns
//             .filter(camp => camp.CampaignId !== null && camp.SNo !== null)
//             .map(camp => {
//                 const campaignIdStr = camp.CampaignId!.toString();
//                 const articleId = mappingMap.get(camp.SNo!.toString());
//                 const article = articleMap.get(articleId!);
//                 const styleName = article?.StyleId ? styleMap.get(article.StyleId) : "N/A";

//                 const details = campaignIdGroups.get(campaignIdStr) || [];
//                 const aggregated = details.reduce((acc, curr) => {
//                     acc.Clicks += Number(curr.Clicks || 0);
//                     acc.Impressions += Number(curr.Impressions || 0);
//                     acc.InvalidClicks += Number(curr.InvalidClicks || 0);
//                     acc.Conversions += Number(curr.Conversions || 0);
//                     return acc;
//                 }, {
//                     Clicks: 0,
//                     Impressions: 0,
//                     InvalidClicks: 0,
//                     Conversions: 0,
//                     CTR: 0,
//                     AverageCPC: 0,
//                     AverageCost: 0,
//                     CostPerConversion: 0
//                 });

//                 if (details.length > 0) {
//                     aggregated.CTR = details.reduce((sum, d) => sum + Number(d.CTR || 0), 0) / details.length;
//                     aggregated.AverageCPC = details.reduce((sum, d) => sum + Number(d.AverageCPC || 0), 0) / details.length;
//                     aggregated.AverageCost = details.reduce((sum, d) => sum + Number(d.AverageCost || 0), 0) / details.length;
//                     aggregated.CostPerConversion = details.reduce((sum, d) => sum + Number(d.CostPerConversion || 0), 0) / details.length;
//                 }

//                 return {
//                     ArticleId: article?.Id || "N/A",
//                     ArticleName: article?.Title || "N/A",
//                     StyleName: styleName || "N/A",
//                     CampaignId: campaignIdStr,
//                     Name: camp.Name || "N/A",
//                     OptimizationScore: camp.OptimizationScore || 0,
//                     BiddingStrategyType: camp.BiddingStrategyType || "N/A",
//                     Clicks: aggregated.Clicks,
//                     Impressions: aggregated.Impressions.toString(),
//                     CTR: aggregated.CTR,
//                     AverageCPC: aggregated.AverageCPC,
//                     AverageCost: aggregated.AverageCost,
//                     InvalidClicks: aggregated.InvalidClicks.toString(),
//                     Conversions: aggregated.Conversions,
//                     CostPerConversion: aggregated.CostPerConversion
//                 };
//             });

//         let filteredResults = results;
//         if (search) {
//             filteredResults = results.filter(item =>
//                 item.CampaignId.toLowerCase().includes(search) ||
//                 item.Name.toLowerCase().includes(search) ||
//                 item.BiddingStrategyType.toLowerCase().includes(search)
//             );
//         }

//         filteredResults.sort((a, b) => {
//             let comparison = 0;
//             switch (orderBy) {
//                 case "CampaignId":
//                     comparison = String(a.CampaignId).localeCompare(String(b.CampaignId));
//                     break;
//                 case "Name":
//                     comparison = a.Name.localeCompare(b.Name);
//                     break;
//                 case "OptimizationScore":
//                     comparison = Number(a.OptimizationScore) > Number(b.OptimizationScore) ? 1 : -1;
//                     break;
//                 case "Impressions":
//                     comparison = Number(a.Impressions) > Number(b.Impressions) ? 1 : -1;
//                     break;
//                 case "Clicks":
//                     comparison = a.Clicks > b.Clicks ? 1 : -1;
//                     break;
//                 default:
//                     comparison = a.Name.localeCompare(b.Name);
//             }
//             return orderDir.toLowerCase() === "desc" ? -comparison : comparison;
//         });

//         const recordsTotal = filteredResults.length;
//         const paginatedData = length === -1
//             ? filteredResults
//             : filteredResults.slice((start - 1) * length, start * length);

//         return NextResponse.json({
//             success: true,
//             data: paginatedData,
//             pagination: {
//                 draw: parseInt(searchParams.get("draw") || "1"),
//                 recordsTotal,
//                 recordsFiltered: recordsTotal,
//                 currentPageCount: paginatedData.length,
//                 start,
//                 length,
//                 currentPage: start,
//                 totalPages: length === -1 ? 1 : Math.ceil(recordsTotal / length),
//                 hasNextPage: length === -1 ? false : start * length < recordsTotal,
//                 hasPreviousPage: start > 1,
//             },
//         });

//     } catch (error) {
//         console.error("Error fetching campaign details:", error);
//         return NextResponse.json({ error: "Failed to fetch campaign details" }, { status: 500 });
//     } finally {
//         await prisma.$disconnect();
//     }
// }



import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const articleId = searchParams.get('articleId');
        const startDateStr = searchParams.get('startDate');
        const endDateStr = searchParams.get('endDate');
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const orderBy = searchParams.get("orderBy") || "SegmentDate";
        const orderDir = searchParams.get("orderDir") || "asc";
        const search = searchParams.get("q")?.toLowerCase();

        if (!articleId || !/^[0-9a-fA-F\-]{36}$/.test(articleId)) {
            return NextResponse.json({ error: 'Valid articleId (UUID) is required' }, { status: 400 });
        }

        const startDate = new Date(startDateStr || '');
        const endDate = new Date(endDateStr || '');
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return NextResponse.json({ error: 'Valid startDate and endDate are required' }, { status: 400 });
        }

        const articleDetail = await prisma.articleDetails.findUnique({
            where: { Id: articleId },
            select: { Title: true, StyleIdLm: true, StyleIdDm: true }
        });

        let styleName = 'N/A';
        if (articleDetail?.StyleIdLm) {
            const style = await prisma.styleIds.findUnique({
                where: { Id: articleDetail.StyleIdLm },
                select: { Name: true }
            });
            styleName = style?.Name || 'N/A';
        } else if (articleDetail?.StyleIdDm) {
            const style = await prisma.styleIds.findUnique({
                where: { Id: articleDetail.StyleIdDm },
                select: { Name: true }
            });
            styleName = style?.Name || 'N/A';
        }

        const mappings = await prisma.articleCampaignMappings.findMany({
            where: {
                ArticleId: articleId,
                CampaignId: { not: null }
            },
            include: {
                Ads_Campaigns: {
                    select: {
                        CampaignId: true,
                        Name: true,
                        OptimizationScore: true,
                        BiddingStrategyType: true
                    }
                }
            }
        });

        const uniqueCampaignIds = new Set<string>();
        const results: any[] = [];

        for (const mapping of mappings) {
            const campaign = mapping.Ads_Campaigns;
            if (!campaign || campaign.CampaignId === null) continue;

            const campaignId = campaign.CampaignId.toString();
            if (uniqueCampaignIds.has(campaignId)) continue;
            uniqueCampaignIds.add(campaignId);

            const segmentDetails = await prisma.ads_CampaignDetails.findMany({
                where: {
                    CampaignId: campaign.CampaignId,
                    SegmentDate: {
                        gte: startDate,
                        lte: endDate
                    }
                },
                select: {
                    SegmentDate: true,
                    Clicks: true,
                    Impressions: true,
                    CTR: true,
                    AverageCPC: true,
                    AverageCost: true,
                    InvalidClicks: true,
                    Conversions: true,
                    CostPerConversion: true
                }
            });

            for (const segment of segmentDetails) {
                results.push({
                    CampaignId: campaignId,
                    Name: campaign.Name,
                    SegmentDate: segment.SegmentDate,
                    ArticleName: articleDetail?.Title || 'N/A',
                    StyleName: styleName,
                    Clicks: segment.Clicks,
                    Impressions: segment.Impressions?.toString() || '0',
                    CTR: segment.CTR,
                    AvgCPC: segment.AverageCPC,
                    AvgCost: segment.AverageCost,
                    InvalidClicks: segment.InvalidClicks?.toString() || '0',
                    Conversions: segment.Conversions,
                    CostPerConversions: segment.CostPerConversion,
                    OptimizationScore: campaign.OptimizationScore,
                    BidStrategyType: campaign.BiddingStrategyType
                });
            }
        }

        // 🔍 Filter by search across all fields
        let filteredResults = results;
        if (search) {
            filteredResults = results.filter(item =>
                Object.values(item).some(val =>
                    val !== null &&
                    val !== undefined &&
                    val.toString().toLowerCase().includes(search)
                )
            );
        }

        // 🔃 Sort dynamically by any field
        filteredResults.sort((a, b) => {
            let valA = a[orderBy];
            let valB = b[orderBy];

            if (valA === undefined || valB === undefined) return 0;

            if (typeof valA === 'bigint') valA = Number(valA);
            if (typeof valB === 'bigint') valB = Number(valB);

            if (typeof valA === 'string' && !isNaN(Date.parse(valA))) {
                valA = new Date(valA);
                valB = new Date(valB);
            }

            const comparison = valA > valB ? 1 : valA < valB ? -1 : 0;
            return orderDir.toLowerCase() === 'desc' ? -comparison : comparison;
        });

        // 📄 Pagination
        const recordsTotal = filteredResults.length;
        const paginatedData = length === -1
            ? filteredResults
            : filteredResults.slice((start - 1) * length, start * length);

        return NextResponse.json({
            success: true,
            data: paginatedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsTotal,
                recordsFiltered: recordsTotal,
                currentPageCount: paginatedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsTotal / length),
                hasNextPage: length === -1 ? false : start * length < recordsTotal,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error('Error fetching campaign data:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    } 
}