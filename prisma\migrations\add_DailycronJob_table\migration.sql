-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS "DailyCronJob" (
  id SERIAL PRIMARY KEY,
  "job_name" VARCHAR(255) NOT NULL,
  "start_time" TIMESTAMP NOT NULL,
  "end_time" TIMES<PERSON>MP NULL,
  "status" VARCHAR(20) NOT NULL CHECK ("status" IN ('running', 'completed', 'failed')) DEFAULT 'running',
  "error_message" TEXT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_dailycronjob_job_name ON "Daily<PERSON>ronJob" ("job_name");
CREATE INDEX IF NOT EXISTS idx_dailycronjob_start_time ON "DailyCronJob" ("start_time");
CREATE INDEX IF NOT EXISTS idx_dailycronjob_status ON "DailyCronJob" ("status");
CREATE INDEX IF NOT EXISTS idx_dailycronjob_created_at ON "DailyCronJob" ("created_at");
