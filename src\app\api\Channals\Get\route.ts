import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q")?.toLowerCase();
        const orderBy = searchParams.get("orderBy") || "DisplayName";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        let allChannels = await prisma.channals.findMany({
            // where: { Active: true },
            select: {
                Id: true,
                DisplayName: true,
                ReportingDimensionId: true,
            },
        });

        let dataWithCustomChannelId = allChannels.map(channel => {
            const parts = (channel.ReportingDimensionId ?? "").split(':');
            const customChannelId = parts[parts.length - 1];
            return {
                Id: channel.Id,
                DisplayName: channel.DisplayName,
                CustomChannelId: customChannelId,
            };
        });

        if (search) {
            dataWithCustomChannelId = dataWithCustomChannelId.filter(channel =>
                channel.CustomChannelId?.toLowerCase().includes(search) ||
                channel.DisplayName?.toLowerCase().includes(search)
            );
        }

        if (orderBy === 'CustomChannelId') {
            dataWithCustomChannelId.sort((a, b) => {
                const aVal = a.CustomChannelId || '';
                const bVal = b.CustomChannelId || '';
                return orderDir === 'asc'
                    ? aVal.localeCompare(bVal)
                    : bVal.localeCompare(aVal);
            });
        } else {
            dataWithCustomChannelId.sort((a, b) => {
                const aVal = (a as any)[orderBy] || '';
                const bVal = (b as any)[orderBy] || '';
                
                if (typeof aVal === 'string' && typeof bVal === 'string') {
                    return orderDir === 'asc'
                        ? aVal.localeCompare(bVal)
                        : bVal.localeCompare(aVal);
                }
                
                if (aVal < bVal) return orderDir === 'asc' ? -1 : 1;
                if (aVal > bVal) return orderDir === 'asc' ? 1 : -1;
                return 0;
            });
        }

        const recordsFiltered = dataWithCustomChannelId.length;
        const recordsTotal = allChannels.length;

        let paginatedData = dataWithCustomChannelId;
        if (length !== -1) {
            const skipCount = (start === 1 ? 0 : start - 1) * length;
            paginatedData = dataWithCustomChannelId.slice(skipCount, skipCount + length);
        }

        return NextResponse.json({
            success: true,
            data: paginatedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                recordsTotal,
                currentPageCount: paginatedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Failed to fetch users" },
            { status: 500 }
        );
    } 
}