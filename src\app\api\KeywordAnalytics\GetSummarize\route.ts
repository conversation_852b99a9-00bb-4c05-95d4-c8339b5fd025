// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

// export async function GET(req: NextRequest) {
//     try {
//         // const user = await verifyToken(req);
//         // if (!user) {
//         //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         // }

//         const { searchParams } = new URL(req.url);
//         const start = parseInt(searchParams.get("page") || "1");
//         const length = parseInt(searchParams.get("length") || "-1");
//         const searchRaw = searchParams.get("q");
//         const search = searchRaw ? searchRaw.trim() : null;
//         const orderBy = searchParams.get("orderBy") || "ClickCount";
//         const orderDir = searchParams.get("orderDir") || "desc";
//         const startDate = searchParams.get("startDate");
//         const endDate = searchParams.get("endDate");
//         const url = searchParams.get("url");
//         const domain = searchParams.get("domain");

//         if (!startDate || !endDate) {
//             return NextResponse.json({ error: "startDate and endDate are required." }, { status: 400 });
//         }

//         if (!url || !domain) {
//             return NextResponse.json({ error: "url and domain are required." }, { status: 400 });
//         }

//         const startDateObj = new Date(startDate.split(" ")[0]); 
//         const endDateObj = new Date(endDate.split(" ")[0]);
//         endDateObj.setHours(23, 59, 59, 999);

//         interface RawQueryResult {
//             URL: string;
//             Keyword: string;
//             Domain: string;
//             Created_At_Date: string | Date;
//             Count_Sum: bigint | number;
//             AdsClickCounter_Sum: bigint | number;
//         }

//         const queryParams = [url, domain, startDateObj, endDateObj];
//         let searchCondition = '';

//         if (search) {
//             const searchPattern = `%${search}%`;
//             searchCondition = `AND (
//                 "URL" ILIKE $5 OR 
//                 "Keyword" ILIKE $6 OR 
//                 "IpAddress" ILIKE $7 OR 
//                 "Domain" ILIKE $8
//             )`;
//             queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
//         }

//         // Determine ORDER BY clause
//         let orderByClause: string;
//         switch (orderBy) {
//             case 'ClickCount':
//                 orderByClause = 'SUM("Count")';
//                 break;
//             case 'AdsClickCount':
//                 orderByClause = 'SUM("AdsClickCounter")';
//                 break;
//             case 'URL':
//                 orderByClause = '"URL"';
//                 break;
//             case 'Keyword':
//                 orderByClause = '"Keyword"';
//                 break;
//             case 'StartDate':
//                 orderByClause = 'DATE("Created_At")';
//                 break;
//             default:
//                 orderByClause = '"Domain"';
//         }

//         const orderDirection = orderDir === 'asc' ? 'ASC' : 'DESC';

//         const query = `
//             SELECT 
//                 "URL",
//                 "Keyword", 
//                 "Domain",
//                 DATE("Created_At") as "Created_At_Date",
//                 SUM("Count") as "Count_Sum",
//                 SUM("AdsClickCounter") as "AdsClickCounter_Sum"
//             FROM "QueryAnalytics"
//             WHERE 
//                 "URL" = $1 AND
//                 "Domain" = $2 AND
//                 "Created_At" >= $3 AND
//                 "Created_At" <= $4
//                 ${searchCondition}
//             GROUP BY "URL", "Keyword", "Domain", DATE("Created_At")
//             ORDER BY ${orderByClause} ${orderDirection}
//         `;

//         const aggregatedDataRaw = await prisma.$queryRawUnsafe(query, ...queryParams) as RawQueryResult[];

//         let transformedData = aggregatedDataRaw.map((item) => {
//             const clickCount = Number(item.Count_Sum) || 0;
//             const adsClickCount = Number(item.AdsClickCounter_Sum) || 0;
//             const adsPerCount = clickCount > 0 ? (adsClickCount / clickCount) * 100 : 0;

//             return {
//                 ClickCount: clickCount,
//                 AdsClickCount: adsClickCount,
//                 AdsPerCount: parseFloat(adsPerCount.toFixed(2)),
//                 URL: item.URL,
//                 Keyword: item.Keyword,
//                 Domain: item.Domain,
//                 StartDate: item.Created_At_Date instanceof Date 
//                     ? item.Created_At_Date.toISOString().split('T')[0] 
//                     : item.Created_At_Date
//             };
//         });

//         interface TransformedDataItem {
//             ClickCount: number;
//             AdsClickCount: number;
//             AdsPerCount: number;
//             URL: string;
//             Keyword: string;
//             Domain: string;
//             StartDate: string | null;
//         }

//         if (orderBy === 'ClickCount') {
//             transformedData.sort((a: TransformedDataItem, b: TransformedDataItem) => {
//                 if (a.ClickCount >= 50 || b.ClickCount >= 50) {
//                     const aValue = a.ClickCount >= 50 ? a.AdsPerCount : a.ClickCount;
//                     const bValue = b.ClickCount >= 50 ? b.AdsPerCount : b.ClickCount;
//                     return orderDir === 'asc' ? aValue - bValue : bValue - aValue;
//                 }
//                 return orderDir === 'asc' ? a.ClickCount - b.ClickCount : b.ClickCount - a.ClickCount;
//             });
//         }

//         const totalRecords = transformedData.length;

//         let paginatedData = transformedData;
//         // if (length !== -1) {
//         //     const skip = (start - 1) * length;
//         //     paginatedData = transformedData.slice(skip, skip + length);
//         // }

//         return NextResponse.json({
//             success: true,
//             data: paginatedData,
//         });

//     } catch (error) {
//         console.error("Error processing request:", error);
//         return NextResponse.json(
//             {
//                 error: "Internal Server Error",
//                 details: error instanceof Error ? error.message : String(error)
//             },
//             { status: 500 }
//         );
//     }
// }



import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';

interface TransformedDataItem {
    ClickCount: number;
    AdsClickCount: number;
    AdsPerCount: number;
    URL: string;
    Keyword: string;
    Domain: string;
    Date: string;
}

export async function GET(req: NextRequest) {
    const client = new Client({
        connectionString: process.env.DATABASE_URL_API,
        ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    });

    try {
        await client.connect();

        const { searchParams } = new URL(req.url);
        const orderBy = searchParams.get('orderBy') || 'Count_Sum';
        const orderDir = (searchParams.get('orderDir') || 'desc')
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const url = searchParams.get("url");
        const domain = searchParams.get("domain");
        const search = searchParams.get("search");

        if (!startDate || !endDate) {
            return NextResponse.json({ success: false, error: "startDate and endDate are required." }, { status: 400 });
        }

        if (!url || !domain) {
            return NextResponse.json({ success: false, error: "url and domain are required." }, { status: 400 });
        }

        await client.query('BEGIN');
        await client.query('SET TRANSACTION ISOLATION LEVEL READ COMMITTED');

        let searchCondition = '';
        let queryParams: any[] = [url, domain];

        if (search) {
            searchCondition = ` AND "Keyword" ILIKE $5`;
            queryParams.push(`%${search}%`);
        }

        const startDateTime = new Date(startDate + 'T00:00:00.000Z').toISOString();
        const endDateTime = new Date(endDate + 'T23:59:59.999Z').toISOString();
        queryParams.push(startDateTime, endDateTime);

        const orderByClause = getOrderByClause(orderBy);
        const orderDirection = orderDir === 'asc' ? 'ASC' : 'DESC';

        const query = `
            SELECT 
                "URL",
                "Keyword", 
                "Domain",
                DATE("Created_At_Date") as "Created_At_Date",
                SUM("Count_Sum") as "Count_Sum",
                SUM("AdsClickCounter_Sum") as "AdsClickCounter_Sum"
            FROM vw_cleaned_query_analytics
            WHERE 
                "URL" = $1 AND
                "Domain" = $2 AND
                "Created_At_Date" >= $3 AND
                "Created_At_Date" <= $4
                ${searchCondition}
            GROUP BY "URL", "Keyword", "Domain", DATE("Created_At_Date")
            ORDER BY ${orderByClause} ${orderDirection}
            LIMIT 100
        `;

        const result = await client.query(query, queryParams);
        await client.query('COMMIT');

        let transformedData: TransformedDataItem[] = result.rows.map((item: any) => {
            const clickCount = Number(item.Count_Sum) || 0;
            const adsClickCount = Number(item.AdsClickCounter_Sum) || 0;
            const adsPerCount = clickCount > 0 ? (adsClickCount / clickCount) * 100 : 0;
            const dateOnly = item.Created_At_Date ? new Date(item.Created_At_Date).toISOString().split('T')[0] : '';
            return {
                ClickCount: clickCount,
                AdsClickCount: adsClickCount,
                AdsPerCount: parseFloat(adsPerCount.toFixed(2)),
                URL: item.URL,
                Keyword: item.Keyword || '',
                Domain: item.Domain,
                Date: dateOnly
            };
        });

        if (orderBy === 'Count_Sum') {
            transformedData.sort((a: TransformedDataItem, b: TransformedDataItem) => {
                if (a.ClickCount >= 50 && b.ClickCount >= 50) {
                    return orderDir === 'asc' ? a.AdsPerCount - b.AdsPerCount : b.AdsPerCount - a.AdsPerCount;
                }
                else if (a.ClickCount >= 50) {
                    return orderDir === 'asc' ? 1 : -1;
                }
                else if (b.ClickCount >= 50) {
                    return orderDir === 'asc' ? -1 : 1;
                }
                else {
                    return orderDir === 'asc' ? a.ClickCount - b.ClickCount : b.ClickCount - a.ClickCount;
                }
            });
        }

        return NextResponse.json({ success: true, data: transformedData }, { status: 200 });
    } catch (error) {
        console.error('❌ Error fetching referral data:', error);
        try {
            await client.query('ROLLBACK');
        } catch (rollbackError) {
            console.error('❌ Failed to rollback transaction:', rollbackError);
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return NextResponse.json({ success: false, error: 'Internal Server Error', details: errorMessage }, { status: 500 });
    } finally {
        try {
            await client.end();
        } catch (endError) {
            console.error('❌ Failed to close database connection:', endError);
        }
    }
}

function getOrderByClause(orderBy: string): string {
    switch (orderBy) {
        case 'ClickCount':
            return 'SUM("Count_Sum")';
        case 'AdsClickCount':
            return 'SUM("AdsClickCounter_Sum")';
        case 'URL':
            return '"URL"';
        case 'Domain':
            return '"Domain"';
        case 'Keyword':
            return '"Keyword"';
        case 'Date':
            return 'DATE("Created_At_Date")';
        default:
            return 'SUM("Count_Sum")';
    }
}