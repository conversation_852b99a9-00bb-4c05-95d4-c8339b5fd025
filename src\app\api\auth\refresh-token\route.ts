import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify, SignJWT } from 'jose';

export async function GET(req: NextRequest) {
  try {
    const refreshToken = req.cookies.get('refreshToken')?.value;
    
    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Refresh token not found' },
        { status: 401 }
      );
    }

    const secret = new TextEncoder().encode(process.env.JWT_SECRET!);
    
    try {
      const { payload } = await jwtVerify(refreshToken, secret) as { payload: any };
      
      const newAccessToken = await new SignJWT({
        Id: payload.Id,
        Email: payload.Email,
        User_Type: payload.User_Type,
        Name: payload.Name,
        ProfilePic: payload.ProfilePic,
      })
        .setProtectedHeader({ alg: 'HS256' })
        .setExpirationTime('15m')
        .setIssuedAt()
        .sign(secret);

      const response = NextResponse.json({ success: true });
      
      response.cookies.set('accessToken', newAccessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 15 * 60, 
      });

      return response;
    } catch (error) {
      console.error('Refresh token verification failed:', error);
      const response = NextResponse.json(
        { error: 'Invalid refresh token' },
        { status: 401 }
      );
      
      response.cookies.set('accessToken', '', { maxAge: 0, path: '/' });
      response.cookies.set('refreshToken', '', { maxAge: 0, path: '/' });
      
      return response;
    }
  } catch (error) {
    console.error('Refresh token error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}