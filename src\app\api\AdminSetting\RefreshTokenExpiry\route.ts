import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);

        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const setting = await prisma.adminUserSetting.findFirst({
            select: {
                CampaignRefreshTokenExpiresIn: true
            }
        });
        console.log(setting);

        if (!setting || !setting.CampaignRefreshTokenExpiresIn) {
            return NextResponse.json({
                success: true,
                Data: [],
            });
        }

        const expirationDate = new Date(setting.CampaignRefreshTokenExpiresIn);
        const formattedDate = expirationDate.toDateString() + ' ' + expirationDate.toTimeString().split(' ')[0];

        const thresholdDate = new Date(expirationDate.getTime() - (86400 + 3600) * 1000);

        const now = new Date();

        if (now >= thresholdDate) {
            return NextResponse.json({
                success: true,
                Data: `Refresh token expires on ${formattedDate}`,
            });
        } else {
            return NextResponse.json({
                success: false,
                Data: [],
            });
        }

    } catch (error) {
        console.error('Error handling admin user setting:', error);
        return NextResponse.json({
            error: 'Failed to process admin user setting',
            details: process.env.NODE_ENV === 'development' ? error : undefined
        }, { status: 500 });
    }
};
