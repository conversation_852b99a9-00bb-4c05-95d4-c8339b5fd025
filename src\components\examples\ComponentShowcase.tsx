"use client";

import React, { useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaUsers } from 'react-icons/fa';
import {
  PageContainer,
  ContentCard,
  ActionBar,
  Button,
  BaseModal,
  FormModal,
  ConfirmationModal,
  LoadingSpinner,
  EmptyState,
  InputGroup,
  SearchableDropdown,
  Checkbox,
} from '@/components/ui';
import { useForm, validationRules, combineRules } from '@/hooks';

const ComponentShowcase: React.FC = () => {
  // State for different modals
  const [baseModalOpen, setBaseModalOpen] = useState(false);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Sample data for dropdowns
  const sampleOptions = [
    { Id: '1', Name: 'Option 1' },
    { Id: '2', Name: 'Option 2' },
    { Id: '3', Name: 'Option 3' },
  ];

  // Form management
  const { values, errors, handleChange, handleBlur, validateForm, reset } = useForm({
    initialValues: {
      name: '',
      email: '',
      type: '',
      active: false,
    },
    validationRules: {
      name: combineRules(
        validationRules.required('Name is required'),
        validationRules.minLength(2, 'Name must be at least 2 characters')
      ),
      email: combineRules(
        validationRules.required('Email is required'),
        validationRules.email()
      ),
      type: validationRules.required('Type is required'),
    },
  });

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setLoading(false);
        setFormModalOpen(false);
        reset();
        alert('Form submitted successfully!');
      }, 2000);
    }
  };

  const handleConfirmAction = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setConfirmModalOpen(false);
      alert('Action confirmed!');
    }, 1500);
  };

  return (
    <PageContainer
      title="Component Showcase"
      subtitle="Demonstration of all new modular components"
    >
      {/* Action Bar Example */}
      <ContentCard title="Action Bar Examples" className="mb-6">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Left Aligned Actions</h4>
            <ActionBar
              actions={[
                {
                  label: 'Primary Action',
                  onClick: () => alert('Primary clicked'),
                  variant: 'primary',
                  icon: <FaPlus />,
                },
                {
                  label: 'Secondary Action',
                  onClick: () => alert('Secondary clicked'),
                  variant: 'outlinePrimary',
                  icon: <FaEdit />,
                },
              ]}
            />
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Right Aligned Actions</h4>
            <ActionBar
              align="right"
              actions={[
                {
                  label: 'Delete',
                  onClick: () => alert('Delete clicked'),
                  variant: 'danger',
                  icon: <FaTrash />,
                },
              ]}
            />
          </div>
        </div>
      </ContentCard>

      {/* Modal Examples */}
      <ContentCard title="Modal Examples" className="mb-6">
        <ActionBar
          actions={[
            {
              label: 'Base Modal',
              onClick: () => setBaseModalOpen(true),
              variant: 'primary',
            },
            {
              label: 'Form Modal',
              onClick: () => setFormModalOpen(true),
              variant: 'green',
            },
            {
              label: 'Confirmation Modal',
              onClick: () => setConfirmModalOpen(true),
              variant: 'danger',
            },
          ]}
        />
      </ContentCard>

      {/* Loading and Empty States */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <ContentCard title="Loading Spinner">
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" text="Loading data..." />
          </div>
        </ContentCard>

        <ContentCard title="Empty State">
          <EmptyState
            icon={<FaUsers />}
            title="No users found"
            description="Get started by adding your first user to the system."
            action={{
              label: 'Add User',
              onClick: () => alert('Add user clicked'),
              icon: <FaPlus />,
            }}
            size="sm"
          />
        </ContentCard>
      </div>

      {/* Content Card Variants */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <ContentCard title="Default Card" variant="default">
          <p className="text-gray-600">This is a default content card.</p>
        </ContentCard>

        <ContentCard title="Outlined Card" variant="outlined">
          <p className="text-gray-600">This is an outlined content card.</p>
        </ContentCard>

        <ContentCard title="Elevated Card" variant="elevated">
          <p className="text-gray-600">This is an elevated content card.</p>
        </ContentCard>
      </div>

      {/* Base Modal */}
      <BaseModal
        isOpen={baseModalOpen}
        onClose={() => setBaseModalOpen(false)}
        title="Base Modal Example"
        actions={
          <>
            <Button
              label="Cancel"
              variant="outlineDark"
              shape="rounded"
              onClick={() => setBaseModalOpen(false)}
            />
            <Button
              label="Save"
              variant="primary"
              shape="rounded"
              onClick={() => setBaseModalOpen(false)}
            />
          </>
        }
      >
        <p className="text-gray-600">
          This is a base modal component that can be customized with any content and actions.
        </p>
      </BaseModal>

      {/* Form Modal */}
      <FormModal
        isOpen={formModalOpen}
        onClose={() => {
          setFormModalOpen(false);
          reset();
        }}
        title="Form Modal Example"
        onSubmit={handleFormSubmit}
        isLoading={loading}
        submitLabel="Submit Form"
      >
        <div className="space-y-4">
          <InputGroup
            label="Name"
            type="text"
            name="name"
            value={values.name}
            handleChange={handleChange}
            onBlur={handleBlur}
            placeholder="Enter your name"
            required
            error={errors.name}
          />

          <InputGroup
            label="Email"
            type="email"
            name="email"
            value={values.email}
            handleChange={handleChange}
            onBlur={handleBlur}
            placeholder="Enter your email"
            required
            error={errors.email}
          />

          <SearchableDropdown
            label="Type"
            options={sampleOptions}
            value={values.type}
            onChange={(option) => handleChange({
              target: { name: 'type', value: option?.Id || '' }
            } as any)}
            placeholder="Select a type"
            displayKey="Name"
            idKey="Id"
          />

          <Checkbox
            label="Active"
            name="active"
            checked={values.active}
            onChange={handleChange}
            withIcon="check"
            withBg
            radius="md"
          />
        </div>
      </FormModal>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        onConfirm={handleConfirmAction}
        title="Confirm Action"
        message="Are you sure you want to perform this action? This cannot be undone."
        confirmLabel="Yes, Continue"
        confirmVariant="danger"
        isLoading={loading}
        icon={<FaTrash />}
      />
    </PageContainer>
  );
};

export default ComponentShowcase;
