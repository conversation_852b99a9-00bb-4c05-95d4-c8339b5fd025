-- FUNCTION: public.fn_ads_revenue_data_test(text, text, date, date, text, boolean, boolean, text)

-- DROP FUNCTION IF EXISTS public.fn_ads_revenue_data_test(text, text, date, date, text, boolean, boolean, text);

CREATE OR REPLACE FUNCTION public.fn_ads_revenue_data_test(
	p_article_id text,
	p_channel_id text,
	p_from_date date,
	p_to_date date,
	p_group_mode text,
	p_isnullable_data boolean,
	p_inr_to_usd boolean,
	p_style_id text)
    RETURNS void
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
AS $BODY$

DECLARE
    sql TEXT;
	selectefield TEXT := '';
    selectfiledforagg TEXT := '';
	selectfiledforgroupby TEXT := '';
    selectfieldforwherecls TEXT := '';
    selectfieldforjoin TEXT := '';
	nullable_where_clause  TEXT := '';
	today_rate numeric(10,2);
BEGIN

	nullable_where_clause  := 'WHERE
	COALESCE(rv."total_rv_revenue", 0) != 0 OR
	COALESCE(rv."total_rv_rpc", 0) != 0 OR
	COALESCE((rv."total_rv_revenue" - ads."total_ads_cost"), 0) != 0 OR
	COALESCE(ads."total_ads_conversions", 0) != 0 OR
	COALESCE(rv."total_rv_clicks", 0) != 0 OR
	COALESCE(ads."total_ads_cost", 0) != 0 OR
	COALESCE(ads."avg_ads_cpa", 0) != 0 OR
	COALESCE(ads."avg_ads_cpc", 0) != 0 OR
	COALESCE(ads."total_ads_clicks", 0) != 0';

	today_rate := 
		(SELECT ROUND("USDtoINR", 2)
		FROM "Ads_RateConversion" 
		WHERE "Date" = (SELECT MAX("Date") FROM "Ads_RateConversion"));

	-- Cleanup any existing temp table
    EXECUTE 'DROP TABLE IF EXISTS pg_temp.temp_ads_result';

		IF p_group_mode IS NULL OR p_group_mode = '' THEN
        EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := '';
        selectfiledforagg := '';
		selectfiledforgroupby := '';
        selectfieldforwherecls := ' TRUE ';
        selectfieldforjoin := '';

		ELSIF p_group_mode = 'date' THEN
        EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
			date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := ' segmentdate, ';
		selectfiledforgroupby := ' GROUP BY segmentdate';
        selectfieldforwherecls := ' rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := '';
		
		

		ELSIF p_group_mode = 'article' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            article_name text,
			articleid uuid,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, ' ;
        selectfiledforagg := ' articleid, ';
		selectfiledforgroupby := ' GROUP BY articleid';
        selectfieldforwherecls := ' rv.articleid = ads.articleid';
        selectfieldforjoin := ' LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';

	
	  	ELSIF p_group_mode = 'channel' THEN
        EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            channel_name text,
			channelid uuid,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, ';
        selectfiledforagg := ' channelid,';
		selectfiledforgroupby := ' GROUP BY channelid';
        selectfieldforwherecls := ' rv.channelid = ads.channelid';
        selectfieldforjoin := ' LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'country' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
			country text,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' COALESCE(rv.country, ads.country) AS country, ';
        selectfiledforagg := ' country, ' ;
		selectfiledforgroupby := ' GROUP BY country';
        selectfieldforwherecls := ' rv.country = ads.country';
        selectfieldforjoin := ' ';
    
		ELSIF p_group_mode = 'article,channel' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            article_name text,
			articleid uuid,
            channel_name text,
			channelid uuid,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
		ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, ';
        selectfiledforagg := ' articleid, channelid, ';
		selectfiledforgroupby := ' GROUP BY articleid, channelid ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'article,country' THEN
		EXECUTE '
	        CREATE TEMP TABLE pg_temp.temp_ads_result (
	            article_name text,
				articleid uuid,
				country text,
	            rv_revenue numeric,
	            rv_rpc numeric,
	            profit numeric,
	            roi numeric,
	            ads_conversions numeric,
	            ads_click numeric,
	            ads_spend numeric,
	            ads_cpa numeric,
	            ads_cpc numeric,
			spend_clicks numeric
	        )';
		selectefield := ' art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, COALESCE(rv.country, ads.country) AS country, ';
		selectfiledforagg := ' articleid, country, ';
		selectfiledforgroupby := ' GROUP BY articleid, country ';
		selectfieldforwherecls := ' rv.articleid = ads.articleid AND rv.country = ads.country';
		selectfieldforjoin :=  ' LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';

		ELSIF p_group_mode = 'article,date' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            article_name text,
			articleid uuid,
			date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, COALESCE(rv.segmentdate, ads.segmentdate) AS date, ' ;
        selectfiledforagg := ' articleid,segmentdate, ';
		selectfiledforgroupby := ' GROUP BY articleid,segmentdate';
        selectfieldforwherecls := ' rv.articleid = ads.articleid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';

		 ELSIF p_group_mode = 'channel,country' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            channel_name text,
			channelid uuid,
			country text,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, COALESCE(rv.country, ads.country) AS country, ';
        selectfiledforagg := ' channelid, country, ';
		selectfiledforgroupby := ' GROUP BY channelid, country';
        selectfieldforwherecls := 'rv.channelid = ads.channelid AND rv.country = ads.country';
        selectfieldforjoin := 'LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'channel,date' THEN
        EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            channel_name text,
			channelid uuid,
			date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid,COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' channelid,segmentdate,';
		selectfiledforgroupby := ' GROUP BY channelid, segmentdate';
        selectfieldforwherecls := ' rv.channelid = ads.channelid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'country,date' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
			country text,
			date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' COALESCE(rv.country, ads.country) AS country,COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' country,segmentdate, ' ;
		selectfiledforgroupby := ' GROUP BY country, segmentdate';
        selectfieldforwherecls := ' rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := ' ';

		ELSIF p_group_mode = 'article,channel,date' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            article_name text,
			articleid uuid,
            channel_name text,
			channelid uuid,
			date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
		selectefield := ' art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, 
		ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid , COALESCE(rv.segmentdate, ads.segmentdate) AS date, ';
        selectfiledforagg := ' articleid, channelid, segmentdate, ';
		selectfiledforgroupby := ' GROUP BY articleid, channelid, segmentdate ';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		 ELSIF p_group_mode = 'channel,country,date' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            channel_name text,
			channelid uuid,
			country text,
            date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := 'ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'channelid, country, segmentdate,';
		selectfiledforgroupby := ' GROUP BY channelid, country, segmentdate';
        selectfieldforwherecls := 'rv.channelid = ads.channelid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'article,country,date' THEN
		EXECUTE '
	        CREATE TEMP TABLE pg_temp.temp_ads_result (
	            article_name text,
				articleid uuid,
				country text,
	            date date,
	            rv_revenue numeric,
	            rv_rpc numeric,
	            profit numeric,
	            roi numeric,
	            ads_conversions numeric,
	            ads_click numeric,
	            ads_spend numeric,
	            ads_cpa numeric,
	            ads_cpc numeric,
			spend_clicks numeric
	        )';
		selectefield := 'art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, COALESCE(rv.country, ads.country) AS country
		, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
		selectfiledforagg := 'articleid, country, segmentdate, ';
		selectfiledforgroupby := ' GROUP BY articleid, country, segmentdate';
		selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
		selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid)';
		

	  	ELSIF p_group_mode = 'article,channel,country' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            article_name text,
			articleid uuid,
            channel_name text,
			channelid uuid,
			country text,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := ' art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, COALESCE(rv.country, ads.country) AS country, ';
        selectfiledforagg := 'channelid, articleid, country,';
		selectfiledforgroupby := ' GROUP BY channelid, articleid, country';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.country = ads.country';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';

		ELSIF p_group_mode = 'article,channel,country,date' THEN
		EXECUTE '
        CREATE TEMP TABLE pg_temp.temp_ads_result (
            article_name text,
			articleid uuid,
            channel_name text,
			channelid uuid,
			country text,
            date date,
            rv_revenue numeric,
            rv_rpc numeric,
            profit numeric,
            roi numeric,
            ads_conversions numeric,
            ads_click numeric,
            ads_spend numeric,
            ads_cpa numeric,
            ads_cpc numeric,
			spend_clicks numeric
        )';
        selectefield := 'art."Title" AS article_name, COALESCE(rv.articleid, ads.articleid) AS articleid, ch."DisplayName" AS channel_name, COALESCE(rv.channelid, ads.channelid) AS channelid, COALESCE(rv.country, ads.country) AS country, COALESCE(rv.segmentdate, ads.segmentdate) AS date,';
        selectfiledforagg := 'channelid, articleid, country, segmentdate,';
		selectfiledforgroupby := ' GROUP BY channelid, articleid, country, segmentdate';
        selectfieldforwherecls := 'rv.articleid = ads.articleid AND rv.channelid = ads.channelid AND rv.country = ads.country AND rv.segmentdate = ads.segmentdate';
        selectfieldforjoin := 'LEFT JOIN "ArticleDetails" art ON art."Id" = COALESCE(rv.articleid, ads.articleid) LEFT JOIN "Channals" ch ON ch."Id" = COALESCE(rv.channelid, ads.channelid)';
    

		  ELSE
        RAISE EXCEPTION 'Unsupported group mode: %', p_group_mode;
    END IF;

	 -- Append nullable condition if needed
    IF p_isnullable_data THEN
        selectfieldforjoin := selectfieldforjoin || ' ' || nullable_where_clause;
    END IF;

    sql := format($fmt$
        WITH rv_agg AS (
            SELECT %s  --select field
                SUM(rv_revenue) AS total_rv_revenue,
				 case when SUM(rv_clicks) > 0 then SUM(rv_revenue) / SUM(rv_clicks) else 0 end AS total_rv_rpc,
                --SUM(rv_rpc) AS total_rv_rpc,
				SUM(rv_clicks) AS total_rv_clicks
            FROM fn_get_revenue_details_test($1,$2, $3, $4,$8)
            %s  --GROUP BY 
        ),
        ads_agg AS (
            SELECT %s  --select field
                SUM(ads_cost) AS total_ads_cost,
                SUM(ads_clicks) AS total_ads_clicks,
                SUM(ads_conversions) AS total_ads_conversions,
                case when SUM(ads_conversions) > 0 then SUM(ads_cost) / SUM(ads_conversions) else 0 end AS avg_ads_cpa,
				case when  SUM(ads_clicks) > 0 then SUM(ads_cost) / SUM(ads_clicks) else 0  end AS avg_ads_cpc
            FROM fn_get_campaign_details_test($1,$2, $3, $4)
			%s 
        )
		INSERT INTO pg_temp.temp_ads_result
        SELECT
            %s  --select field
			 CASE WHEN $7 THEN ROUND(COALESCE(rv.total_rv_revenue / %s , 0),2) ELSE ROUND(COALESCE(rv.total_rv_revenue, 0),2) END AS rv_revenue,
			 CASE WHEN $7 THEN ROUND(COALESCE(rv.total_rv_rpc / %s , 0),2) ELSE ROUND(COALESCE(rv.total_rv_rpc, 0),2) END AS rv_rpc,
             CASE WHEN $7 THEN ROUND((COALESCE(rv.total_rv_revenue , 0) - COALESCE(ads.total_ads_cost, 0)) / %s , 2) ELSE ROUND((COALESCE(rv.total_rv_revenue , 0) - COALESCE(ads.total_ads_cost, 0)), 2) END AS profit,
			 
            CASE
                WHEN ads.total_ads_cost > 0 THEN
                    COALESCE(ROUND((rv.total_rv_revenue - ads.total_ads_cost) / ads.total_ads_cost * 100, 2),0)
                ELSE 0
            END AS roi,
			
			ROUND(COALESCE(ads.total_ads_conversions, 0),2) AS ads_conversions,
            COALESCE(rv.total_rv_clicks, 0) AS ads_click,
		 	CASE WHEN $7 THEN ROUND(COALESCE(ads.total_ads_cost / %s , 0), 2) ELSE  ROUND(COALESCE(ads.total_ads_cost, 0), 2) END AS ads_spend,
		 	CASE WHEN $7 THEN ROUND(COALESCE(ROUND(ads.avg_ads_cpa / %s, 2), 0), 2) ELSE ROUND(COALESCE(ROUND(ads.avg_ads_cpa, 2), 0), 2) END AS ads_cpa,
		 	CASE WHEN $7 THEN ROUND(COALESCE(ROUND(ads.avg_ads_cpc / %s, 2), 0), 2) ELSE ROUND(COALESCE(ROUND(ads.avg_ads_cpc, 2), 0), 2) END AS ads_cpc
			 ,total_ads_clicks as spend_clicks
        FROM rv_agg rv
        FULL OUTER JOIN ads_agg ads
            ON %s -- selectfieldforwherecls
        %s  --selectfieldforjoin
    $fmt$,
        selectfiledforagg, selectfiledforgroupby, selectfiledforagg, selectfiledforgroupby,
        selectefield,
		today_rate,today_rate,today_rate,today_rate,today_rate,today_rate,
        selectfieldforwherecls,
        selectfieldforjoin
    );

	--RETURN sql;
    -- Execute final SQL
    EXECUTE sql USING p_article_id, p_channel_id, p_from_date, p_to_date,p_group_mode,p_isnullable_data,p_inr_to_usd,p_style_id;
	END;
$BODY$;

