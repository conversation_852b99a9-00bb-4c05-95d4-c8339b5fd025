import React, { useEffect, useState } from 'react';
import { useSidebarContext } from "@/components/Layouts/sidebar/sidebar-context";
import apiClient from '@/utils/axiosInstance';

const Footer = () => {
  const { isMobile, isCollapsed } = useSidebarContext();
  const [data, setData] = useState(null);

  const fetchTokenExpiry = async () => {
    try {
      const response = await apiClient.get("/AdminSetting/RefreshTokenExpiry");
      const expiryData = response.data.Data;

      // if it's a string (expiry message)
      if (typeof expiryData === "string" && expiryData.trim() !== "") {
        setData(expiryData);
      }
      // if it's an empty array []
      else if (Array.isArray(expiryData) && expiryData.length === 0) {
        setData(`Welcome  • © ${new Date().getFullYear()} Admin Panel • v1.0.0`);
      }
      // otherwise nothing
      else {
        setData(null);
      }
    } catch (error) {
      console.error("Error fetching token expiry:", error);
      setData(null);
    }
  };

  useEffect(() => {
    fetchTokenExpiry();
  }, []);

  if (!data) return null; // don't render anything if no data at all

  return (
<footer
  className={`fixed bottom-0 left-0 text-xs ${
    isMobile
      ? "ml-0 w-full"
      : isCollapsed
      ? "ml-24 w-[calc(100vw-96px)]"
      : "ml-70 w-[calc(100vw-280px)]"
      } py-1 px-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50 backdrop-blur-sm border-t border-gray-100/50`}
    >
    <marquee behavior="scroll" direction="left" scrollamount="7" className="text-gray-600 font-medium">
        {data}
    </marquee>
</footer>
  );
};

export default Footer;