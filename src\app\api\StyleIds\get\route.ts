import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const searchRaw = searchParams.get("q");
        const search = searchRaw ? searchRaw.trim() : null;
        const orderBy = searchParams.get("orderBy") || "Name";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        let where: any = { IsDeleted: false };

        if (role !== 'Super Admin' && role !== 'Admin') {
            where.CreatedBy = userId;
        }

        if (search && search !== "") {
            where.OR = [
                { Name: { contains: search, mode: 'insensitive' } },
                { StyleId: { contains: search } }
            ];
        }

        const recordsTotal = await prisma.styleIds.count({
            where: { IsDeleted: false }
        });

        const recordsFiltered = await prisma.styleIds.count({ where });

        // Define allowed sortable fields
        const allowedFields = ['Name', 'StyleId', 'UserCount'] as const;
        type AllowedField = typeof allowedFields[number];

        // Validate and normalize orderBy
        const safeOrderBy: AllowedField = allowedFields.includes(orderBy as AllowedField)
            ? orderBy as AllowedField
            : 'Name';

        // Create orderBy clause
        let orderByClause: any;

        if (safeOrderBy === 'UserCount') {
            orderByClause = {
                StyleIdUserMappings: {
                    _count: orderDir
                }
            };
        } else {
            orderByClause = {
                [safeOrderBy]: orderDir
            };
        }

        const stylesWithUsers = await prisma.styleIds.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            select: {
                Id: true,
                Name: true,
                StyleId: true,
                _count: {
                    select: {
                        StyleIdUserMappings: {
                            where: {
                                AdminUser: {
                                    IsDeleted: false
                                }
                            }
                        }
                    }
                }
            }
        });

        // For true case-insensitive sorting, we would need to sort in memory:
        const transformedData = stylesWithUsers.map(style => ({
            Id: style.Id,
            Name: style.Name,
            StyleId: style.StyleId,
            UserCount: style._count.StyleIdUserMappings,
        }));

        // Apply case-insensitive sorting in memory if needed
        if (safeOrderBy !== 'UserCount' && safeOrderBy !== 'StyleId') {
            transformedData.sort((a, b) => {
                const aValue = String(a[safeOrderBy]).toLowerCase();
                const bValue = String(b[safeOrderBy]).toLowerCase();
                return orderDir === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });
        }

        const totalPages = length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1));

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages,
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    }
}