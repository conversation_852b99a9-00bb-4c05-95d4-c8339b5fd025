import axios from 'axios';
import dotenv from 'dotenv';
import { google } from 'googleapis';
import { PrismaClient } from '../../prisma/generated/client';

const prisma = new PrismaClient();

dotenv.config();

interface UserRequest {
    budgetName: string;
    budgetAmountMicros: string;
    campaignName: string;
    status?: string;
    customerId: string;
}

interface BudgetRequest {
    budgetName: string;
    budgetAmountMicros: string;
    customerId: string;
}

interface CampaignRequest {
    campaignName: string;
    customerId: string;
    status?: string;
}

interface BudgetResponse {
    results: {
        resourceName: string;
    }[];
}

interface CampaignResponse {
    results: {
        resourceName: string;
    }[];
}

interface CampaignDetails {
    campaign: {
        id: string;
        name: string;
        status: string;
        advertising_channel_type: string;
        resource_name: string;
        campaign_budget: string;
    };
    campaign_budget: {
        amount_micros: string;
        name: string;
        status: string;
        resource_name: string;
    };
}

interface BudgetOperation {
    create: {
        name: string;
        amount_micros: number;
        delivery_method: string;
        type: string;
    };
}

interface CampaignOperation {
    create: {
        name: string;
        advertising_channel_type: string;
        status: string;
        campaign_budget: string;
        manual_cpc: {
            enhanced_cpc_enabled: boolean;
        };
    };
}

interface MutateRequest {
    operations: BudgetOperation[] | CampaignOperation[];
}

export class GoogleAdsService {
    private authToken: string;
    private devToken: string;
    private loginCustomerId: string;
    private oauth2Client: any;

    constructor() {
        this.authToken = '';
        this.devToken = '';
        this.loginCustomerId = '';
    }

    /**
     * Initialize all required credentials from database
     */
    private async initializeAuth() {
        try {
            const userSettings = await prisma.adminUserSetting.findFirst({});
            if (!userSettings) {
                throw new Error('No admin user settings found in database');
            }

            if (!userSettings.DeveloperToken) {
                throw new Error('Developer token not found in database');
            }
            if (!userSettings.LoginCustomerId) {
                throw new Error('Login customer ID not found in database');
            }

            this.devToken = userSettings.DeveloperToken;
            this.loginCustomerId = userSettings.LoginCustomerId;

            if (userSettings.CampaignClientId && userSettings.CampaignClientSecret) {
                const redirectUrl = userSettings.CampaignRedirectUrl ?? undefined;
                this.oauth2Client = new google.auth.OAuth2(
                    userSettings.CampaignClientId,
                    userSettings.CampaignClientSecret,
                    redirectUrl
                );

                let tokens = null;
                if (userSettings.CampaignAccessToken && userSettings.CampaignRefreshToken) {
                    tokens = {
                        access_token: userSettings.CampaignAccessToken,
                        refresh_token: userSettings.CampaignRefreshToken,
                    };
                }

                if (!tokens) {
                    throw new Error('No access tokens found in database');
                }

                this.oauth2Client.setCredentials(tokens);
                this.authToken = `Bearer ${tokens.access_token}`;
            } else {
                throw new Error('Missing OAuth client credentials in database');
            }

        } catch (error) {
            console.error('Error initializing auth:', error);
            throw error;
        }
    }

    /**
     * Refresh the access token
     */
    private async refreshToken() {
        try {
            const userSettings = await prisma.adminUserSetting.findFirst({});
            if (!userSettings?.CampaignRefreshToken) {
                throw new Error('No refresh token found in database');
            }

            const { credentials } = await this.oauth2Client.refreshAccessToken();

            const newTokens = {
                access_token: credentials.access_token,
                refresh_token: credentials.refresh_token || userSettings.CampaignRefreshToken,
            };

            // Update tokens in database
            await prisma.adminUserSetting.updateMany({
                data: {
                    CampaignAccessToken: credentials.access_token,
                    CampaignRefreshToken: credentials.refresh_token || userSettings.CampaignRefreshToken,
                },
            });

            this.oauth2Client.setCredentials(newTokens);
            this.authToken = `Bearer ${newTokens.access_token}`;

        } catch (error) {
            console.error('Token refresh failed:', error);
            throw new Error('Failed to refresh access token');
        }
    }

    /**
     * Main method to create a campaign
     */
    async createCampaign(userRequest: UserRequest) {
        try {
            await this.initializeAuth();

            this.validateUserRequest(userRequest);

            const budgetResourceName = await this.createBudget(userRequest);
            // console.log('Created budget with resource name:', budgetResourceName);

            const { campaignId, campaignResourceName } = await this.createCampaignWithBudget(userRequest, budgetResourceName);
            // console.log('Created campaign with ID:', campaignId);
            // console.log('Created campaign with resource name:', campaignResourceName);

            const campaignDetails = {
                campaign: {
                    id: campaignId,
                    name: userRequest.campaignName,
                    status: userRequest.status || 'PAUSED',
                    advertising_channel_type: 'SEARCH',
                    resource_name: campaignResourceName,
                    campaign_budget: budgetResourceName
                },
                campaign_budget: {
                    resource_name: budgetResourceName,
                    name: userRequest.budgetName,
                    status: 'ENABLED',
                    amount_micros: userRequest.budgetAmountMicros
                }
            };

            return {
                success: true,
                campaignDetails: campaignDetails
            };
        } catch (error) {
            console.error('Error in createCampaign:', error);
            throw error;
        }
    }

    /**
     * Creates a campaign budget
     */
    private async createBudget({ budgetName, budgetAmountMicros, customerId }: BudgetRequest): Promise<string> {
        try {
            const budgetPayload: MutateRequest = {
                operations: [{
                    create: {
                        name: budgetName,
                        amount_micros: Number(budgetAmountMicros),
                        delivery_method: 'STANDARD',
                        type: 'STANDARD',
                    }
                }]
            };

            const budgetResponse = await axios.post<BudgetResponse>(
                `https://googleads.googleapis.com/v19/customers/${customerId}/campaignBudgets:mutate`,
                budgetPayload,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!budgetResponse.data?.results?.[0]?.resourceName) {
                throw new Error('Failed to create budget: No resource name returned');
            }

            return budgetResponse.data.results[0].resourceName;
        } catch (error: any) {
            if (error.response?.status === 401) {
                await this.refreshToken();
                return this.createBudget({ budgetName, budgetAmountMicros, customerId });
            }
            console.error('Error in createBudget:', error);
            throw error;
        }
    }

    /**
     * Creates a campaign with the specified budget
     */
    private async createCampaignWithBudget({ campaignName, customerId, status }: CampaignRequest, budgetResourceName: string): Promise<{ campaignId: string; campaignResourceName: string }> {
        try {
            const campaignPayload: MutateRequest = {
                operations: [{
                    create: {
                        name: campaignName,
                        advertising_channel_type: "SEARCH",
                        status: status || "PAUSED",
                        campaign_budget: budgetResourceName,
                        manual_cpc: {
                            enhanced_cpc_enabled: false,
                        },
                    }
                }]
            };

            const response = await axios.post<CampaignResponse>(
                `https://googleads.googleapis.com/v19/customers/${customerId}/campaigns:mutate`,
                campaignPayload,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!response.data?.results?.[0]?.resourceName) {
                throw new Error('Failed to create campaign: No resource name returned');
            }

            const campaignResourceName = response.data.results[0].resourceName;
            const campaignId = campaignResourceName.split('/')[3];

            return {
                campaignId: campaignId,
                campaignResourceName: campaignResourceName
            };
        } catch (error: any) {
            if (error.response?.status === 401) {
                await this.refreshToken();
                return this.createCampaignWithBudget({ campaignName, customerId, status }, budgetResourceName);
            }
            console.error('Error in createCampaignWithBudget:', error);
            throw error;
        }
    }

    /**
     * Fetches campaign details
     */
    async fetchCampaignDetails(customerId: string, campaignId: string): Promise<CampaignDetails> {
        try {
            const searchRequest = {
                query: `
                    SELECT 
                        campaign.id, 
                        campaign.name, 
                        campaign.status,
                        campaign.advertising_channel_type,
                        campaign.resource_name,
                        campaign.campaign_budget
                    FROM campaign 
                    WHERE campaign.id = ${campaignId}`
            };

            const response = await axios.post(
                `https://googleads.googleapis.com/v19/customers/${customerId}/googleAds:search`,
                searchRequest,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!response.data?.results?.[0]) {
                throw new Error(`No campaign found with ID: ${campaignId}`);
            }

            return response.data.results[0];
        } catch (error: any) {
            if (error.response?.status === 401) {
                await this.refreshToken();
                return this.fetchCampaignDetails(customerId, campaignId);
            }
            console.error('Error in fetchCampaignDetails:', error);
            throw error;
        }
    }

    /**
     * Fetch customer clients from Google Ads API
     */
    async getCustomerClients(): Promise<any[]> {
        try {
            await this.initializeAuth();

            const endpoint = `https://googleads.googleapis.com/v19/customers/${this.loginCustomerId}/googleAds:search`;
            const query = `
                SELECT 
                    customer.descriptive_name, 
                    customer_client.client_customer, 
                    customer_client.level, 
                    customer_client.manager, 
                    customer_client.descriptive_name, 
                    customer_client.currency_code, 
                    customer_client.time_zone,
                    customer_client.id 
                FROM customer_client
            `;

            const response = await axios.post(endpoint, { query }, {
                headers: this.getHeaders()
            });

            return response.data?.results || [];

        } catch (error: any) {
            if (error.response?.status === 401) {
                await this.refreshToken();
                return this.getCustomerClients();
            }

            console.error('Error fetching customer clients:', error.response?.data || error.message);
            throw new Error('Failed to fetch customer clients');
        }
    }

    /**
     * Returns the headers needed for Google Ads API requests
     */
    private getHeaders() {
        return {
            'Authorization': this.authToken,
            'developer-token': this.devToken,
            'Content-Type': 'application/json',
            'login-customer-id': this.loginCustomerId
        };
    }

    /**
     * Validates user request parameters
     */
    private validateUserRequest(request: UserRequest) {
        if (!request.customerId) {
            throw new Error('Customer ID is required');
        }
        if (!request.budgetName) {
            throw new Error('Budget name is required');
        }
        if (!request.budgetAmountMicros || isNaN(Number(request.budgetAmountMicros))) {
            throw new Error('Valid budget amount is required');
        }

        const budgetAmount = Number(request.budgetAmountMicros);
        // if (budgetAmount < 1000000) {
        //     throw new Error('Budget amount must be at least 1,000,000 micros ($1.00)');
        // }

        if (!request.campaignName) {
            throw new Error('Campaign name is required');
        }

        if (request.campaignName.length > 255) {
            throw new Error('Campaign name must be 255 characters or less');
        }
    }
}