import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';


export async function PATCH(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        // Check if user has admin permissions (optional, you can implement as needed)
        // if (user.User_Type !== 'admin') {
        //    return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
        // }

        const { Id, Block } = await req.json();

        if (!Id) {
            return NextResponse.json(
                { error: "User ID is required" },
                { status: 400 }
            );
        }

        if (Block === undefined || typeof Block !== 'boolean') {
            return NextResponse.json(
                { error: "Block must be a boolean value" },
                { status: 400 }
            );
        }

        // Check if the target user exists
        const existingUser = await prisma.adminUser.findUnique({
            where: {
                Id: Id
            }
        });

        if (!existingUser) {
            return NextResponse.json(
                { error: "User not found" },
                { status: 404 }
            );
        }

        // Check if user is already in the requested state
        if (existingUser.Block === Block) {
            const message = Block
                ? "User is already blocked"
                : "User is already unblocked";

            return NextResponse.json({
                message,
                currentStatus: existingUser.Block
            }, { status: 200 });
        }

        // Update user's blocked status
        const updatedUser = await prisma.adminUser.update({
            where: {
                Id: Id
            },
            data: {
                Block: Block
            },
            // select: {
            //     Id: true,
            //     Name: true,
            //     Email: true,
            //     Number: true,
            //     User_Type: true,
            //     Block: true
            // }
        });

        const message = Block
            ? "User has been blocked successfully"
            : "User has been unblocked successfully";

        return NextResponse.json({
            success: true,
            message,

        }, { status: 200 });
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } 
}