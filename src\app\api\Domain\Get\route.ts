import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const { searchParams } = new URL(req.url);
        const page = parseInt(searchParams.get("page") || "1");
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q") || '';
        const orderBy = searchParams.get("orderBy") || "Name";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        // Build base filter
        let where: any = {
            IsDeleted: false
        };

        // Role-based filtering
        if (role !== 'Super Admin' && role !== 'Admin') {
            where.CreatedBy = userId;
        }

        // Search filtering
        if (search) {
            where.OR = [
                { Name: { contains: search, mode: 'insensitive' } },
                { Prefix: { contains: search, mode: 'insensitive' } },
            ];
        }

        // Validate allowed orderBy fields
        const allowedFields = ['Name', 'Prefix', 'ShowUrlName', 'ChannelId'] as const;
        type AllowedField = typeof allowedFields[number];

        const safeOrderBy: AllowedField = allowedFields.includes(orderBy as AllowedField)
            ? orderBy as AllowedField
            : 'Name';

        // Create type-safe orderBy clause
        const orderByClause = {
            [safeOrderBy]: orderDir
        };

        // Fetch domains with proper sorting
        const DomainWithUsers = await prisma.domain.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            select: {
                Id: true,
                Name: true,
                Prefix: true,
                ShowUrlName: true,
            }
        });

        const recordsFiltered = await prisma.domain.count({ where });

        return NextResponse.json({
            success: true,
            data: DomainWithUsers,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                currentPageCount: DomainWithUsers.length,
                start,
                length,
                currentPage: page,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: process.env.NODE_ENV === 'development'
                    ? error instanceof Error ? error.message : String(error)
                    : undefined
            },
            { status: 500 }
        );
    }
}