// import { prisma } from '../../../../lib/prisma';
// import { NextRequest, NextResponse } from 'next/server';
// import { verifyToken } from '../../../../lib/varifyToken';
// import { google } from "googleapis";
// import {
//     generateReport,
//     getAdClients,
//     getAdSenseAccounts,
// } from '@/utils/functions';

// export async function POST(req: NextRequest) {
//     try {
//         // Verify user authentication
//         const user = await verifyToken(req);
//         if (!user?.Id) {
//             return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
//         }

//         // Parse query parameters
//         const { searchParams } = new URL(req.url);
//         const code = searchParams.get("code");
//         const startDateParams = searchParams.get("startDate");
//         const endDateParams = searchParams.get("endDate");
//         const breakPoints = searchParams.get("breakPoints") || "{}";
//         const toggle = searchParams.get("toggle") || "false";

//         // Parse request body
//         const body = await req.json();
//         let selectedChannels = body.selectedChannels || null;
//         let selectedStyles = body.selectedStyles || null;

//         // Get OAuth credentials from database
//         const userSettings = await prisma.adminUserSetting.findFirst();
//         if (!userSettings?.RevenueClientId || !userSettings?.RevenueClientSecret) {
//             return NextResponse.json(
//                 { message: 'OAuth credentials not found in database' },
//                 { status: 404 }
//             );
//         }

//         // Initialize OAuth2 client
//         const oauth2Client = new google.auth.OAuth2(
//             userSettings.RevenueClientId,
//             userSettings.RevenueClientSecret,
//             userSettings.RevenueRedirectUrl
//         );

//         // Generate authorization URL
//         const authUrl = oauth2Client.generateAuthUrl({
//             access_type: 'offline',
//             scope: [
//                 'https://www.googleapis.com/auth/adsense.readonly',
//                 'https://www.googleapis.com/auth/adsense',
//             ],
//             prompt: 'consent',
//             include_granted_scopes: true,
//         });

//         // Check for existing tokens in database
//         let tokens = null;
//         if (userSettings.RevenueAccessToken && userSettings.RevenueRefreshToken) {
//             tokens = {
//                 access_token: userSettings.RevenueAccessToken,
//                 refresh_token: userSettings.RevenueRefreshToken,
//             };
//         }

//         // Handle initial OAuth flow
//         if (!code && !tokens) {
//             return NextResponse.redirect(authUrl);
//         }

//         // Exchange authorization code for tokens
//         if (code) {
//             try {
//                 const { tokens: newTokens } = await oauth2Client.getToken(code);
//                 oauth2Client.setCredentials(newTokens);
//                 tokens = newTokens;

//                 // Store tokens in database
//                 await prisma.adminUserSetting.updateMany({
//                     data: {
//                         RevenueAccessToken: newTokens.access_token,
//                         RevenueRefreshToken: newTokens.refresh_token,
//                     },
//                 });

//                 return NextResponse.json(
//                     { message: 'Authorization successful', tokens: newTokens },
//                     { status: 200 }
//                 );
//             } catch (error) {
//                 const errorMessage = error instanceof Error ? error.message : 'Unknown error during token exchange';
//                 return NextResponse.json(
//                     { error: 'Failed to exchange authorization code for tokens', details: errorMessage },
//                     { status: 500 }
//                 );
//             }
//         }

//         if (tokens) {
//             oauth2Client.setCredentials(tokens);

//             // Verify token validity
//             try {
//                 const adsense = google.adsense({ version: 'v2', auth: oauth2Client });
//                 await adsense.accounts.list({ pageSize: 1 });
//             } catch (error) {
//                 const isAuthError =
//                     error instanceof Error &&
//                     (error.message.includes('invalid_token') ||
//                         (typeof (error as any).code === 'number' && (error as any).code === 401));

//                 if (isAuthError) {
//                     try {
//                         // Refresh expired tokens
//                         const { credentials } = await oauth2Client.refreshAccessToken();
//                         tokens = {
//                             access_token: credentials.access_token,
//                             refresh_token: credentials.refresh_token || userSettings.RevenueRefreshToken,
//                             expiry_date: credentials.expiry_date
//                         };

//                         // Update tokens in database
//                         await prisma.adminUserSetting.updateMany({
//                             data: {
//                                 RevenueAccessToken: credentials.access_token,
//                                 RevenueRefreshToken: credentials.refresh_token || userSettings.RevenueRefreshToken,
//                             },
//                         });

//                         oauth2Client.setCredentials(tokens);
//                     } catch (refreshError) {
//                         return NextResponse.redirect(authUrl);
//                     }
//                 } else {
//                     throw error;
//                 }
//             }
//         }

//         // Get AdSense accounts
//         const accounts = await getAdSenseAccounts(oauth2Client, google);
//         if (!accounts || accounts.length === 0) {
//             return NextResponse.json({ message: "No AdSense accounts found" }, { status: 404 });
//         }

//         // Verify required account IDs are set
//         if (!userSettings?.AdsAccountId || !userSettings?.AdsClientId) {
//             return NextResponse.json({ message: "AdSense account or client ID not found" }, { status: 404 });
//         }

//         // Check user permissions
//         const finduser = await prisma.adminUser.findUnique({
//             where: { Id: user.Id.toString() }
//         });

//         if (!finduser) {
//             return NextResponse.json({ error: "User not found." }, { status: 404 });
//         }

//         // Process report if dates are provided
//         if (startDateParams && endDateParams) {
//             const finalStartDate = {
//                 day: startDateParams.split("-")[2],
//                 month: startDateParams.split("-")[1],
//                 year: startDateParams.split("-")[0],
//             };
//             const finalEndDate = {
//                 day: endDateParams.split("-")[2],
//                 month: endDateParams.split("-")[1],
//                 year: endDateParams.split("-")[0],
//             };

//             const metrics = [
//                 "ESTIMATED_EARNINGS",
//                 "IMPRESSIONS",
//                 "IMPRESSIONS_RPM",
//                 "CLICKS",
//                 "IMPRESSIONS_CTR",
//                 "COST_PER_CLICK",
//             ];

//             // Check user has required permissions
//             if (["Admin", "Super Admin", "Account", "Partner"].includes(finduser.User_Type || "")) {
//                 let allReportsData: any[] = [];

//                 // Helper functions to fetch channel/style IDs
//                 const fetchChannelsRecord = async (Id: string) => {
//                     const record = await prisma.channals.findUnique({ where: { Id: Id } });
//                     return userSettings?.PubId
//                         ? `partner-pub-${userSettings.PubId}:${record?.ReportingDimensionId?.split(":")[1]}`
//                         : record?.ReportingDimensionId;
//                 };

//                 const fetchStyleDimensionId = async (Id: string) => {
//                     const record = await prisma.styleIds.findUnique({ where: { Id: Id } });
//                     return record?.StyleId;
//                 };

//                 // Auto-fetch channels if not provided
//                 if (!selectedChannels || selectedChannels.length === 0) {
//                     const allChannels = await prisma.channals.findMany({
//                         select: { Id: true }
//                     });
//                     selectedChannels = allChannels.map(channel => channel.Id);
//                 }

//                 // Auto-fetch styles if not provided
//                 if (!selectedStyles || selectedStyles.length === 0) {
//                     const allStyles = await prisma.styleIds.findMany({
//                         select: { Id: true }
//                     });
//                     selectedStyles = allStyles.map(style => style.Id);
//                 }

//                 // Process Channels
//                 if (selectedChannels.length > 0) {
//                     const channelDimensions = await Promise.all(
//                         selectedChannels.map(fetchChannelsRecord)
//                     );
//                     const validChannels = channelDimensions.filter(Boolean);

//                     if (validChannels.length > 0) {
//                         const channelReport = await generateReport(
//                             oauth2Client,
//                             userSettings.AdsAccountId,
//                             google,
//                             finalStartDate,
//                             finalEndDate,
//                             metrics,
//                             JSON.parse(breakPoints),
//                             validChannels,
//                             toggle
//                         );

//                         if (typeof channelReport === 'string') {
//                             const [headerRow, ...rows] = channelReport.split('\n');
//                             const headers = headerRow.split(',');

//                             for (const row of rows) {
//                                 if (!row.trim()) continue;

//                                 const values = row.split(',');
//                                 const rowData: any = {};

//                                 headers.forEach((header, index) => {
//                                     rowData[header] = values[index]?.trim() || null;
//                                 });

//                                 allReportsData.push(rowData);
//                             }
//                         } else if (Array.isArray(channelReport)) {
//                             allReportsData = allReportsData.concat(channelReport);
//                         }
//                     }
//                 }

//                 // Process Styles
//                 if (selectedStyles.length > 0) {
//                     const styleDimensions = await Promise.all(
//                         selectedStyles.map(fetchStyleDimensionId)
//                     );
//                     const validStyles = styleDimensions.filter(Boolean);

//                     if (validStyles.length > 0) {
//                         const styleReport = await generateReport(
//                             oauth2Client,
//                             userSettings.AdsAccountId,
//                             google,
//                             finalStartDate,
//                             finalEndDate,
//                             metrics,
//                             JSON.parse(breakPoints),
//                             validStyles,
//                             toggle
//                         );

//                         if (typeof styleReport === 'string') {
//                             const [headerRow, ...rows] = styleReport.split('\n');
//                             const headers = headerRow.split(',');

//                             for (const row of rows) {
//                                 if (!row.trim()) continue;

//                                 const values = row.split(',');
//                                 const rowData: any = {};

//                                 headers.forEach((header, index) => {
//                                     rowData[header] = values[index]?.trim() || null;
//                                 });

//                                 allReportsData.push(rowData);
//                             }
//                         } else if (Array.isArray(styleReport)) {
//                             allReportsData = allReportsData.concat(styleReport);
//                         }
//                     }
//                 }

//                 // Process data for database storage with FIXED FIELD NAMES
//                 if (allReportsData.length > 0) {
//                     const batchUpserts = [];

//                     for (const row of allReportsData) {
//                         const processValue = (value: string) => value?.trim() || "";

//                         const processNumericValue = (value: string) => {
//                             const val = processValue(value);
//                             if (!val) return null;

//                             // Handle currency format (INR or USD)
//                             const cleanedValue = val
//                                 .replace(/₹/g, '') // Remove rupee symbol
//                                 .replace(/\$/g, '') // Remove dollar symbol
//                                 .replace(/INR/g, '') // Remove INR text
//                                 .replace(/USD/g, '') // Remove USD text
//                                 .replace(/,/g, '') // Remove commas
//                                 .replace(/\s+/g, '') // Remove spaces
//                                 .trim();

//                             const numericValue = parseFloat(cleanedValue);
//                             return isNaN(numericValue) ? null : numericValue;
//                         };

//                         // Use UPPERCASE field names to match API response
//                         const formattedDate = row.DATE ? new Date(row.DATE) : null;
//                         if (!formattedDate) {
//                             // console.log('Skipping row due to invalid date:', row.DATE);
//                             continue;
//                         }

//                         const finalChannelId = processValue(row.CUSTOM_CHANNEL_ID);
//                         const finalStyleId = processValue(row.CUSTOM_SEARCH_STYLE_ID);

//                         // Debug logging
//                         // console.log('Processing row:', {
//                         //     rawEarnings: row.ESTIMATED_EARNINGS,
//                         //     processedEarnings: processNumericValue(row.ESTIMATED_EARNINGS),
//                         //     date: row.DATE,
//                         //     channelId: finalChannelId,
//                         //     styleId: finalStyleId,
//                         //     toggle: toggle
//                         // });

//                         const uniqueIdentifier = {
//                             Date: formattedDate,
//                             StyleId: finalStyleId,
//                             ChannalId: finalChannelId,
//                             Country: processValue(row.COUNTRY_CODE), 
//                             PlatfromType: processValue(row.PLATFORM_TYPE_NAME)
//                         };

//                         const revenueData = {
//                             EstimatedEarnings: processNumericValue(row.ESTIMATED_EARNINGS),
//                             Impressions: processNumericValue(row.IMPRESSIONS),
//                             ImpressionsRpm: processNumericValue(row.IMPRESSIONS_RPM),
//                             Clicks: processNumericValue(row.CLICKS),
//                             ImpressionsCtr: processNumericValue(row.IMPRESSIONS_CTR),
//                             CostPerClick: processNumericValue(row.COST_PER_CLICK)
//                         };

//                         // Debug the processed values
//                         // console.log('Processed revenue data:', revenueData);

//                         // Skip if all numeric values are null
//                         if (Object.values(revenueData).every(val => val === null)) {
//                             // console.log('Skipping row - all numeric values are null:', row);
//                             continue;
//                         }

//                         batchUpserts.push(
//                             prisma.revenue.upsert({
//                                 where: {
//                                     Date_StyleId_ChannalId_Country_PlatfromType: uniqueIdentifier
//                                 },
//                                 create: {
//                                     ...uniqueIdentifier,
//                                     ...revenueData
//                                 },
//                                 update: revenueData
//                             })
//                         );
//                     }

//                     // Execute batch in chunks with better error handling
//                     const chunkSize = 100;
//                     for (let i = 0; i < batchUpserts.length; i += chunkSize) {
//                         const chunk = batchUpserts.slice(i, i + chunkSize);
//                         try {
//                             await prisma.$transaction(chunk);
//                             // console.log(`Successfully processed chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(batchUpserts.length / chunkSize)}`);
//                         } catch (error) {
//                             console.error(`Error upserting revenue records (chunk ${Math.floor(i / chunkSize) + 1}):`, error);
//                             // Log the problematic data
//                             console.error('Problematic chunk data:', JSON.stringify(chunk.slice(0, 1), null, 2));
//                         }
//                     }

//                     // console.log(`Total records processed: ${batchUpserts.length}`);
//                 }

//                 // Return JSON response
//                 return NextResponse.json({
//                     data: allReportsData,
//                     message: `Successfully processed ${allReportsData.length} records`
//                 }, { status: 200 });
//             }
//         }

//         return NextResponse.json({ message: "Missing date range or unauthorized role" }, { status: 400 });

//     } catch (error) {
//         console.error("Adsense POST Error:", error);
//         return NextResponse.json({
//             error: "Internal server error",
//             details: error instanceof Error ? error.message : String(error)
//         }, { status: 500 });
//     } finally {
//         await prisma.$disconnect();
//     }
// }



import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { google } from "googleapis";
import {
    generateReport,
    getAdClients,
    getAdSenseAccounts,
} from '@/utils/functions';

// Helper function to deduplicate report data
function deduplicateReportData(data: any[]): any[] {
    const seen = new Set();
    return data.filter(item => {
        // Create a unique key for each record based on relevant fields
        const key = [
            item.DATE,
            item.COUNTRY_CODE || '',
            item.PLATFORM_TYPE_NAME || '',
            item.CUSTOM_CHANNEL_ID || '',
            item.CUSTOM_SEARCH_STYLE_ID || '',
        ].join('|');

        if (!seen.has(key)) {
            seen.add(key);
            return true;
        }
        return false;
    });
}

export async function POST(req: NextRequest) {
    try {
        // Verify user authentication
        // const user = await verifyToken(req);
        // if (!user?.Id) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }

        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        // Parse query parameters
        const { searchParams } = new URL(req.url);
        const code = searchParams.get("code");
        const breakPoints = searchParams.get("breakPoints") || "{}";
        const toggle = searchParams.get("toggle") || "false";

        // Get today's date in YYYY-MM-DD format
        const today = new Date();
        const todayFormatted = today.toISOString().split('T')[0];

        // console.log(`Fetching AdSense data for today: ${todayFormatted}`);

        // Parse request body
        const body = await req.json();
        let selectedChannels = body.selectedChannels || null;
        let selectedStyles = body.selectedStyles || null;

        // Get OAuth credentials from database
        const userSettings = await prisma.adminUserSetting.findFirst();
        if (!userSettings?.RevenueClientId || !userSettings?.RevenueClientSecret) {
            return NextResponse.json(
                { message: 'OAuth credentials not found in database' },
                { status: 404 }
            );
        }

        // Initialize OAuth2 client
        const oauth2Client = new google.auth.OAuth2(
            userSettings.RevenueClientId,
            userSettings.RevenueClientSecret,
            userSettings.RevenueRedirectUrl as string
        );

        // Handle OAuth flow if needed
        if (code) {
            try {
                const { tokens } = await oauth2Client.getToken(code);
                oauth2Client.setCredentials(tokens);
                await prisma.adminUserSetting.updateMany({
                    data: {
                        RevenueAccessToken: tokens.access_token,
                        RevenueRefreshToken: tokens.refresh_token,
                    },
                });
                return NextResponse.json(
                    { message: 'Authorization successful' },
                    { status: 200 }
                );
            } catch (error) {
                return NextResponse.json(
                    { error: 'Failed to exchange authorization code' },
                    { status: 500 }
                );
            }
        }

        // Set credentials from database
        if (userSettings.RevenueAccessToken && userSettings.RevenueRefreshToken) {
            oauth2Client.setCredentials({
                access_token: userSettings.RevenueAccessToken,
                refresh_token: userSettings.RevenueRefreshToken,
            });
        } else {
            return NextResponse.redirect(
                oauth2Client.generateAuthUrl({
                    access_type: 'offline',
                    scope: [
                        'https://www.googleapis.com/auth/adsense.readonly',
                        'https://www.googleapis.com/auth/adsense',
                    ],
                    prompt: 'consent',
                })
            );
        }

        // Verify token validity
        try {
            const adsense = google.adsense({ version: 'v2', auth: oauth2Client });
            await adsense.accounts.list({ pageSize: 1 });
        } catch (error) {
            if (error instanceof Error && error.message.includes('invalid_token')) {
                try {
                    const { credentials } = await oauth2Client.refreshAccessToken();
                    await prisma.adminUserSetting.updateMany({
                        data: {
                            RevenueAccessToken: credentials.access_token,
                            RevenueRefreshToken: credentials.refresh_token || userSettings.RevenueRefreshToken,
                        },
                    });
                    oauth2Client.setCredentials(credentials);
                } catch (refreshError) {
                    return NextResponse.redirect(
                        oauth2Client.generateAuthUrl({
                            access_type: 'offline',
                            scope: [
                                'https://www.googleapis.com/auth/adsense.readonly',
                                'https://www.googleapis.com/auth/adsense',
                            ],
                            prompt: 'consent',
                        })
                    );
                }
            } else {
                throw error;
            }
        }

        // Get AdSense accounts
        const accounts = await getAdSenseAccounts(oauth2Client, google);
        if (!accounts || accounts.length === 0) {
            return NextResponse.json({ message: "No AdSense accounts found" }, { status: 404 });
        }

        // Verify required account IDs
        if (!userSettings?.AdsAccountId || !userSettings?.AdsClientId) {
            return NextResponse.json({ message: "AdSense account or client ID not found" }, { status: 404 });
        }

        // Check user permissions
        const finduser = await prisma.adminUser.findUnique({
            where: { Id: userId.toString() }
        });

        if (!finduser) {
            return NextResponse.json({ error: "User not found." }, { status: 404 });
        }

        if (!["Admin", "Super Admin", "Account", "Partner"].includes(finduser.User_Type || "")) {
            return NextResponse.json({ message: "Unauthorized role" }, { status: 403 });
        }

        // Prepare date parameters
        const finalStartDate = {
            day: todayFormatted.split("-")[2],
            month: todayFormatted.split("-")[1],
            year: todayFormatted.split("-")[0],
        };
        const finalEndDate = { ...finalStartDate };

        const metrics = [
            "ESTIMATED_EARNINGS",
            "IMPRESSIONS",
            "IMPRESSIONS_RPM",
            "CLICKS",
            "IMPRESSIONS_CTR",
            "COST_PER_CLICK",
        ];

        let allReportsData: any[] = [];

        // Helper functions to fetch channel/style IDs
        const fetchChannelsRecord = async (Id: string) => {
            const record = await prisma.channals.findUnique({ where: { Id: Id } });
            return userSettings?.PubId
                ? `partner-pub-${userSettings.PubId}:${record?.ReportingDimensionId?.split(":")[1]}`
                : record?.ReportingDimensionId;
        };

        const fetchStyleDimensionId = async (Id: string) => {
            const record = await prisma.styleIds.findUnique({ where: { Id: Id } });
            return record?.StyleId;
        };

        // Auto-fetch channels if not provided
        if (!selectedChannels || selectedChannels.length === 0) {
            const allChannels = await prisma.channals.findMany({
                select: { Id: true }
            });
            selectedChannels = allChannels.map(channel => channel.Id);
        }

        // Auto-fetch styles if not provided
        if (!selectedStyles || selectedStyles.length === 0) {
            const allStyles = await prisma.styleIds.findMany({
                select: { Id: true }
            });
            selectedStyles = allStyles.map(style => style.Id);
        }

        // Process Channels
        if (selectedChannels.length > 0) {
            const channelDimensions = await Promise.all(
                selectedChannels.map(fetchChannelsRecord)
            );
            const validChannels = channelDimensions.filter(Boolean);

            if (validChannels.length > 0) {
                const channelReport = await generateReport(
                    oauth2Client,
                    userSettings.AdsAccountId,
                    google,
                    finalStartDate,
                    finalEndDate,
                    metrics,
                    JSON.parse(breakPoints),
                    validChannels,
                    toggle
                );

                if (typeof channelReport === 'string') {
                    const [headerRow, ...rows] = channelReport.split('\n');
                    const headers = headerRow.split(',');

                    for (const row of rows) {
                        if (!row.trim()) continue;

                        const values = row.split(',');
                        const rowData: any = {};

                        headers.forEach((header, index) => {
                            rowData[header] = values[index]?.trim() || null;
                        });

                        allReportsData.push(rowData);
                    }
                } else if (Array.isArray(channelReport)) {
                    allReportsData = allReportsData.concat(channelReport);
                }
            }
        }

        // Process Styles
        if (selectedStyles.length > 0) {
            const styleDimensions = await Promise.all(
                selectedStyles.map(fetchStyleDimensionId)
            );
            const validStyles = styleDimensions.filter(Boolean);

            if (validStyles.length > 0) {
                const styleReport = await generateReport(
                    oauth2Client,
                    userSettings.AdsAccountId,
                    google,
                    finalStartDate,
                    finalEndDate,
                    metrics,
                    JSON.parse(breakPoints),
                    validStyles,
                    toggle
                );

                if (typeof styleReport === 'string') {
                    const [headerRow, ...rows] = styleReport.split('\n');
                    const headers = headerRow.split(',');

                    for (const row of rows) {
                        if (!row.trim()) continue;

                        const values = row.split(',');
                        const rowData: any = {};

                        headers.forEach((header, index) => {
                            rowData[header] = values[index]?.trim() || null;
                        });

                        allReportsData.push(rowData);
                    }
                } else if (Array.isArray(styleReport)) {
                    allReportsData = allReportsData.concat(styleReport);
                }
            }
        }

        // Deduplicate and filter the data
        const processedData = deduplicateReportData(
            allReportsData.filter(row => {
                return row.DATE && (
                    row.ESTIMATED_EARNINGS ||
                    row.IMPRESSIONS ||
                    row.CLICKS ||
                    row.IMPRESSIONS_RPM ||
                    row.IMPRESSIONS_CTR ||
                    row.COST_PER_CLICK
                );
            })
        );

        // Calculate summary from raw data
        const calculateNumericValue = (value: string) => {
            if (!value) return 0;
            const cleanedValue = value
                .replace(/₹/g, '')
                .replace(/\$/g, '')
                .replace(/INR/g, '')
                .replace(/USD/g, '')
                .replace(/,/g, '')
                .replace(/\s+/g, '')
                .trim();
            const numericValue = parseFloat(cleanedValue);
            return isNaN(numericValue) ? 0 : numericValue;
        };

        // Return JSON response with deduplicated data
        return NextResponse.json({
            data: processedData
        }, { status: 200 });

    } catch (error) {
        console.error("Adsense POST Error:", error);
        return NextResponse.json({
            error: "Internal server error",
            details: error instanceof Error ? error.message : String(error)
        }, { status: 500 });
    }
}