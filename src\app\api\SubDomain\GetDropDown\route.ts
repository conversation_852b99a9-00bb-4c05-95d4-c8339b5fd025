import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // const user = await verifyToken(req);
        // if (!user) {
        //     return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;
        const email = req.headers.get('x-user-email');
        const name = req.headers.get('x-user-name');

        const { searchParams } = new URL(req.url);
        const domainId = searchParams.get("DomainId");

        // Build the base where condition
        let where: any = {
            IsDeleted: false,
        };

        // If DomainId is provided and not empty, add it to the filter
        if (domainId && domainId.trim() !== '') {
            where.Domain = domainId;
        }

        if (role === 'Super Admin' || role === 'Admin') {
            const subdomainlist = await prisma.subDomain.findMany({
                where,
                orderBy: {
                    Name: 'desc'
                },
                select: {
                    Id: true,
                    Name: true,
                    Url: true
                }
            });

            return NextResponse.json({
                success: true,
                data: subdomainlist,
            });
        }

        // For regular users: filter by UserId + optional Domain
        const subdomainlist = await prisma.subDomain.findMany({
            where: {
                ...where,
                SubDomainUserMappings: {
                    some: {
                        UserId: userId,
                    }
                }
            },
            orderBy: {
                CreatedAt: 'desc'
            },
            select: {
                Id: true,
                Name: true,
                Url: true
            }
        });

        return NextResponse.json({
            success: true,
            data: subdomainlist,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    }
}
