import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
    try {
        const role = req.headers.get('x-user-role') as string;
        const userId = req.headers.get('x-user-id') as string;

        const today = new Date();
        const startDate = today;
        const endDate = today;

        let processedStyleId: string | null = null;
        let allowedArticleIds: string[] = [];

        if (role === 'Partner' || role === 'Account') {
            const styleMapping = await prisma.styleIdUserMappings.findFirst({
                where: { UserId: userId },
                select: { StyleId: true }
            });

            if (styleMapping?.StyleId) {
                processedStyleId = styleMapping.StyleId;

                const articleDetails = await prisma.articleDetails.findMany({
                    where: {
                        OR: [
                            { StyleIdLm: processedStyleId },
                            { StyleIdDm: processedStyleId }
                        ],
                        IsDeleted: false,
                        User_Id_Settings: userId
                    },
                    select: { Id: true }
                });

                allowedArticleIds = articleDetails.map(article => article.Id.toString());
            }
        }

        const processedArticleId = allowedArticleIds.length > 0
            ? allowedArticleIds.join(',')
            : '';

        // Execute database function for ROI
        await prisma.$executeRawUnsafe(
            `SELECT fn_ads_revenue_data_test_styleid(
                $1::text, $2::text, $3::date, $4::date, $5::text, $6::boolean, $7::boolean, $8::text
            )`,
            processedArticleId,
            '',
            startDate,
            endDate,
            '',
            true,
            false,
            processedStyleId || ''
        );

        const selectFields = `
            CASE
                WHEN COALESCE(SUM("ads_spend"::float), 0) = 0 THEN 0
                ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - COALESCE(SUM("ads_spend"::float), 0)) 
                    / COALESCE(SUM("ads_spend"::float), 0)) * 100
            END AS "roi",
            CASE
                WHEN (COALESCE(SUM("ads_spend"::float), 0) * 1.18) = 0 THEN 0
                ELSE ((COALESCE(SUM("rv_revenue"::float), 0) - (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) 
                    / (COALESCE(SUM("ads_spend"::float), 0) * 1.18)) * 100
            END AS "roi_gst"
        `;

        const roiData = await prisma.$queryRawUnsafe<{ roi: number; roi_gst: number }[]>(`
            SELECT ${selectFields}
            FROM pg_temp.temp_ads_result
            LIMIT 1
        `);

        const roiResult = roiData[0] || { roi: 0, roi_gst: 0 };

        // --- Get dashboard counts ---
        let subDomainIds: string[] = [];
        let domainCount: number;

        if (role === 'Super Admin' || role === 'Admin') {
            const allSubDomains = await prisma.subDomain.findMany({
                where: { IsDeleted: false },
                distinct: ['Domain'],
                select: { Domain: true },
            });
            domainCount = allSubDomains.length;
        } else {
            const userWiseSubDomain = await prisma.subDomainUserMappings.findMany({
                where: { UserId: userId },
                select: { SubDomainId: true },
            });

            subDomainIds = userWiseSubDomain
                .map(mapping => mapping.SubDomainId)
                .filter((id): id is string => id !== null);

            const domain = await prisma.subDomain.findMany({
                where: {
                    Id: { in: subDomainIds },
                    IsDeleted: false,
                },
                distinct: ['Domain'],
                select: { Domain: true },
            });
            domainCount = domain.length;
        }

        let articleCount: number;
        if (role === 'Partner' || role === 'Account') {
            articleCount = await prisma.articleDetails.count({
                where: { User_Id_Settings: userId, IsDeleted: false },
            });
        } else {
            articleCount = await prisma.articleDetails.count({
                where: { IsDeleted: false },
            });
        }

        let userCount: number | undefined;
        if (role === 'Super Admin' || role === 'Admin') {
            userCount = await prisma.adminUser.count({
                where: { IsDeleted: false },
            });
        }

        // Return flattened response
        return NextResponse.json({
            success: true,
            data: {
                domainCount,
                articleCount,
                ...(userCount !== undefined && { userCount }),
                roi: roiResult.roi,
                roi_gst: roiResult.roi_gst
            }
        });

    } catch (error) {
        console.error('Error fetching data:', error);
        return NextResponse.json(
            {
                error: 'Failed to fetch data',
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    }
}

